import React, { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider, useAuth } from '@/store/auth-context';
import { 
  LoginPage, 
  AuthCallback, 
  AuthTestPage 
} from '@/pages/auth';
import { 
  DashboardPage, 
  SystemSettingsPage, 
  SimpleHealthPage 
} from '@/pages/system';
import { 
  ProjectManagementPage, 
  ProjectDetailPage, 
  ProjectTaskDetailPage 
} from '@/pages/project';
import { 
  PassportRecognitionPage, 
  TrainBookingPage, 
  TaskDetailPage, 
  TaskOrderDetailPage 
} from '@/pages/train_order';
import HotelBookingPage from '@/pages/hotel_order/HotelBookingPage';
import HotelTaskDetailPage from '@/pages/hotel_order/HotelTaskDetailPage';
import { Toaster } from '@/components/ui/toaster';
import { initGlobalAuthHandler } from '@/utils/global-auth-handler';

// 受保护的路由组件
const ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isAuthenticated, isLoading } = useAuth();
  
  if (isLoading) {
    return <div className="flex items-center justify-center min-h-screen"></div>;
  }
  
  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }
  
  return <>{children}</>;
};

// 公共路由组件（已登录用户将被重定向到仪表盘）
const PublicRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isAuthenticated, isLoading } = useAuth();
  
  if (isLoading) {
    return <div className="flex items-center justify-center min-h-screen"></div>;
  }
  
  if (isAuthenticated) {
    return <Navigate to="/dashboard" replace />;
  }
  
  return <>{children}</>;
};

const AppRoutes: React.FC = () => {
  return (
    <Routes>
      <Route path="/login" element={
        <PublicRoute>
          <LoginPage />
        </PublicRoute>
      } />
      <Route path="/dashboard" element={
        <ProtectedRoute>
          <DashboardPage />
        </ProtectedRoute>
      } />
      <Route path="/passport-recognition" element={
        <ProtectedRoute>
          <PassportRecognitionPage />
        </ProtectedRoute>
      } />
      <Route path="/projects" element={
        <ProtectedRoute>
          <ProjectManagementPage />
        </ProtectedRoute>
      } />
      <Route path="/project-detail/:projectId" element={
        <ProtectedRoute>
          <ProjectDetailPage />
        </ProtectedRoute>
      } />
      <Route path="/project-task-detail/:projectId" element={
        <ProtectedRoute>
          <ProjectTaskDetailPage />
        </ProtectedRoute>
      } />
      <Route path="/task-detail/:taskId" element={
        <ProtectedRoute>
          <TaskDetailPage />
        </ProtectedRoute>
      } />
      <Route path="/task-orders/:taskId" element={
        <ProtectedRoute>
          <TaskOrderDetailPage />
        </ProtectedRoute>
      } />
      <Route path="/train-booking/:projectId" element={
        <ProtectedRoute>
          <TrainBookingPage />
        </ProtectedRoute>
      } />
      <Route path="/hotel-booking/:projectId" element={
        <ProtectedRoute>
          <HotelBookingPage />
        </ProtectedRoute>
      } />
      <Route path="/hotel-task-detail/:taskId" element={
        <ProtectedRoute>
          <HotelTaskDetailPage />
        </ProtectedRoute>
      } />
      <Route path="/settings" element={
        <ProtectedRoute>
          <SystemSettingsPage />
        </ProtectedRoute>
      } />
      {/* 健康检查页面 - 公开访问，无需登录 */}
      <Route path="/healthCheck" element={<SimpleHealthPage />} />
      {/* 自动认证功能测试页面 */}
      <Route path="/auth-test" element={<AuthTestPage />} />
      {/* 添加授权回调路由 */}
      <Route path="/auth/callback" element={<AuthCallback />} />
      <Route path="/" element={<Navigate to="/dashboard" replace />} />
      <Route path="*" element={<Navigate to="/dashboard" replace />} />
    </Routes>
  );
};

const App: React.FC = () => {
  // 初始化全局认证错误处理器
  useEffect(() => {
    console.log('🚀 [App] 初始化全局认证错误处理器...');
    initGlobalAuthHandler();
    console.log('✅ [App] 全局认证错误处理器初始化完成');
  }, []);

  return (
    <Router>
      <AuthProvider>
        <AppRoutes />
        <Toaster />
      </AuthProvider>
    </Router>
  );
};

export default App;
