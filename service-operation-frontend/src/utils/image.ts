/**
 * 图片URL工具函数
 */

/**
 * 构建完整的图片URL
 * @param imagePath 图片路径（可能是相对路径或完整URL）
 * @returns 完整的图片URL
 */
export function buildImageUrl(imagePath: string): string {
  // 如果已经是完整的URL，直接返回
  if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
    return imagePath;
  }
  
  // 获取API基础URL
  const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000';
  
  // 移除API路径中的/api后缀（如果存在）
  const baseUrl = apiBaseUrl.replace(/\/api$/, '');
  
  // 确保路径以/开头
  const path = imagePath.startsWith('/') ? imagePath : `/${imagePath}`;
  
  return `${baseUrl}${path}`;
}

/**
 * 检查图片是否可以加载
 * @param imageUrl 图片URL
 * @returns Promise<boolean>
 */
export function checkImageLoad(imageUrl: string): Promise<boolean> {
  return new Promise((resolve) => {
    const img = new Image();
    img.onload = () => resolve(true);
    img.onerror = () => resolve(false);
    img.src = imageUrl;
  });
} 