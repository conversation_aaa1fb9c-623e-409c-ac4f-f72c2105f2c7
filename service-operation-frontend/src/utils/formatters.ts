/**
 * 安全地格式化金额
 * @param amount 金额值（可能是数字、字符串、null或undefined）
 * @param defaultValue 默认值
 * @returns 格式化后的金额字符串
 */
export const formatAmount = (amount: number | string | null | undefined, defaultValue: string = '-'): string => {
  if (amount === null || amount === undefined || amount === '') {
    return defaultValue;
  }
  
  const numAmount = typeof amount === 'number' ? amount : parseFloat(amount.toString());
  
  if (isNaN(numAmount)) {
    return defaultValue;
  }
  
  return `¥${numAmount.toFixed(2)}`;
};

/**
 * 安全地格式化数字
 * @param value 数值
 * @param decimals 小数位数
 * @param defaultValue 默认值
 * @returns 格式化后的数字字符串
 */
export const formatNumber = (value: number | string | null | undefined, decimals: number = 2, defaultValue: string = '-'): string => {
  if (value === null || value === undefined || value === '') {
    return defaultValue;
  }
  
  const numValue = typeof value === 'number' ? value : parseFloat(value.toString());
  
  if (isNaN(numValue)) {
    return defaultValue;
  }
  
  return numValue.toFixed(decimals);
};

/**
 * 格式化火车票订单的日期字段（专门处理datetime字符串，只显示日期部分）
 * @param dateString 日期时间字符串
 * @param defaultValue 默认值
 * @returns 格式化后的日期字符串
 */
export const formatTrainOrderDate = (dateString: string | null | undefined, defaultValue: string = '-'): string => {
  if (!dateString) {
    return defaultValue;
  }
  
  try {
    // 处理datetime字符串，只显示日期部分
    const date = new Date(dateString);
    if (isNaN(date.getTime())) {
      return defaultValue;
    }
    
    return date.toLocaleDateString('zh-CN', { 
      year: 'numeric',
      month: '2-digit', 
      day: '2-digit' 
    }).replace(/\//g, '/');
  } catch (error) {
    return defaultValue;
  }
};

/**
 * 格式化火车票订单的时间字段（专门处理datetime字符串，只显示时间部分）
 * @param datetimeString 日期时间字符串
 * @param defaultValue 默认值
 * @returns 格式化后的时间字符串（HH:MM格式）
 */
export const formatTrainOrderTime = (datetimeString: string | null | undefined, defaultValue: string = '-'): string => {
  if (!datetimeString) {
    return defaultValue;
  }
  
  try {
    // 处理datetime字符串，只显示时间部分
    const date = new Date(datetimeString);
    if (isNaN(date.getTime())) {
      return defaultValue;
    }
    
    // 提取时间部分，格式为HH:MM
    return date.toLocaleTimeString('zh-CN', { 
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    });
  } catch (error) {
    return defaultValue;
  }
}; 