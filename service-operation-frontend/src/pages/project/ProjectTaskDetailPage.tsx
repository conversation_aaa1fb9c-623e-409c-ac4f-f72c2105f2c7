import React, { useState, useEffect, useCallback } from 'react';
import { useParams, useNavigate, useSearchParams } from 'react-router-dom';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import MainLayout from '@/components/layout/MainLayout';

import TrainBookingContent from '@/components/booking/TrainBookingContent';
import { 
  ArrowLeft, 
  Building, 
  DollarSign,
  Clock,
  CheckCircle,
  RefreshCw,
  Eye,
  Edit,
  Trash2,
  FileText,
  X,
  Download,
  Search,
  Clock2Icon,
  AlertTriangle,
  Bell,
  User,
  AlertCircle,
  Send,
  Loader,
  XCircle,
  CreditCard
} from 'lucide-react';
import * as XLSX from 'xlsx-js-style';
import { saveAs } from 'file-saver';
import { ProjectService } from '@/services/project';
import { ProjectTaskService } from '@/services/project';
import { Project } from '@/types/project';
import { TaskCardInfo, ProjectTask } from '@/types/project-task';
import { useToast } from '@/hooks/use-toast';
import { formatAmount } from '@/utils/formatters';

import { trainOrderApi, ProjectOrderStatsResponse } from '@/api/trainOrder';
import { api } from '@/services/system';

// 任务类型配置
const taskTypeConfigs = {
  '火车票预订': {
    label: '火车票预订',
    icon: Building,
    color: 'text-blue-600',
    bgColor: 'bg-blue-50',
    borderColor: 'border-blue-500'
  },
  '机票预订': {
    label: '飞机票预订',
    icon: Building,
    color: 'text-green-600',
    bgColor: 'bg-green-50',
    borderColor: 'border-green-500'
  },
  '酒店预订': {
    label: '酒店预订',
    icon: Building,
    color: 'text-purple-600',
    bgColor: 'bg-purple-50',
    borderColor: 'border-purple-500'
  }
};

// 火车票订单接口 - 完整字段定义
interface TrainOrder {
  id: number;
  project_id: number;
  sequence_number: number;
  traveler_full_name: string;
  traveler_surname?: string;
  traveler_given_name?: string;
  nationality?: string;
  gender?: string;
  birth_date?: string;
  id_type?: string;
  id_number?: string;
  id_expiry_date?: string;
  mobile_phone?: string;
  mobile_phone_country_code?: string;
  travel_date?: string;
  departure_station?: string;
  arrival_station?: string;
  train_number?: string;
  seat_type?: string;
  departure_time?: string;
  arrival_time?: string;
  cost_center?: string;
  trip_submission_item?: string;
  contact_person?: string;
  contact_phone?: string;
  contact_email?: string;
  approval_reference?: string;
  company_name?: string;
  booking_agent?: string;
  ticket_sms?: string;
  amount?: number | null;
  order_number?: string;
  bill_number?: string;
  order_status: string;
  fail_reason?: string;
  order_type?: string; // 新增预定类型字段
  created_at: string;
  updated_at: string;
  is_deleted: boolean;
}

// 火车票订单列表响应接口
interface TrainOrderListResponse {
  total: number;
  page: number;
  page_size: number;
  items: TrainOrder[];
}

// Tab类型定义
type TabType = 'booking' | 'all-orders' | 'exception-orders' | 'details' | 'reconciliation';

// 导入统一的API配置

// 可编辑输入组件 - 独立定义避免重新渲染导致焦点丢失
interface EditableInputProps {
  field: keyof TrainOrder;
  value: string | number | null | undefined;
  type?: string;
  placeholder?: string;
  options?: { value: string; label: string }[];
  onFieldChange: (field: keyof TrainOrder, value: string | number) => void;
  validationErrors: Record<string, string>;
}

const EditableInput = React.memo<EditableInputProps>(({ 
  field, 
  value, 
  type = 'text', 
  placeholder, 
  options, 
  onFieldChange, 
  validationErrors 
}) => {
  const error = validationErrors[field];
  const inputClassName = `w-full px-2 py-1 border ${error ? 'border-red-500' : 'border-gray-300'} rounded text-sm focus:outline-none focus:ring-2 ${error ? 'focus:ring-red-500' : 'focus:ring-blue-500'}`;
  
  const handleChange = useCallback((e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    onFieldChange(field, e.target.value);
  }, [field, onFieldChange]);
  
  if (options) {
    return (
      <div className="w-full">
        <select
          value={String(value || '')}
          onChange={handleChange}
          className={inputClassName}
        >
          <option value="">请选择</option>
          {options.map(option => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
        {error && (
          <div className="mt-1 text-red-600 text-xs">
            {error}
          </div>
        )}
      </div>
    );
  }
  
  return (
    <div className="w-full">
      <input
        type={type}
        value={String(value || '')}
        onChange={handleChange}
        className={inputClassName}
        placeholder={placeholder}
      />
      {error && (
        <div className="mt-1 text-red-600 text-xs">
          {error}
        </div>
      )}
    </div>
  );
});

const ProjectTaskDetailPage: React.FC = () => {
  const { projectId } = useParams<{ projectId: string }>();
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  const taskType = searchParams.get('type') || '火车票预订';
  const initialTab = searchParams.get('tab') as TabType || 'booking';
  const { toast } = useToast();
  
  const [project, setProject] = useState<Project | null>(null);
  const [tasks, setTasks] = useState<TaskCardInfo[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<TabType>(initialTab);

  // 所有订单相关状态
  const [allOrders, setAllOrders] = useState<TrainOrder[]>([]);
  const [allOrdersLoading, setAllOrdersLoading] = useState(false);
  const [allOrdersPage, setAllOrdersPage] = useState(1);
  const [allOrdersPageSize, setAllOrdersPageSize] = useState(20);
  const [allOrdersTotal, setAllOrdersTotal] = useState(0);

  // 异常订单相关状态
  const [exceptionOrders, setExceptionOrders] = useState<TrainOrder[]>([]);
  const [exceptionOrdersLoading, setExceptionOrdersLoading] = useState(false);
  const [exceptionOrdersPage, setExceptionOrdersPage] = useState(1);
  const [exceptionOrdersPageSize] = useState(20);
  const [exceptionOrdersTotal, setExceptionOrdersTotal] = useState(0);

  // 搜索相关状态
  const [searchTravelerName, setSearchTravelerName] = useState('');
  const [searchMobilePhone, setSearchMobilePhone] = useState('');
  const [searchContactPhone, setSearchContactPhone] = useState('');

  // 状态筛选相关状态
  const [selectedStatuses, setSelectedStatuses] = useState<string[]>([
    'check_failed', 'initial', 'submitted', 'processing', 'completed', 'failed'
  ]); // 默认选中所有状态，包括验证失败(check_failed)和待提交(initial)

  // 异常订单搜索相关状态
  const [exceptionSearchTravelerName, setExceptionSearchTravelerName] = useState('');
  const [exceptionSearchMobilePhone, setExceptionSearchMobilePhone] = useState('');
  const [exceptionSearchContactPhone, setExceptionSearchContactPhone] = useState('');
  
  // 异常订单独立的状态筛选（只包含验证失败和预定失败）
  const [exceptionSelectedStatuses, setExceptionSelectedStatuses] = useState<string[]>(['check_failed', 'failed']);

  // 对账单相关状态
  const [reconciliationOrders, setReconciliationOrders] = useState<TrainOrder[]>([]);
  const [reconciliationOrdersLoading, setReconciliationOrdersLoading] = useState(false);
  const [reconciliationOrdersPage, setReconciliationOrdersPage] = useState(1);
  const [reconciliationOrdersPageSize] = useState(20);
  const [reconciliationOrdersTotal, setReconciliationOrdersTotal] = useState(0);

  // 对账单搜索相关状态
  const [reconciliationSearchTravelerName, setReconciliationSearchTravelerName] = useState('');
  const [reconciliationSearchMobilePhone, setReconciliationSearchMobilePhone] = useState('');
  const [reconciliationSearchContactPhone, setReconciliationSearchContactPhone] = useState('');

  // 订单操作状态
  const [selectedOrder, setSelectedOrder] = useState<TrainOrder | null>(null);
  const [editingOrder, setEditingOrder] = useState<TrainOrder | null>(null);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [orderOperationLoading, setOrderOperationLoading] = useState(false);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

  // 预订任务相关状态
  const [bookingTasks, setBookingTasks] = useState<ProjectTask[]>([]);
  const [bookingTasksLoading, setBookingTasksLoading] = useState(false);
  const [taskStats, setTaskStats] = useState<{[key: string]: any}>({});

  // 项目订单统计状态
  const [projectStats, setProjectStats] = useState<ProjectOrderStatsResponse | null>(null);

  // 导出Excel功能
  const exportToExcel = (data: TrainOrder[], filename: string, title?: string) => {
    try {
      // 准备导出数据 - 包含所有数据库字段
      const exportData = data.map((order, index) => ({
        '序号': index + 1,
        '订单状态': getOrderStatusText(order.order_status),
        '出行人姓名': order.traveler_full_name || '-',
        '出行人姓': order.traveler_surname || '-',
        '出行人名': order.traveler_given_name || '-',
        '国籍': order.nationality || '-',
        '性别': order.gender || '-',
        '出生日期': order.birth_date || '-',
        '证件类型': order.id_type || '-',
        '证件号码': order.id_number || '-',
        '证件有效期至': order.id_expiry_date || '-',
        '手机号': order.mobile_phone || '-',
        '手机号国际区号': order.mobile_phone_country_code || '-',
        '出行日期': order.travel_date || '-',
        '出发站名': order.departure_station || '-',
        '到达站名': order.arrival_station || '-',
        '车次': order.train_number || '-',
        '座位类型': order.seat_type || '-',
        '出发时间': order.departure_time || '-',
        '到达时间': order.arrival_time || '-',
        '成本中心': order.cost_center || '-',
        '行程提交项': order.trip_submission_item || '-',
        '联系人': order.contact_person || '-',
        '联系人手机号': order.contact_phone || '-',
        '联系人邮箱': order.contact_email || '-',
        '审批参考人': order.approval_reference || '-',
        '公司名称': order.company_name || '-',
        '代订人': order.booking_agent || '-',
        '出票短信': order.ticket_sms || '-',
        '金额': order.amount || 0,
        '订单号': order.order_number || '-',
        '账单号': order.bill_number || '-',
        '失败原因': order.fail_reason || '-',
        '创建时间': formatDateTime(order.created_at),
        '更新时间': formatDateTime(order.updated_at)
      }));

      // 获取列名
      const headers = Object.keys(exportData[0] || {});
      
      // 创建空工作表
      const ws = XLSX.utils.aoa_to_sheet([]);
      
      // 如果有标题，添加标题行
      if (title) {
        // 第一行：标题
        XLSX.utils.sheet_add_aoa(ws, [[title]], { origin: 'A1' });
        
        // 合并标题行单元格
        ws['!merges'] = [{ s: { r: 0, c: 0 }, e: { r: 0, c: headers.length - 1 } }];
        
        // 第二行：列名
        XLSX.utils.sheet_add_aoa(ws, [headers], { origin: 'A2' });
        
        // 第三行开始：数据
        const dataRows = exportData.map(row => headers.map(header => (row as any)[header]));
        XLSX.utils.sheet_add_aoa(ws, dataRows, { origin: 'A3' });
      } else {
        // 没有标题时，直接使用原来的方式
        XLSX.utils.sheet_add_json(ws, exportData, { origin: 'A1' });
      }
      
      // 设置列宽
      const colWidths = [
        { wch: 6 },   // 序号
        { wch: 10 },  // 订单状态
        { wch: 12 },  // 出行人姓名
        { wch: 10 },  // 出行人姓
        { wch: 10 },  // 出行人名
        { wch: 8 },   // 国籍
        { wch: 6 },   // 性别
        { wch: 12 },  // 出生日期
        { wch: 10 },  // 证件类型
        { wch: 18 },  // 证件号码
        { wch: 12 },  // 证件有效期至
        { wch: 12 },  // 手机号
        { wch: 8 },   // 手机号国际区号
        { wch: 12 },  // 出行日期
        { wch: 12 },  // 出发站名
        { wch: 12 },  // 到达站名
        { wch: 10 },  // 车次
        { wch: 10 },  // 座位类型
        { wch: 10 },  // 出发时间
        { wch: 10 },  // 到达时间
        { wch: 15 },  // 成本中心
        { wch: 15 },  // 行程提交项
        { wch: 10 },  // 联系人
        { wch: 12 },  // 联系人手机号
        { wch: 20 },  // 联系人邮箱
        { wch: 12 },  // 审批参考人
        { wch: 15 },  // 公司名称
        { wch: 10 },  // 代订人
        { wch: 15 },  // 出票短信
        { wch: 10 },  // 金额
        { wch: 15 },  // 订单号
        { wch: 15 },  // 账单号
        { wch: 20 },  // 失败原因
        { wch: 18 },  // 创建时间
        { wch: 18 }   // 更新时间
      ];
      ws['!cols'] = colWidths;

      // 获取工作表范围
      const range = XLSX.utils.decode_range(ws['!ref'] || 'A1');
      
      // 定义完整的边框样式
      const borderAll = {
        style: 'thin',
        color: { rgb: '000000' }
      };
      
      // 为所有单元格添加边框和样式
      for (let row = range.s.r; row <= range.e.r; row++) {
        for (let col = range.s.c; col <= range.e.c; col++) {
          const cellAddress = XLSX.utils.encode_cell({ r: row, c: col });
          if (!ws[cellAddress]) ws[cellAddress] = { t: 's', v: '' };
          
          // 设置单元格样式
          ws[cellAddress].s = {
            border: {
              top: borderAll,
              bottom: borderAll,
              left: borderAll,
              right: borderAll
            },
            alignment: { 
              horizontal: 'left', 
              vertical: 'center',
              wrapText: true 
            },
            font: { 
              name: '微软雅黑', 
              sz: 10 
            }
          };
          
          // 为标题行添加特殊样式
          if (title && row === 0) {
            ws[cellAddress].s.fill = {
              patternType: 'solid',
              fgColor: { rgb: 'E3F2FD' }
            };
            ws[cellAddress].s.font = {
              name: '微软雅黑',
              sz: 14,
              bold: true
            };
            ws[cellAddress].s.alignment = {
              horizontal: 'center',
              vertical: 'center',
              wrapText: true
            };
          }
          
          // 为列名行添加特殊样式
          else if ((title && row === 1) || (!title && row === 0)) {
            ws[cellAddress].s.fill = {
              patternType: 'solid',
              fgColor: { rgb: 'F3F4F6' }
            };
            ws[cellAddress].s.font = {
              name: '微软雅黑',
              sz: 10,
              bold: true
            };
            ws[cellAddress].s.alignment = {
              horizontal: 'center',
              vertical: 'center',
              wrapText: true
            };
          }
        }
      }

      // 创建工作簿
      const wb = XLSX.utils.book_new();
      
      // 添加工作表
      XLSX.utils.book_append_sheet(wb, ws, '火车票订单');
      
      // 生成Excel文件
      const excelBuffer = XLSX.write(wb, { 
        bookType: 'xlsx', 
        type: 'array',
        cellStyles: true
      });
      const blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      
      // 下载文件
      saveAs(blob, filename);
      
      // 显示成功提示
      toast({
        title: "导出成功",
        description: "Excel文件已成功导出",
        variant: "default",
      });
      
    } catch (error) {
      console.error('导出Excel失败:', error);
      toast({
        title: "导出失败",
        description: "导出Excel文件时发生错误，请重试",
        variant: "destructive",
      });
    }
  };



  // 导出所有订单数据（分批获取）
  const exportAllOrders = async () => {
    if (!projectId) return;
    
    try {
      // 使用当前的搜索和筛选条件分批获取数据
      const allOrdersData: TrainOrder[] = [];
      let page = 1;
      const pageSize = 100; // 使用最大允许的page_size
      let hasMoreData = true;

      // 使用当前的筛选条件
      const statusFilter = selectedStatuses.length > 0 ? selectedStatuses.join(',') : '';

      while (hasMoreData) {
        const response = await trainOrderApi.getOrdersByProject(
          projectId,
          page,
          pageSize,
          statusFilter,
          searchTravelerName || undefined,
          searchMobilePhone || undefined,
          searchContactPhone || undefined
        );

        if (!response.data) {
          throw new Error('获取订单数据失败');
        }

        const data = response.data;
        allOrdersData.push(...data.items);

        // 检查是否还有更多数据
        hasMoreData = data.items.length === pageSize && allOrdersData.length < data.total;
        page++;
      }
      
      if (allOrdersData.length === 0) {
        toast({
          title: "无数据",
          description: "当前筛选条件下暂无订单数据可导出",
          variant: "destructive",
        });
        return;
      }

      // 生成更详细的文件名，包含筛选条件信息
      const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
      let filenameSuffix = '';
      
      // 添加状态筛选信息到文件名
      if (selectedStatuses.length > 0 && selectedStatuses.length < 6) {
        const statusNames = selectedStatuses.map(status => getOrderStatusText(status)).join('_');
        filenameSuffix += `_${statusNames}`;
      }
      
      // 添加搜索条件信息到文件名
      if (searchTravelerName) {
        filenameSuffix += `_出行人${searchTravelerName}`;
      }
      if (searchMobilePhone) {
        filenameSuffix += `_手机${searchMobilePhone}`;
      }
      if (searchContactPhone) {
        filenameSuffix += `_联系人${searchContactPhone}`;
      }

      const filename = `火车票订单_项目${projectId}${filenameSuffix}_${timestamp}.xlsx`;
      const title = `${project?.project_name || '项目' + projectId} - 所有订单明细`;
      exportToExcel(allOrdersData, filename, title);

      toast({
        title: "导出成功",
        description: `成功导出 ${allOrdersData.length} 条筛选后的订单数据`,
        variant: "default",
      });
      
    } catch (error: any) {
      console.error('导出订单失败:', error);
      toast({
        title: "导出失败",
        description: error.message || "获取订单数据失败，请重试",
        variant: "destructive",
      });
    }
  };

  // 格式化日期时间
  const formatDateTime = (dateString: string) => {
    if (!dateString) return '-';
    try {
      const date = new Date(dateString);
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
    } catch {
      return dateString;
    }
  };

  // 获取订单状态文本
  const getOrderStatusText = (status: string) => {
    switch (status) {
      case 'initial':
        return '待提交';
      case 'submitted':
        return '已提交';
      case 'processing':
        return '处理中';
      case 'completed':
        return '预定完成';
      case 'failed':
        return '预定失败';
      case 'check_failed':
        return '验证失败';
      default:
        return status;
    }
  };



  // 加载所有订单数据
  const loadAllOrders = async (page: number = 1) => {
    if (!projectId) return;

    setAllOrdersLoading(true);
    try {
      const statusFilter = selectedStatuses.length > 0 ? selectedStatuses.join(',') : '';
      
      const response = await trainOrderApi.getOrdersByProject(
        projectId,
        page,
        allOrdersPageSize,
        statusFilter,
        searchTravelerName || undefined,
        searchMobilePhone || undefined,
        searchContactPhone || undefined
      );

      if (response.data) {
        setAllOrders(response.data.items || []);
        setAllOrdersTotal(response.data.total || 0);
        setAllOrdersPage(page);
      }
    } catch (error: any) {
      console.error('加载所有订单失败:', error);
      toast({
        title: "加载失败",
        description: error.message || "获取订单数据失败，请重试",
        variant: "destructive",
      });
    } finally {
      setAllOrdersLoading(false);
    }
  };

  // 加载项目订单统计数据
  const loadProjectStats = async () => {
    if (!projectId) return;

    try {
      const response = await trainOrderApi.getProjectStats(projectId);
      if (response.data) {
        setProjectStats(response.data);
      }
    } catch (error: any) {
      console.error('加载项目统计失败:', error);
    }
  };

  // 搜索处理函数
  const handleSearch = () => {
    setAllOrdersPage(1); // 重置到第一页
    loadAllOrders(1);
  };

  // 清空搜索条件
  const handleClearSearch = () => {
    setSearchTravelerName('');
    setSearchMobilePhone('');
    setSearchContactPhone('');
    setSelectedStatuses(['check_failed', 'initial', 'submitted', 'processing', 'completed', 'failed']); // 重置为默认状态，包含所有状态
    loadAllOrders(1);
  };

  // 获取订单状态显示
  const getOrderStatusDisplay = (status: string) => {
    switch (status) {
      case 'initial':
        return <span className="px-2 py-1 bg-gray-100 text-gray-600 rounded-full text-xs">待提交</span>;
      case 'submitted':
        return <span className="px-2 py-1 bg-blue-100 text-blue-700 rounded-full text-xs">已提交</span>;
      case 'processing':
        return <span className="px-2 py-1 bg-cyan-100 text-cyan-700 rounded-full text-xs">处理中</span>;
      case 'completed':
        return <span className="px-2 py-1 bg-green-100 text-green-700 rounded-full text-xs">预定完成</span>;
      case 'failed':
        return <span className="px-2 py-1 bg-red-100 text-red-700 rounded-full text-xs">预定失败</span>;
      case 'check_failed':
        return <span className="px-2 py-1 bg-amber-100 text-amber-700 rounded-full text-xs">验证失败</span>;
      default:
        return <span className="px-2 py-1 bg-gray-100 text-gray-600 rounded-full text-xs">{status}</span>;
    }
  };

  // 获取预定类型显示
  const getOrderTypeDisplay = (orderType?: string) => {
    switch (orderType) {
      case 'book':
        return <span className="px-2 py-1 bg-blue-100 text-blue-700 rounded-full text-xs">仅预定</span>;
      case 'book_and_issue':
        return <span className="px-2 py-1 bg-green-100 text-green-700 rounded-full text-xs">预定且出票</span>;
      default:
        return <span className="text-gray-400 text-xs">-</span>;
    }
  };



  // 订单操作处理函数
  const handleViewOrder = (order: TrainOrder) => {
    setSelectedOrder(order);
    setIsViewModalOpen(true);
  };

  const handleEditOrder = (order: TrainOrder) => {
    setSelectedOrder(order);
    setEditingOrder({ ...order }); // 创建副本用于编辑
    setValidationErrors({}); // 清空验证错误
    setIsEditModalOpen(true);
  };

  // 处理字段变更 - 使用useCallback优化避免重新渲染
  const handleFieldChange = useCallback((field: keyof TrainOrder, value: string | number) => {
    setEditingOrder(prev => {
      if (!prev) return prev;
      
      // 更新字段值
      const updatedOrder = {
        ...prev,
        [field]: value
      };
      
      // 实时验证当前字段
      const error = validateField(field, value, updatedOrder.id_type);
      setValidationErrors(prevErrors => {
        const newErrors = { ...prevErrors };
        if (error) {
          newErrors[field] = error;
        } else {
          delete newErrors[field];
        }
        return newErrors;
      });
      
      return updatedOrder;
    });
  }, []); // 空依赖数组，使用函数式更新避免闭包问题

  // 保存编辑的订单


  // 验证规则定义 - 与火车票预订页面保持一致
  const getFieldValidationRule = (field: keyof TrainOrder) => {
    const rules: Record<string, {
      required?: boolean;
      pattern?: RegExp;
      message?: string;
      maxLength?: number;
      minLength?: number;
      enumValues?: string[];
    }> = {
      // 基础必填字段（所有情况下都必填）
      traveler_full_name: { required: true, message: '出行人姓名不能为空', maxLength: 50 },
      id_type: { 
        required: true, 
        message: '证件类型不能为空', 
        maxLength: 20,
        enumValues: ['身份证', '公务护照', '普通护照', '港澳通行证', '台胞证', '回乡证', '军人证', '海员证', '台湾通行证', '外国永久居留证', '港澳台居民居住证', '其他']
      },
      id_number: { 
        required: true, 
        message: '证件号码不能为空',
        maxLength: 50
      },
      mobile_phone: { 
        required: true, 
        pattern: /^1[3-9]\d{9}$/, 
        message: '手机号码格式不正确',
        maxLength: 11
      },
      travel_date: { required: true, message: '出行日期不能为空' },
      departure_station: { required: true, message: '出发站名不能为空', maxLength: 50 },
      arrival_station: { required: true, message: '到达站名不能为空', maxLength: 50 },
      train_number: { required: true, message: '车次不能为空', maxLength: 20 },
      seat_type: { required: true, message: '座位类型不能为空', maxLength: 20 },
      departure_time: { required: true, message: '出发时间不能为空' },
      arrival_time: { required: true, message: '到达时间不能为空' },
      cost_center: { required: true, message: '成本中心不能为空', maxLength: 100 },
      contact_person: { required: true, message: '联系人不能为空', maxLength: 50 },
      contact_phone: { 
        required: true, 
        pattern: /^1[3-9]\d{9}$/, 
        message: '联系人手机号格式不正确',
        maxLength: 11
      },
      approval_reference: { required: true, message: '审批参考人不能为空', maxLength: 50 },
      
      // 证件类型不为身份证时的额外必填字段（动态必填）
      traveler_surname: { maxLength: 25 },
      traveler_given_name: { maxLength: 25 },
      nationality: { maxLength: 20 },
      gender: { 
        maxLength: 10,
        enumValues: ['男', '女']
      }, 
      birth_date: {},
      id_expiry_date: {},
      
      // 手机号国际区号（自动补齐为86，非必填）
      mobile_phone_country_code: { maxLength: 10 },
      
      // 非必填字段
      contact_email: { 
        pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/, 
        message: '联系人邮箱格式不正确',
        maxLength: 100
      },
      company_name: { maxLength: 100 },
      booking_agent: { maxLength: 50 },
      order_number: { maxLength: 50 },
      bill_number: { maxLength: 50 },
      amount: { 
        pattern: /^\d+(\.\d{1,2})?$/, 
        message: '金额格式不正确（最多两位小数）'
      },
      sequence_number: { maxLength: 50 },
      trip_submission_item: { maxLength: 200 },
      ticket_sms: { maxLength: 500 }
    };
    
    return rules[field] || {};
  };

  // 验证单个字段
  const validateField = (field: keyof TrainOrder, value: string | number | boolean | null | undefined, idType?: string): string | null => {
    const rule = getFieldValidationRule(field);
    
    // 处理boolean类型字段
    if (typeof value === 'boolean') {
      return null;
    }
    
    const stringValue = String(value || '').trim();
    
    // 动态必填字段：当证件类型不为身份证时，这些字段变为必填
    const dynamicRequiredFields = ['traveler_surname', 'traveler_given_name', 'nationality', 'gender', 'birth_date', 'id_expiry_date'];
    let isRequired = rule.required;
    
    if (dynamicRequiredFields.includes(field) && idType && idType !== '身份证') {
      isRequired = true;
    }
    
    // 必填验证
    if (isRequired && !stringValue) {
      const fieldNames: Record<string, string> = {
        // 基础必填字段
        traveler_full_name: '出行人姓名',
        id_type: '证件类型',
        id_number: '证件号码',
        mobile_phone: '手机号',
        travel_date: '出行日期',
        departure_station: '出发站名',
        arrival_station: '到达站名',
        train_number: '车次',
        seat_type: '座位类型',
        departure_time: '出发时间',
        arrival_time: '到达时间',
        cost_center: '成本中心',
        contact_person: '联系人',
        contact_phone: '联系人手机号',
        approval_reference: '审批参考人',
        // 动态必填字段
        traveler_surname: '出行人姓',
        traveler_given_name: '出行人名',
        nationality: '国籍',
        gender: '性别',
        birth_date: '出生日期',
        id_expiry_date: '证件有效期'
      };
      return fieldNames[field] ? `${fieldNames[field]}不能为空` : (rule.message || `${field}不能为空`);
    }
    
    // 如果值为空且非必填，跳过其他验证
    if (!stringValue && !isRequired) {
      return null;
    }
    
    // 长度验证
    if (rule.maxLength && stringValue.length > rule.maxLength) {
      const fieldNames: Record<string, string> = {
        traveler_full_name: '出行人姓名',
        traveler_surname: '出行人姓',
        traveler_given_name: '出行人名',
        nationality: '国籍',
        gender: '性别',
        id_type: '证件类型',
        id_number: '证件号码',
        mobile_phone: '手机号',
        mobile_phone_country_code: '手机号国际区号',
        travel_date: '出行日期',
        departure_station: '出发站名',
        arrival_station: '到达站名',
        train_number: '车次',
        seat_type: '座位类型',
        departure_time: '出发时间',
        arrival_time: '到达时间',
        cost_center: '成本中心',
        contact_person: '联系人',
        contact_phone: '联系人手机号',
        contact_email: '联系人邮箱',
        approval_reference: '审批参考人',
        company_name: '公司名称',
        booking_agent: '代订人',
        order_number: '订单号',
        bill_number: '账单号',
        sequence_number: '差旅单号',
        trip_submission_item: '行程提交项',
        ticket_sms: '出票短信'
      };
      const fieldDisplayName = fieldNames[field] || field;
      return `${fieldDisplayName}长度不能超过${rule.maxLength}个字符`;
    }

    // 格式验证
    if (rule.pattern && !rule.pattern.test(stringValue)) {
      return rule.message || '格式不正确';
    }
    
    // 身份证号码特殊验证：只在证件类型为身份证时验证格式
    if (field === 'id_number' && stringValue && idType === '身份证') {
      const idNumberPattern = /^[0-9Xx]{15}$|^[0-9Xx]{18}$/;
      if (!idNumberPattern.test(stringValue)) {
        return '身份证号码格式不正确（15位或18位数字或字母X）';
      }
    }
    
    // 枚举值验证
    if (rule.enumValues && !rule.enumValues.includes(stringValue)) {
      return `${rule.message || field}必须是以下值之一：${rule.enumValues.join('、')}`;
    }
    
    return null;
  };

  // 验证所有字段
  const validateAllFields = (order: TrainOrder): Record<string, string> => {
    const errors: Record<string, string> = {};
    
    // 基础必填字段（所有情况下都必填）
    const basicRequiredFields: (keyof TrainOrder)[] = [
      'traveler_full_name', 'id_type', 'id_number', 'mobile_phone', 'travel_date',
      'departure_station', 'arrival_station', 'train_number', 'seat_type', 
      'departure_time', 'arrival_time', 'cost_center', 'contact_person', 
      'contact_phone', 'approval_reference'
    ];
    
    // 证件类型不为身份证时的额外必填字段
    const dynamicRequiredFields: (keyof TrainOrder)[] = [
      'traveler_surname', 'traveler_given_name', 'nationality', 
      'gender', 'birth_date', 'id_expiry_date'
    ];
    
    // 验证基础必填字段
    basicRequiredFields.forEach(field => {
      const error = validateField(field, order[field], order.id_type);
      if (error) {
        errors[field] = error;
      }
    });
    
    // 验证动态必填字段（仅当证件类型不为身份证时）
    if (order.id_type && order.id_type !== '身份证') {
      dynamicRequiredFields.forEach(field => {
        const error = validateField(field, order[field], order.id_type);
        if (error) {
          errors[field] = error;
        }
      });
    }
    
    // 验证非必填字段的格式（如果有值的话）
    const optionalFields: (keyof TrainOrder)[] = ['contact_email', 'amount'];
    optionalFields.forEach(field => {
      if (order[field]) {
        const error = validateField(field, order[field], order.id_type);
        if (error) {
          errors[field] = error;
        }
      }
    });
    
    return errors;
  };

  const saveEditingOrder = async () => {
    if (!editingOrder || !selectedOrder || orderOperationLoading) return;

    // 保存前进行完整验证
    const errors = validateAllFields(editingOrder);
    setValidationErrors(errors);
    
    if (Object.keys(errors).length > 0) {
      toast({
        title: "验证失败",
        description: `请修正 ${Object.keys(errors).length} 个字段的错误后再保存`,
        variant: "destructive",
      });
      return;
    }

    setOrderOperationLoading(true);
    try {
      const response = await api.put<TrainOrder>(`/train-order/${selectedOrder.id}`, editingOrder);

      if (response.success && response.data) {
        // 根据订单状态显示不同的提示信息
        if (response.data.order_status === 'check_failed') {
          toast({
            title: "保存完成",
            description: `${response.data.traveler_full_name} 的订单信息已保存，但验证失败：${response.data.fail_reason || '请检查数据格式'}`,
            variant: "destructive",
          });
        } else if (response.data.order_status === 'initial') {
          toast({
            title: "保存成功",
            description: `${response.data.traveler_full_name} 的订单信息已成功更新，状态已改为待提交`,
            variant: "default",
          });
        } else {
          toast({
            title: "保存成功",
            description: `${response.data.traveler_full_name} 的订单信息已成功更新`,
            variant: "default",
          });
        }
        
        setIsEditModalOpen(false);
        setSelectedOrder(null);
        setEditingOrder(null);
        setValidationErrors({});
        // 刷新订单列表
        if (activeTab === 'all-orders') {
          await loadAllOrders(allOrdersPage);
        } else if (activeTab === 'exception-orders') {
          await loadExceptionOrders(exceptionOrdersPage);
        } else if (activeTab === 'reconciliation') {
          await loadReconciliationOrders(reconciliationOrdersPage);
        }
      } else {
        throw new Error(response.error || '保存订单失败');
      }
    } catch (error) {
      console.error('保存订单失败:', error);
      toast({
        title: "保存失败",
        description: "保存订单时发生错误，请重试",
        variant: "destructive",
      });
    } finally {
      setOrderOperationLoading(false);
    }
  };

  const handleDeleteOrder = (order: TrainOrder) => {
    setSelectedOrder(order);
    setIsDeleteDialogOpen(true);
  };

  const confirmDeleteOrder = async () => {
    if (!selectedOrder || orderOperationLoading) return;

    setOrderOperationLoading(true);
    try {
      const response = await api.delete(`/train-order/${selectedOrder.id}`);

      if (response.success) {
        toast({
          title: "删除成功",
          description: `已删除 ${selectedOrder.traveler_full_name} 的订单`,
        });
        setIsDeleteDialogOpen(false);
        setSelectedOrder(null);
        // 刷新订单列表
        if (activeTab === 'all-orders') {
          await loadAllOrders(allOrdersPage);
        } else if (activeTab === 'exception-orders') {
          await loadExceptionOrders(exceptionOrdersPage);
        } else if (activeTab === 'reconciliation') {
          await loadReconciliationOrders(reconciliationOrdersPage);
        }
      } else {
        throw new Error(response.error || '删除订单失败');
      }
    } catch (error) {
      console.error('删除订单失败:', error);
      toast({
        title: "删除失败",
        description: "删除订单时发生错误，请重试",
        variant: "destructive",
      });
    } finally {
      setOrderOperationLoading(false);
    }
  };



  // 格式化日期

  // 模拟任务卡片数据的生成
  const generateTaskCardInfo = (task: ProjectTask): TaskCardInfo => {
    const mockOrderCount = Math.floor(Math.random() * 10) + 1;
    const mockAmount = Math.floor(Math.random() * 50000) + 5000;
    const mockPeople = Math.floor(Math.random() * 20) + 5;
    const statuses = ['已确认', '待确认', '已取消'];
    const mockStatus = statuses[Math.floor(Math.random() * statuses.length)];
    const mockException = Math.random() > 0.8;

    return {
      task,
      order_count: mockOrderCount,
      total_amount: mockAmount,
      total_people: mockPeople,
      booking_status: mockStatus,
      has_exception: mockException
    };
  };

  // 加载项目详情和任务列表
  const loadProjectDetails = async () => {
    if (!projectId) return;

    setLoading(true);
    setError(null);

    try {
      // 加载项目详情
      const projectResponse = await ProjectService.getProject(parseInt(projectId));
      if (!projectResponse.success) {
        throw new Error(projectResponse.error || '获取项目详情失败');
      }
      setProject(projectResponse.data!);

      // 加载项目任务列表
      const tasksResponse = await ProjectTaskService.getTasksByProject(
        parseInt(projectId),
        { page: 1, page_size: 100 }
      );

      // 筛选指定类型的任务并生成卡片信息
      const filteredTasks = tasksResponse.items.filter(task => 
        task.task_type === taskType || 
        (taskType === '机票预订' && task.task_type === '飞机票预订')
      );
      
      const taskCardInfos = filteredTasks.map(generateTaskCardInfo);
      setTasks(taskCardInfos);

    } catch (err) {
      console.error('加载项目详情失败:', err);
      setError(err instanceof Error ? err.message : '加载项目详情失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadProjectDetails();
  }, [projectId, taskType]);

  // 当切换到所有订单tab时加载数据
  useEffect(() => {
    if (activeTab === 'all-orders') {
      loadAllOrders(1);
      loadProjectStats(); // 加载统计数据
    }
    if (activeTab === 'exception-orders') {
      loadExceptionOrders(1);
    }
    if (activeTab === 'details') {
      loadBookingTasks();
    }
    if (activeTab === 'reconciliation') {
      loadReconciliationOrders(1);
    }
  }, [activeTab]);

  // 当状态筛选变化时重新加载订单
  useEffect(() => {
    if (activeTab === 'all-orders') {
      loadAllOrders(1);
    }
  }, [selectedStatuses]);

  // 监听所有订单页面大小变化，自动重新加载数据
  useEffect(() => {
    if (activeTab === 'all-orders') {
      setAllOrdersPage(1); // 重置到第一页
      loadAllOrders(1);
    }
  }, [allOrdersPageSize]);

  // 监听异常订单状态筛选变化，自动重新加载数据
  useEffect(() => {
    if (activeTab === 'exception-orders') {
      setExceptionOrdersPage(1); // 重置到第一页
      loadExceptionOrders(1);
    }
  }, [exceptionSelectedStatuses]);

  // 监听对账单搜索条件变化，自动重新加载数据
  useEffect(() => {
    if (activeTab === 'reconciliation') {
      setReconciliationOrdersPage(1); // 重置到第一页
      loadReconciliationOrders(1);
    }
  }, [reconciliationSearchTravelerName, reconciliationSearchMobilePhone, reconciliationSearchContactPhone]);

  // 处理tab切换
  const handleTabChange = (tab: TabType) => {
    setActiveTab(tab);
    // 更新URL参数
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set('tab', tab);
    setSearchParams(newSearchParams);
  };



  // 获取当前任务类型配置
  const currentConfig = taskTypeConfigs[taskType as keyof typeof taskTypeConfigs];
  const IconComponent = currentConfig?.icon || Building;

  // 删除未使用的统计变量

  // 渲染预订内容
  const renderBookingContent = () => {
    if (taskType === '火车票预订') {
      return <TrainBookingContent onNavigateToAllOrders={() => handleTabChange('all-orders')} />;
    } else if (taskType === '机票预订') {
      return (
        <Card className="bg-white border border-gray-200 p-8 text-center">
          <div className="p-4 bg-green-50 rounded-full mx-auto w-fit mb-4">
            <Building className="h-8 w-8 text-green-600" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">机票预订功能</h3>
          <p className="text-gray-500 mb-4">机票预订功能正在开发中...</p>
          <Button className="bg-green-600 hover:bg-green-700 text-white">
            敬请期待
          </Button>
        </Card>
      );
    } else if (taskType === '酒店预订') {
      return (
        <Card className="bg-white border border-gray-200 p-8 text-center">
          <div className="p-4 bg-purple-50 rounded-full mx-auto w-fit mb-4">
            <Building className="h-8 w-8 text-purple-600" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">酒店预订功能</h3>
          <p className="text-gray-500 mb-4">酒店预订功能正在开发中...</p>
          <Button className="bg-purple-600 hover:bg-purple-700 text-white">
            敬请期待
          </Button>
        </Card>
      );
    }
    return null;
  };

  // 渲染所有订单内容
  const renderAllOrdersContent = () => {
    // 计算各状态订单数量（前端计算，用于状态筛选显示）
    const statusCounts = allOrders.reduce((counts, order) => {
      counts[order.order_status] = (counts[order.order_status] || 0) + 1;
      return counts;
    }, {} as Record<string, number>);

    return (
      <div className="space-y-4">
        {/* 订单统计卡片 */}
        {allOrdersTotal > 0 && (
          <Card className="bg-white border border-gray-200">
            <div className="p-4">
              <h3 className="text-sm font-medium text-gray-900 mb-3">订单状态统计</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-4">
                {/* 总订单数 */}
                <div className="text-center">
                  <div className="p-2 bg-purple-100 rounded-lg mx-auto w-fit mb-1">
                    <CreditCard className="h-4 w-4 text-purple-600" />
                  </div>
                  <p className="text-lg font-medium text-gray-900">{projectStats?.total_orders || allOrdersTotal}</p>
                  <p className="text-xs text-gray-500">总订单数</p>
                </div>
                {/* 验证失败 */}
                <div className="text-center">
                  <div className="p-2 bg-amber-100 rounded-lg mx-auto w-fit mb-1">
                    <AlertCircle className="h-4 w-4 text-amber-600" />
                  </div>
                  <p className="text-lg font-medium text-gray-900">{statusCounts['check_failed'] || 0}</p>
                  <p className="text-xs text-gray-500">验证失败</p>
                </div>
                {/* 待提交 */}
                <div className="text-center">
                  <div className="p-2 bg-gray-100 rounded-lg mx-auto w-fit mb-1">
                    <Clock className="h-4 w-4 text-gray-600" />
                  </div>
                  <p className="text-lg font-medium text-gray-900">{statusCounts['initial'] || 0}</p>
                  <p className="text-xs text-gray-500">待提交</p>
                </div>
                {/* 已提交 */}
                <div className="text-center">
                  <div className="p-2 bg-blue-100 rounded-lg mx-auto w-fit mb-1">
                    <Send className="h-4 w-4 text-blue-600" />
                  </div>
                  <p className="text-lg font-medium text-gray-900">{statusCounts['submitted'] || 0}</p>
                  <p className="text-xs text-gray-500">已提交</p>
                </div>
                {/* 处理中 */}
                <div className="text-center">
                  <div className="p-2 bg-cyan-100 rounded-lg mx-auto w-fit mb-1">
                    <Loader className="h-4 w-4 text-cyan-600" />
                  </div>
                  <p className="text-lg font-medium text-gray-900">{statusCounts['processing'] || 0}</p>
                  <p className="text-xs text-gray-500">处理中</p>
                </div>
                {/* 预定完成 */}
                <div className="text-center">
                  <div className="p-2 bg-green-100 rounded-lg mx-auto w-fit mb-1">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                  </div>
                  <p className="text-lg font-medium text-gray-900">{statusCounts['completed'] || 0}</p>
                  <p className="text-xs text-gray-500">预定完成</p>
                </div>
                {/* 预定失败 */}
                <div className="text-center">
                  <div className="p-2 bg-red-100 rounded-lg mx-auto w-fit mb-1">
                    <XCircle className="h-4 w-4 text-red-600" />
                  </div>
                  <p className="text-lg font-medium text-gray-900">{statusCounts['failed'] || 0}</p>
                  <p className="text-xs text-gray-500">预定失败</p>
                </div>
                {/* 完成金额 */}
                <div className="text-center">
                  <div className="p-2 bg-purple-100 rounded-lg mx-auto w-fit mb-1">
                    <DollarSign className="h-4 w-4 text-purple-600" />
                  </div>
                  <p className="text-lg font-medium text-gray-900">¥{projectStats?.completed_amount || '0'}</p>
                  <p className="text-xs text-gray-500">完成金额</p>
                </div>
              </div>
            </div>
          </Card>
        )}

        <Card className="bg-white border border-gray-200 rounded-lg shadow-sm">
          <div className="border-b border-gray-200 p-4">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-2">
                <Eye className="h-5 w-5 text-gray-600" />
                <h3 className="text-lg font-medium text-gray-900">所有订单</h3>
                {allOrdersTotal > 0 && (
                  <span className="ml-2 bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded-full">
                    {allOrdersTotal}
                  </span>
                )}
              </div>
              <div className="flex items-center gap-2">
              </div>
            </div>
            
            {/* 搜索区域 */}
            <div className="flex flex-col gap-3">
              {/* 第一行：状态筛选 */}
              <div className="flex items-center gap-3">
                <span className="text-sm font-medium text-gray-700 whitespace-nowrap">状态筛选：</span>
                <div className="flex flex-wrap gap-2">
                  {[
                    { value: 'check_failed', label: '验证失败', color: 'bg-amber-100 text-amber-700' },
                    { value: 'initial', label: '待提交', color: 'bg-gray-100 text-gray-600' },
                    { value: 'submitted', label: '已提交', color: 'bg-blue-100 text-blue-700' },
                    { value: 'processing', label: '处理中', color: 'bg-cyan-100 text-cyan-700' },
                    { value: 'completed', label: '预定完成', color: 'bg-green-100 text-green-700' },
                    { value: 'failed', label: '预定失败', color: 'bg-red-100 text-red-700' },
                  ].map((status) => (
                    <label
                      key={status.value}
                      className="flex items-center gap-1 cursor-pointer hover:bg-gray-50 p-1 rounded"
                    >
                      <input
                        type="checkbox"
                        checked={selectedStatuses.includes(status.value)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setSelectedStatuses([...selectedStatuses, status.value]);
                          } else {
                            setSelectedStatuses(selectedStatuses.filter(s => s !== status.value));
                          }
                        }}
                        className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
                      />
                      <span className={`px-2 py-1 rounded-full text-xs ${status.color}`}>
                        {status.label}
                      </span>
                    </label>
                  ))}
                </div>
              </div>
              
              {/* 第二行：搜索输入框 */}
              <div className="flex flex-col md:flex-row md:items-center gap-3">
                <div className="flex items-center gap-2 flex-1 max-w-xs">
                  <label className="text-sm text-gray-600 whitespace-nowrap min-w-fit">出行人姓名：</label>
                  <input
                    type="text"
                    placeholder="请输入出行人姓名"
                    value={searchTravelerName}
                    onChange={(e) => setSearchTravelerName(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                    className="flex-1 h-9 px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                  />
                </div>
                
                <div className="flex items-center gap-2 flex-1 max-w-xs">
                  <label className="text-sm text-gray-600 whitespace-nowrap min-w-fit">手机号码：</label>
                  <input
                    type="text"
                    placeholder="请输入手机号码"
                    value={searchMobilePhone}
                    onChange={(e) => setSearchMobilePhone(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                    className="flex-1 h-9 px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                  />
                </div>
                
                <div className="flex items-center gap-2 flex-1 max-w-xs">
                  <label className="text-sm text-gray-600 whitespace-nowrap min-w-fit">联系人手机：</label>
                  <input
                    type="text"
                    placeholder="请输入联系人手机号"
                    value={searchContactPhone}
                    onChange={(e) => setSearchContactPhone(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                    className="flex-1 h-9 px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                  />
                </div>

                <div className="flex items-center gap-2 ml-auto">
                  <Button
                    size="sm"
                    onClick={handleSearch}
                    disabled={allOrdersLoading}
                    className="bg-blue-600 hover:bg-blue-700 text-white h-9"
                  >
                    <Search className="h-4 w-4 mr-2" />
                    搜索
                  </Button>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleClearSearch}
                    disabled={allOrdersLoading}
                    className="h-9"
                  >
                    <X className="h-4 w-4 mr-2" />
                    重置
                  </Button>

                  {allOrdersTotal > 0 && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={exportAllOrders}
                      disabled={allOrdersLoading}
                      className="h-9"
                      title="导出当前筛选条件下的所有订单数据"
                    >
                      <Download className="h-4 w-4 mr-2" />
                      导出
                    </Button>
                  )}
                </div>
              </div>
            </div>
          </div>
          <div className="p-0">
            {allOrdersLoading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <p className="text-gray-600">加载中...</p>
              </div>
            ) : allOrders.length > 0 ? (
              <>
                <div className="overflow-x-auto custom-scrollbar" style={{ 
                  scrollbarWidth: 'auto', 
                  scrollbarColor: '#cbd5e1 #f1f5f9'
                }}>
                  <style dangerouslySetInnerHTML={{
                    __html: `
                      .custom-scrollbar::-webkit-scrollbar {
                        height: 12px;
                        background-color: #f1f5f9;
                      }
                      .custom-scrollbar::-webkit-scrollbar-track {
                        background-color: #f1f5f9;
                        border-radius: 6px;
                      }
                      .custom-scrollbar::-webkit-scrollbar-thumb {
                        background-color: #cbd5e1;
                        border-radius: 6px;
                        border: 2px solid #f1f5f9;
                      }
                      .custom-scrollbar::-webkit-scrollbar-thumb:hover {
                        background-color: #94a3b8;
                      }
                    `
                  }} />
                  <table className="w-full text-xs" style={{ minWidth: '2700px' }}>
                    <thead className="bg-gray-50 border-b border-gray-200">
                      <tr>
                        <th className="text-center p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '60px' }}>序号</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>状态</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>预定类型</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>出行人姓名</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>出行人姓</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>出行人名</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>国籍</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '60px' }}>性别</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>出生日期</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>证件类型</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '150px' }}>证件号码</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>证件有效期至</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '120px' }}>手机号</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>国际区号</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>出行日期</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '120px' }}>出发站名</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '120px' }}>到达站名</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>车次</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>座位类型</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>出发时间</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>到达时间</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '120px' }}>成本中心</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '150px' }}>行程提交项</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>联系人</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '120px' }}>联系人手机号</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '150px' }}>联系人邮箱</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>审批参考人</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '150px' }}>公司名称</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>代订人</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '150px' }}>出票短信</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>金额</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '120px' }}>订单号</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '120px' }}>账单号</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '150px' }}>失败原因</th>
                        <th className="text-center p-2 font-medium text-gray-900 text-xs whitespace-nowrap sticky right-0 bg-gray-50 border-l border-gray-200" style={{ minWidth: '120px' }}>操作</th>
                      </tr>
                    </thead>
                    <tbody>
                      {allOrders.map((order, index) => (
                        <tr key={order.id} className="border-b border-gray-100 hover:bg-gray-50">
                          <td className="p-2 text-xs text-gray-900 whitespace-nowrap text-center">{(allOrdersPage - 1) * allOrdersPageSize + index + 1}</td>
                          <td className="p-2 text-xs whitespace-nowrap">{getOrderStatusDisplay(order.order_status)}</td>
                          <td className="p-2 text-xs whitespace-nowrap">{getOrderTypeDisplay(order.order_type)}</td>
                          <td className="p-2 text-xs font-medium text-gray-900 whitespace-nowrap">{order.traveler_full_name || '-'}</td>
                          <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.traveler_surname || '-'}</td>
                          <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.traveler_given_name || '-'}</td>
                          <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.nationality || '-'}</td>
                          <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.gender || '-'}</td>
                          <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.birth_date || '-'}</td>
                          <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.id_type || '-'}</td>
                          <td className="p-2 text-xs text-gray-600 font-mono whitespace-nowrap">{order.id_number || '-'}</td>
                          <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.id_expiry_date || '-'}</td>
                          <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.mobile_phone || '-'}</td>
                          <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.mobile_phone_country_code || '-'}</td>
                          <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.travel_date || '-'}</td>
                          <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.departure_station || '-'}</td>
                          <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.arrival_station || '-'}</td>
                          <td className="p-2 text-xs font-medium text-blue-600 whitespace-nowrap">{order.train_number || '-'}</td>
                          <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.seat_type || '-'}</td>
                          <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.departure_time || '-'}</td>
                          <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.arrival_time || '-'}</td>
                          <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.cost_center || '-'}</td>
                          <td className="p-2 text-xs text-gray-600 whitespace-nowrap" title={order.trip_submission_item || '-'}>
                            {order.trip_submission_item ? (order.trip_submission_item.length > 20 ? order.trip_submission_item.substring(0, 20) + '...' : order.trip_submission_item) : '-'}
                          </td>
                          <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.contact_person || '-'}</td>
                          <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.contact_phone || '-'}</td>
                          <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.contact_email || '-'}</td>
                          <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.approval_reference || '-'}</td>
                          <td className="p-2 text-xs text-gray-600 whitespace-nowrap" title={order.company_name || '-'}>
                            {order.company_name ? (order.company_name.length > 15 ? order.company_name.substring(0, 15) + '...' : order.company_name) : '-'}
                          </td>
                          <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.booking_agent || '-'}</td>
                          <td className="p-2 text-xs text-gray-600 whitespace-nowrap" title={order.ticket_sms || '-'}>
                            {order.ticket_sms ? (order.ticket_sms.length > 15 ? order.ticket_sms.substring(0, 15) + '...' : order.ticket_sms) : '-'}
                          </td>
                          <td className="p-2 text-xs text-gray-900 font-medium whitespace-nowrap">
                            {formatAmount(order.amount)}
                          </td>
                          <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.order_number || '-'}</td>
                          <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.bill_number || '-'}</td>
                          <td className="p-2 text-xs text-gray-600 whitespace-nowrap relative group cursor-help">
                            {order.fail_reason ? (
                              <>
                                <span className={`${order.fail_reason.length > 15 ? 'border-b border-dotted border-gray-400' : ''}`}>
                                  {order.fail_reason.length > 15 ? order.fail_reason.substring(0, 15) + '...' : order.fail_reason}
                                </span>
                                {order.fail_reason.length > 15 && (
                                  <div className="invisible group-hover:visible absolute bottom-full mb-1 left-0 bg-gray-800 text-white text-xs rounded px-2 py-1 whitespace-normal max-w-xs z-50">
                                    {order.fail_reason}
                                  </div>
                                )}
                              </>
                            ) : (
                              '-'
                            )}
                          </td>
                          <td className="p-2 text-xs whitespace-nowrap sticky right-0 bg-white border-l border-gray-200 z-20">
                            <div className="flex items-center gap-1">
                              <Button
                                variant="ghost"
                                size="icon"
                                title="查看详情"
                                className="h-6 w-6 p-0"
                                onClick={() => handleViewOrder(order)}
                              >
                                <Eye className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="icon"
                                title="编辑订单"
                                className="h-6 w-6 p-0 text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                                onClick={() => handleEditOrder(order)}
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                              {/* 只有验证失败和待提交的订单可以删除 */}
                              {(order.order_status === 'check_failed' || order.order_status === 'initial') && (
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  title="删除订单"
                                  className="h-6 w-6 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                                  onClick={() => handleDeleteOrder(order)}
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              )}
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
                
                {/* 分页 - 始终显示 */}
                <div className="flex items-center justify-between p-4 border-t border-gray-200">
                  <div className="text-sm text-gray-700">
                    {allOrdersTotal > 0 ? (
                      <>显示第 {(allOrdersPage - 1) * allOrdersPageSize + 1} 到 {Math.min(allOrdersPage * allOrdersPageSize, allOrdersTotal)} 条，共 {allOrdersTotal} 条记录</>
                    ) : (
                      <>共 0 条记录</>
                    )}
                  </div>
                  <div className="flex items-center space-x-4">
                    {/* 每页显示数量选择 */}
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-gray-700">每页显示:</span>
                      <select
                        value={allOrdersPageSize}
                        onChange={(e) => {
                          const newPageSize = parseInt(e.target.value);
                          setAllOrdersPageSize(newPageSize);
                          // 页面重置和数据重载由useEffect自动处理
                        }}
                        className="text-sm border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500"
                        disabled={allOrdersLoading}
                      >
                        <option value={5}>5条</option>
                        <option value={10}>10条</option>
                        <option value={20}>20条</option>
                        <option value={50}>50条</option>
                      </select>
                    </div>
                    
                    {/* 分页导航 - 只在有多页时显示 */}
                    {Math.ceil(allOrdersTotal / allOrdersPageSize) > 1 && (
                      <div className="flex items-center space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => loadAllOrders(1)}
                          disabled={allOrdersPage <= 1 || allOrdersLoading}
                        >
                          首页
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => loadAllOrders(allOrdersPage - 1)}
                          disabled={allOrdersPage <= 1 || allOrdersLoading}
                        >
                          上一页
                        </Button>
                        
                        {/* 页码显示 */}
                        <div className="flex items-center space-x-1">
                          {(() => {
                            const totalPages = Math.ceil(allOrdersTotal / allOrdersPageSize);
                            const pages = [];
                            const maxVisiblePages = 5;
                            
                            let startPage = Math.max(1, allOrdersPage - Math.floor(maxVisiblePages / 2));
                            let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
                            
                            if (endPage - startPage + 1 < maxVisiblePages) {
                              startPage = Math.max(1, endPage - maxVisiblePages + 1);
                            }
                            
                            for (let i = startPage; i <= endPage; i++) {
                              pages.push(
                                <button
                                  key={i}
                                  onClick={() => loadAllOrders(i)}
                                  disabled={allOrdersLoading}
                                  className={`px-3 py-1 text-sm border rounded ${
                                    i === allOrdersPage
                                      ? 'bg-blue-600 text-white border-blue-600'
                                      : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                                  } ${allOrdersLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
                                >
                                  {i}
                                </button>
                              );
                            }
                            
                            return pages;
                          })()}
                        </div>
                        
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => loadAllOrders(allOrdersPage + 1)}
                          disabled={allOrdersPage >= Math.ceil(allOrdersTotal / allOrdersPageSize) || allOrdersLoading}
                        >
                          下一页
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => loadAllOrders(Math.ceil(allOrdersTotal / allOrdersPageSize))}
                          disabled={allOrdersPage >= Math.ceil(allOrdersTotal / allOrdersPageSize) || allOrdersLoading}
                        >
                          末页
                        </Button>
                      </div>
                    )}
                    
                    <div className="text-sm text-gray-700">
                      {allOrdersTotal > 0 ? (
                        <>第 {allOrdersPage} 页，共 {Math.ceil(allOrdersTotal / allOrdersPageSize)} 页</>
                      ) : (
                        <>第 1 页，共 1 页</>
                      )}
                    </div>
                  </div>
                </div>
              </>
            ) : (
              <div className="text-center py-8">
                <Building className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">暂无订单数据</h3>
                <p className="text-gray-500">该项目还没有火车票订单</p>
              </div>
            )}
            </div>
          </Card>
        </div>
    );
  };

  // 获取任务统计信息
  const getTaskStatistics = async (taskId: string) => {
    try {
      // 获取该任务相关的订单统计
      const response = await api.get(`/train-order/task/${taskId}/stats`);
      return response.data || {
        order_count: 0,
        total_amount: 0,
        total_people: 0,
        completed_orders: 0,
        failed_orders: 0
      };
    } catch (error) {
      console.error(`获取任务 ${taskId} 统计信息失败:`, error);
      return {
        order_count: 0,
        total_amount: 0,
        total_people: 0,
        completed_orders: 0,
        failed_orders: 0
      };
    }
  };

  // 加载预订任务
  const loadBookingTasks = async () => {
    if (!projectId) return;
    
    setBookingTasksLoading(true);
    try {
      const response = await ProjectTaskService.getTasksByProject(parseInt(projectId), {
        page: 1,
        page_size: 100
      });
      
      // 只获取火车票预订类型的任务，按创建时间倒序排列
      const trainTasks = response.items
        .filter((task: ProjectTask) => task.task_type === '火车票预订')
        .sort((a: ProjectTask, b: ProjectTask) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
      
      setBookingTasks(trainTasks);

      // 获取每个任务的统计信息
      const statsPromises = trainTasks.map(async (task) => {
        const stats = await getTaskStatistics(task.task_id);
        return { taskId: task.task_id, stats };
      });

      const statsResults = await Promise.all(statsPromises);
      const statsMap = statsResults.reduce((acc, { taskId, stats }) => {
        acc[taskId] = stats;
        return acc;
      }, {} as {[key: string]: any});

      setTaskStats(statsMap);
    } catch (error) {
      console.error('加载预订任务失败:', error);
      toast({
        title: "加载失败",
        description: "无法加载预订任务列表",
        variant: "destructive",
      });
    } finally {
      setBookingTasksLoading(false);
    }
  };

  // 格式化金额显示
  // 格式化金额显示（暂时保留，后续可能使用）
  // const formatAmountDisplay = (amount: number) => {
  //   if (amount >= 1000) {
  //     return `Y${(amount / 1000).toFixed(1)}K`;
  //   }
  //   return `Y${amount}`;
  // };

  // 获取预订进度（暂时保留，后续可能使用）
  // const getBookingProgress = (stats: any) => {
  //   if (!stats || stats.order_count === 0) return 0;
  //   return Math.round((stats.completed_orders / stats.order_count) * 100);
  // };

  // 基于project_tasks表的task_status字段获取状态显示
  const getProjectTaskStatusDisplay = (task_status: string) => {
    const statusMap = {
      'pending': { text: '待处理', color: 'bg-yellow-100 text-yellow-700' },
      'submitted': { text: '已提交', color: 'bg-blue-100 text-blue-700' },
      'processing': { text: '处理中', color: 'bg-blue-100 text-blue-700' },
      'in_progress': { text: '进行中', color: 'bg-blue-100 text-blue-700' },
      'completed': { text: '已完成', color: 'bg-green-100 text-green-700' },
      'failed': { text: '失败', color: 'bg-red-100 text-red-700' },
      'cancelled': { text: '已取消', color: 'bg-gray-100 text-gray-700' }
    };
    return statusMap[task_status as keyof typeof statusMap] || { text: task_status, color: 'bg-gray-100 text-gray-600' };
  };

  // 获取任务类型配置
  const getTaskTypeConfig = (task_type: string) => {
    const typeConfigs = {
      '火车票预订': {
        icon: Building,
        color: 'text-blue-600',
        bgColor: 'bg-blue-50',
        borderColor: 'border-blue-200',
        buttonColor: 'bg-blue-600 hover:bg-blue-700'
      },
      '飞机票预订': {
        icon: '✈️',
        color: 'text-green-600',
        bgColor: 'bg-green-50',
        borderColor: 'border-green-200',
        buttonColor: 'bg-green-600 hover:bg-green-700'
      },
      '机票预订': {
        icon: '✈️',
        color: 'text-green-600',
        bgColor: 'bg-green-50',
        borderColor: 'border-green-200',
        buttonColor: 'bg-green-600 hover:bg-green-700'
      },
      '酒店预订': {
        icon: '🏨',
        color: 'text-purple-600',
        bgColor: 'bg-purple-50',
        borderColor: 'border-purple-200',
        buttonColor: 'bg-purple-600 hover:bg-purple-700'
      },
      '交通安排': {
        icon: '🚗',
        color: 'text-orange-600',
        bgColor: 'bg-orange-50',
        borderColor: 'border-orange-200',
        buttonColor: 'bg-orange-600 hover:bg-orange-700'
      },
      '会议安排': {
        icon: '🏢',
        color: 'text-indigo-600',
        bgColor: 'bg-indigo-50',
        borderColor: 'border-indigo-200',
        buttonColor: 'bg-indigo-600 hover:bg-indigo-700'
      }
    };
    return typeConfigs[task_type as keyof typeof typeConfigs] || {
      icon: FileText,
      color: 'text-gray-600',
      bgColor: 'bg-gray-50',
      borderColor: 'border-gray-200',
      buttonColor: 'bg-gray-600 hover:bg-gray-700'
    };
  };

  // 获取任务状态标签（结合project_tasks状态和统计数据）
  const getTaskStatusLabel = (task: ProjectTask, stats: any) => {
    // 优先显示project_tasks表的task_status
    const projectTaskStatus = getProjectTaskStatusDisplay(task.task_status);
    
    // 如果有统计数据，可以进一步细化状态
    if (stats && stats.order_count > 0) {
      if (stats.failed_orders > 0) {
        return { text: '部分失败', color: 'bg-red-100 text-red-700' };
      }
      if (stats.completed_orders === stats.order_count && task.task_status === 'completed') {
        return { text: '全部完成', color: 'bg-green-100 text-green-700' };
      }
    }
    
    return projectTaskStatus;
  };

  // 渲染基于project_tasks表的任务卡片
  const renderProjectTaskCard = (task: ProjectTask) => {
    const stats = taskStats[task.task_id] || { order_count: 0, total_amount: 0, total_people: 0, completed_orders: 0, failed_orders: 0 };
    // const progress = getBookingProgress(stats);
    const statusInfo = getTaskStatusLabel(task, stats);
    const typeConfig = getTaskTypeConfig(task.task_type);

    return (
      <Card key={task.id} className={`bg-white border ${typeConfig.borderColor} hover:shadow-md hover:scale-105 transition-all duration-200 overflow-hidden`}>
        <div className="p-4">
          {/* 卡片头部 - 更紧凑的布局 */}
          <div className="flex items-start justify-between mb-3">
            <div className="flex items-center gap-2">
              <div className={`p-1.5 ${typeConfig.bgColor} rounded-md`}>
                {typeof typeConfig.icon === 'string' ? (
                  <span className="text-sm">{typeConfig.icon}</span>
                ) : (
                  <typeConfig.icon className={`h-4 w-4 ${typeConfig.color}`} />
                )}
              </div>
              <div className="flex-1 min-w-0">
                <h3 className="text-sm font-semibold text-gray-900 truncate">{task.task_type}</h3>
                <p className="text-xs text-gray-500 truncate">{task.task_title}</p>
              </div>
            </div>
            <span className={`px-2 py-0.5 rounded-full text-xs font-medium whitespace-nowrap ${statusInfo.color}`}>
              {statusInfo.text}
            </span>
          </div>

          {/* 统计数据 - 按状态显示订单数量 */}
          <div className="grid grid-cols-4 gap-2 mb-3">
            <div className="text-center">
              <div className="text-lg font-bold text-blue-600">{stats.submitted_orders || 0}</div>
              <div className="text-xs text-gray-500">已提交</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-bold text-cyan-600">{stats.processing_orders || 0}</div>
              <div className="text-xs text-gray-500">处理中</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-bold text-green-600">{stats.completed_orders || 0}</div>
              <div className="text-xs text-gray-500">预定完成</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-bold text-red-600">{stats.failed_orders || 0}</div>
              <div className="text-xs text-gray-500">预定失败</div>
            </div>
          </div>

          {/* 分割线 */}
          <div className="border-t border-gray-200 mb-3"></div>

          {/* 任务信息 - 更简洁的展示 */}
          <div className="space-y-1 mb-3">
            <div className="flex items-center justify-between text-xs">
              <span className="text-gray-500">创建人</span>
              <span className="text-gray-900 font-medium">{task.creator_name}</span>
            </div>
            <div className="flex items-center justify-between text-xs">
              <span className="text-gray-500">创建时间</span>
              <span className="text-gray-900">{new Date(task.created_at).toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })}</span>
            </div>
            {/* 特殊标记 - 更紧凑的显示，设置固定高度确保对齐 */}
            <div className="flex gap-2 mt-1 h-6 items-center">
              {(task as any).sms_notify && (
                <div className="flex items-center gap-1 px-1.5 py-0.5 bg-blue-50 rounded text-xs text-blue-600">
                  <Bell className="h-2.5 w-2.5" />
                  <span>短信</span>
                </div>
              )}
              {(task as any).has_agent && (
                <div className="flex items-center gap-1 px-1.5 py-0.5 bg-green-50 rounded text-xs text-green-600">
                  <User className="h-2.5 w-2.5" />
                  <span>代订</span>
                </div>
              )}
            </div>
          </div>

          {/* 单个操作按钮 - 更现代的设计 */}
                      <Button
              size="sm"
              className={`w-full ${typeConfig.buttonColor} text-white hover:opacity-90 transition-opacity`}
              onClick={() => {
                if (task.task_type === '火车票预订') {
                  navigate(`/task-orders/${task.task_id}`);
                } else {
                  console.log('查看任务详情:', task.task_id);
                }
              }}
            >
              <Eye className="h-4 w-4 mr-1" />
              查看详情
            </Button>
        </div>
      </Card>
    );
  };



  // 渲染任务详情内容
  const renderDetailsContent = () => {
    return (
      <>
        {/* 任务概览标题 */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-2">
            <FileText className="h-5 w-5 text-gray-600" />
            <h3 className="text-lg font-semibold text-gray-900">任务概览</h3>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={loadBookingTasks}
            disabled={bookingTasksLoading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${bookingTasksLoading ? 'animate-spin' : ''}`} />
            刷新
          </Button>
        </div>

        {/* 任务卡片网格 */}
        {bookingTasksLoading ? (
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">加载任务数据中...</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {/* 显示project_tasks表中的所有任务 */}
            {bookingTasks.length > 0 ? (
              bookingTasks.map(renderProjectTaskCard)
            ) : (
              <Card className="bg-white border border-gray-200 p-8 text-center col-span-full">
                <div className="p-4 bg-blue-50 rounded-full mx-auto w-fit mb-4">
                  <Building className="h-8 w-8 text-blue-600" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">暂无火车票预订任务</h3>
                <p className="text-gray-500 mb-4">该项目还没有创建火车票预订任务</p>
                <p className="text-sm text-gray-400">请先在"待预订"tab中上传火车票订单并创建预订任务</p>
              </Card>
            )}
          </div>
        )}
      </>
    );
  };

  // 加载异常订单
  const loadExceptionOrders = async (page: number = 1) => {
    if (!projectId) return;
    
    setExceptionOrdersLoading(true);
    try {
      // 使用异常订单独立的状态筛选，只包含验证失败和预定失败
      const statusFilter = exceptionSelectedStatuses.length > 0 ? exceptionSelectedStatuses.join(',') : 'check_failed,failed';
      
      const response = await api.get(`/train-order/project/${projectId}`, {
        params: {
          page: page.toString(),
          page_size: exceptionOrdersPageSize.toString(),
          order_status: statusFilter, // 修复：使用正确的参数名order_status
          ...(exceptionSearchTravelerName && { traveler_name: exceptionSearchTravelerName }),
          ...(exceptionSearchMobilePhone && { mobile_phone: exceptionSearchMobilePhone }),
          ...(exceptionSearchContactPhone && { contact_phone: exceptionSearchContactPhone }),
        }
      });
      
      const data = response.data as TrainOrderListResponse;
      setExceptionOrders(data.items);
      setExceptionOrdersTotal(data.total);
      setExceptionOrdersPage(page);
    } catch (error) {
      console.error('加载异常订单失败:', error);
      toast({
        title: "加载失败",
        description: "无法加载异常订单列表",
        variant: "destructive",
      });
    } finally {
      setExceptionOrdersLoading(false);
    }
  };

  const handleExceptionSearch = () => {
    loadExceptionOrders(1);
  };

  const handleClearExceptionSearch = () => {
    setExceptionSearchTravelerName('');
    setExceptionSearchMobilePhone('');
    setExceptionSearchContactPhone('');
    setExceptionSelectedStatuses(['check_failed', 'failed']); // 重置为默认异常状态
    // 数据重载由useEffect自动处理
  };

  // 加载对账单订单（只显示已完成状态的订单）
  const loadReconciliationOrders = async (page: number = 1) => {
    if (!projectId) return;

    setReconciliationOrdersLoading(true);
    try {
      const response = await api.get(`/train-order/project/${projectId}`, {
        params: {
          page: page.toString(),
          page_size: reconciliationOrdersPageSize.toString(),
          order_status: 'completed', // 只显示已完成状态的订单
          ...(reconciliationSearchTravelerName && { traveler_name: reconciliationSearchTravelerName }),
          ...(reconciliationSearchMobilePhone && { mobile_phone: reconciliationSearchMobilePhone }),
          ...(reconciliationSearchContactPhone && { contact_phone: reconciliationSearchContactPhone }),
        }
      });

      const data = response.data as TrainOrderListResponse;
      setReconciliationOrders(data.items);
      setReconciliationOrdersTotal(data.total);
      setReconciliationOrdersPage(page);
    } catch (error) {
      console.error('加载对账单订单失败:', error);
      toast({
        title: "加载失败",
        description: "无法加载对账单订单列表",
        variant: "destructive",
      });
    } finally {
      setReconciliationOrdersLoading(false);
    }
  };

  const handleReconciliationSearch = () => {
    loadReconciliationOrders(1);
  };

  const handleClearReconciliationSearch = () => {
    setReconciliationSearchTravelerName('');
    setReconciliationSearchMobilePhone('');
    setReconciliationSearchContactPhone('');
    // 数据重载由useEffect自动处理
  };

  // 导出对账单Excel
  const exportReconciliationOrders = async () => {
    if (reconciliationOrders.length === 0) {
      toast({
        title: "导出失败",
        description: "没有对账单数据可导出",
        variant: "destructive",
      });
      return;
    }

    try {
      const filename = `对账单_${project?.project_name || 'Unknown'}_${new Date().toLocaleDateString('zh-CN').replace(/\//g, '-')}.xlsx`;
      const title = `${project?.project_name || '项目' + projectId} - 对账单明细`;
      exportToExcel(reconciliationOrders, filename, title);

      toast({
        title: "导出成功",
        description: "对账单数据已成功导出",
      });
    } catch (error) {
      console.error('导出对账单失败:', error);
      toast({
        title: "导出失败",
        description: "导出对账单时发生错误",
        variant: "destructive",
      });
    }
  };

  // 导出异常订单Excel
  const exportExceptionOrders = async () => {
    if (exceptionOrders.length === 0) {
      toast({
        title: "导出失败",
        description: "没有异常订单数据可导出",
        variant: "destructive",
      });
      return;
    }

    try {
      const filename = `异常订单_${project?.project_name || 'Unknown'}_${new Date().toLocaleDateString('zh-CN').replace(/\//g, '-')}.xlsx`;
      const title = `${project?.project_name || '项目' + projectId} - 异常订单明细`;
      exportToExcel(exceptionOrders, filename, title);

      toast({
        title: "导出成功",
        description: `已导出 ${exceptionOrders.length} 条异常订单记录`,
      });
    } catch (error) {
      console.error('导出异常订单失败:', error);
      toast({
        title: "导出失败",
        description: "导出异常订单时发生错误",
        variant: "destructive",
      });
    }
  };

  // 渲染异常订单内容
  const renderExceptionOrdersContent = () => {
    // 计算异常订单统计信息
    const checkFailedCount = exceptionOrders.filter(order => order.order_status === 'check_failed').length;
    const bookingFailedCount = exceptionOrders.filter(order => order.order_status === 'failed').length;

    return (
      <div className="space-y-4">
        {/* 异常订单统计卡片 */}
        {exceptionOrdersTotal > 0 && (
          <Card className="bg-white border border-red-200">
            <div className="p-4">
              <h3 className="text-sm font-medium text-red-900 mb-3">异常订单统计</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center">
                  <div className="p-2 bg-red-100 rounded-lg mx-auto w-fit mb-1">
                    <AlertTriangle className="h-4 w-4 text-red-600" />
                  </div>
                  <p className="text-lg font-medium text-gray-900">{exceptionOrdersTotal}</p>
                  <p className="text-xs text-gray-500">异常订单总数</p>
                </div>
                <div className="text-center">
                  <div className="p-2 bg-amber-100 rounded-lg mx-auto w-fit mb-1">
                    <AlertCircle className="h-4 w-4 text-amber-600" />
                  </div>
                  <p className="text-lg font-medium text-amber-700">{checkFailedCount}</p>
                  <p className="text-xs text-gray-500">导入验证失败数</p>
                </div>
                <div className="text-center">
                  <div className="p-2 bg-red-100 rounded-lg mx-auto w-fit mb-1">
                    <X className="h-4 w-4 text-red-600" />
                  </div>
                  <p className="text-lg font-medium text-red-700">{bookingFailedCount}</p>
                  <p className="text-xs text-gray-500">预定失败订单数</p>
                </div>
              </div>
            </div>
          </Card>
        )}

        <Card className="bg-white border border-gray-200 rounded-lg shadow-sm">
          <div className="border-b border-gray-200 p-4">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-2">
                <X className="h-5 w-5 text-red-600" />
                <h3 className="text-lg font-medium text-gray-900">异常订单</h3>
                {exceptionOrdersTotal > 0 && (
                  <span className="ml-2 bg-red-100 text-red-800 text-xs font-medium px-2 py-1 rounded-full">
                    {exceptionOrdersTotal}
                  </span>
                )}
              </div>
              <div className="flex items-center gap-2">
                {/* 导出按钮已移至搜索区域 */}
              </div>
            </div>
            
            {/* 搜索区域 */}
            <div className="flex flex-col gap-3">
              {/* 第一行：状态筛选 */}
              <div className="flex items-center gap-3">
                <span className="text-sm font-medium text-gray-700 whitespace-nowrap">异常类型：</span>
                <div className="flex flex-wrap gap-2">
                  {[
                    { value: 'check_failed', label: '验证失败', color: 'bg-amber-100 text-amber-700' },
                    { value: 'failed', label: '预定失败', color: 'bg-red-100 text-red-700' },
                  ].map((status) => (
                    <label
                      key={status.value}
                      className="flex items-center gap-1 cursor-pointer hover:bg-gray-50 p-1 rounded"
                    >
                      <input
                        type="checkbox"
                        checked={exceptionSelectedStatuses.includes(status.value)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setExceptionSelectedStatuses([...exceptionSelectedStatuses, status.value]);
                          } else {
                            setExceptionSelectedStatuses(exceptionSelectedStatuses.filter(s => s !== status.value));
                          }
                        }}
                        className="w-4 h-4 text-red-600 bg-gray-100 border-gray-300 rounded focus:ring-red-500"
                      />
                      <span className={`px-2 py-1 rounded-full text-xs ${status.color}`}>
                        {status.label}
                      </span>
                    </label>
                  ))}
                </div>
              </div>
              
              {/* 第二行：搜索输入框 */}
              <div className="flex flex-col md:flex-row md:items-center gap-3 pt-2 border-t border-gray-100">
                <div className="flex items-center gap-2 flex-1 max-w-xs">
                  <label className="text-sm text-gray-600 whitespace-nowrap min-w-fit">出行人姓名：</label>
                  <input
                    type="text"
                    placeholder="请输入出行人姓名"
                    value={exceptionSearchTravelerName}
                    onChange={(e) => setExceptionSearchTravelerName(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && handleExceptionSearch()}
                    className="flex-1 h-9 px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent text-sm"
                  />
                </div>
                
                <div className="flex items-center gap-2 flex-1 max-w-xs">
                  <label className="text-sm text-gray-600 whitespace-nowrap min-w-fit">手机号码：</label>
                  <input
                    type="text"
                    placeholder="请输入手机号码"
                    value={exceptionSearchMobilePhone}
                    onChange={(e) => setExceptionSearchMobilePhone(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && handleExceptionSearch()}
                    className="flex-1 h-9 px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent text-sm"
                  />
                </div>
                
                <div className="flex items-center gap-2 flex-1 max-w-xs">
                  <label className="text-sm text-gray-600 whitespace-nowrap min-w-fit">联系人手机：</label>
                  <input
                    type="text"
                    placeholder="请输入联系人手机号"
                    value={exceptionSearchContactPhone}
                    onChange={(e) => setExceptionSearchContactPhone(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && handleExceptionSearch()}
                    className="flex-1 h-9 px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent text-sm"
                  />
                </div>

                <div className="flex items-center gap-2 ml-auto">
                  <Button
                    size="sm"
                    onClick={handleExceptionSearch}
                    disabled={exceptionOrdersLoading}
                    className="bg-red-600 hover:bg-red-700 text-white h-9"
                  >
                    <Search className="h-4 w-4 mr-2" />
                    搜索
                  </Button>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleClearExceptionSearch}
                    disabled={exceptionOrdersLoading}
                    className="h-9"
                  >
                    <X className="h-4 w-4 mr-2" />
                    重置
                  </Button>

                  {exceptionOrdersTotal > 0 && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={exportExceptionOrders}
                      disabled={exceptionOrdersLoading}
                      className="h-9"
                      title="导出异常订单数据"
                    >
                      <Download className="h-4 w-4 mr-2" />
                      导出
                    </Button>
                  )}
                </div>
              </div>
            </div>
          </div>
          <div className="p-0">
            {exceptionOrdersLoading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-600 mx-auto mb-4"></div>
                <p className="text-gray-600">加载中...</p>
              </div>
            ) : exceptionOrders.length > 0 ? (
              <>
                <div className="overflow-x-auto custom-scrollbar" style={{ 
                  scrollbarWidth: 'auto', 
                  scrollbarColor: '#cbd5e1 #f1f5f9'
                }}>
                  <table className="w-full text-xs" style={{ minWidth: '2600px' }}>
                    <thead className="bg-red-50 border-b border-red-200">
                      <tr>
                        <th className="text-center p-2 font-medium text-red-900 text-xs whitespace-nowrap" style={{ minWidth: '60px' }}>序号</th>
                        <th className="text-left p-2 font-medium text-red-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>状态</th>
                        <th className="text-left p-2 font-medium text-red-900 text-xs whitespace-nowrap" style={{ minWidth: '120px' }}>失败原因</th>
                        <th className="text-left p-2 font-medium text-red-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>出行人姓名</th>
                        <th className="text-left p-2 font-medium text-red-900 text-xs whitespace-nowrap" style={{ minWidth: '150px' }}>证件号码</th>
                        <th className="text-left p-2 font-medium text-red-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>出行日期</th>
                        <th className="text-left p-2 font-medium text-red-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>出发站</th>
                        <th className="text-left p-2 font-medium text-red-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>到达站</th>
                        <th className="text-left p-2 font-medium text-red-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>车次</th>
                        <th className="text-left p-2 font-medium text-red-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>座位类型</th>
                        <th className="text-left p-2 font-medium text-red-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>金额</th>
                        <th className="text-left p-2 font-medium text-red-900 text-xs whitespace-nowrap" style={{ minWidth: '120px' }}>手机号</th>
                        <th className="text-left p-2 font-medium text-red-900 text-xs whitespace-nowrap" style={{ minWidth: '120px' }}>联系人</th>
                        <th className="text-left p-2 font-medium text-red-900 text-xs whitespace-nowrap sticky right-0 bg-red-50 border-l border-red-200" style={{ minWidth: '100px' }}>操作</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200">
                      {exceptionOrders.map((order, index) => (
                        <tr key={order.id} className="hover:bg-gray-50">
                          <td className="p-2 text-xs text-gray-900 whitespace-nowrap text-center">{(exceptionOrdersPage - 1) * exceptionOrdersPageSize + index + 1}</td>
                          <td className="p-2 text-xs whitespace-nowrap">
                            {getOrderStatusDisplay(order.order_status)}
                          </td>
                          <td className="p-2 text-xs text-gray-600 whitespace-nowrap relative group cursor-help">
                            {order.fail_reason ? (
                              <>
                                <span className={`${order.fail_reason.length > 15 ? 'border-b border-dotted border-gray-400' : ''}`}>
                                  {order.fail_reason.length > 15 ? order.fail_reason.substring(0, 15) + '...' : order.fail_reason}
                                </span>
                                {order.fail_reason.length > 15 && (
                                  <div className="invisible group-hover:visible absolute bottom-full mb-1 left-0 bg-gray-800 text-white text-xs rounded px-2 py-1 whitespace-normal max-w-xs z-50">
                                    {order.fail_reason}
                                  </div>
                                )}
                              </>
                            ) : (
                              '-'
                            )}
                          </td>
                          <td className="p-2 text-xs text-gray-900 whitespace-nowrap font-medium">{order.traveler_full_name}</td>
                          <td className="p-2 text-xs text-gray-600 whitespace-nowrap font-mono">{order.id_number || '-'}</td>
                          <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.travel_date || '-'}</td>
                          <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.departure_station || '-'}</td>
                          <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.arrival_station || '-'}</td>
                          <td className="p-2 text-xs text-gray-600 whitespace-nowrap font-medium text-blue-600">{order.train_number || '-'}</td>
                          <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.seat_type || '-'}</td>
                          <td className="p-2 text-xs text-gray-900 whitespace-nowrap font-medium">{formatAmount(order.amount)}</td>
                          <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.mobile_phone || '-'}</td>
                          <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.contact_person || '-'}</td>
                          <td className="p-2 text-xs whitespace-nowrap sticky right-0 bg-white border-l border-gray-200 z-20">
                            <div className="flex items-center gap-1">
                              <Button
                                variant="ghost"
                                size="icon"
                                title="查看详情"
                                className="h-6 w-6 p-0"
                                onClick={() => handleViewOrder(order)}
                              >
                                <Eye className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="icon"
                                title="编辑订单"
                                className="h-6 w-6 p-0 text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                                onClick={() => handleEditOrder(order)}
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                              {/* 只有验证失败和待提交的订单可以删除 */}
                              {(order.order_status === 'check_failed' || order.order_status === 'initial') && (
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  title="删除订单"
                                  className="h-6 w-6 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                                  onClick={() => handleDeleteOrder(order)}
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              )}
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
                
                {/* 分页 */}
                {Math.ceil(exceptionOrdersTotal / exceptionOrdersPageSize) > 1 && (
                  <div className="flex items-center justify-between p-4 border-t border-gray-200">
                    <div className="text-sm text-gray-700">
                      显示第 {(exceptionOrdersPage - 1) * exceptionOrdersPageSize + 1} 到 {Math.min(exceptionOrdersPage * exceptionOrdersPageSize, exceptionOrdersTotal)} 条，共 {exceptionOrdersTotal} 条记录
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => loadExceptionOrders(exceptionOrdersPage - 1)}
                        disabled={exceptionOrdersPage <= 1 || exceptionOrdersLoading}
                      >
                        上一页
                      </Button>
                      <span className="text-sm text-gray-700">
                        第 {exceptionOrdersPage} 页，共 {Math.ceil(exceptionOrdersTotal / exceptionOrdersPageSize)} 页
                      </span>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => loadExceptionOrders(exceptionOrdersPage + 1)}
                        disabled={exceptionOrdersPage >= Math.ceil(exceptionOrdersTotal / exceptionOrdersPageSize) || exceptionOrdersLoading}
                      >
                        下一页
                      </Button>
                    </div>
                  </div>
                )}
              </>
            ) : (
              <div className="text-center py-8">
                <CheckCircle className="h-12 w-12 text-green-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">暂无异常订单</h3>
                <p className="text-gray-500">该项目没有失败的订单，所有订单处理正常</p>
              </div>
            )}
            </div>
          </Card>
        </div>
    );
  };

  // 渲染对账单内容
  const renderReconciliationContent = () => {
    // 计算对账单统计信息
    const totalAmount = reconciliationOrders.reduce((sum, order) => {
      const amount = parseFloat(order.amount) || 0;
      return sum + amount;
    }, 0);

    return (
      <div className="space-y-4">
        {/* 对账单统计卡片 */}
        {reconciliationOrdersTotal > 0 && (
          <Card className="bg-white border border-green-200">
            <div className="p-4">
              <h3 className="text-sm font-medium text-green-900 mb-3">对账单统计</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center">
                  <div className="p-2 bg-green-100 rounded-lg mx-auto w-fit mb-1">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                  </div>
                  <p className="text-lg font-medium text-gray-900">{reconciliationOrdersTotal}</p>
                  <p className="text-xs text-gray-500">已完成订单总数</p>
                </div>
                <div className="text-center">
                  <div className="p-2 bg-blue-100 rounded-lg mx-auto w-fit mb-1">
                    <DollarSign className="h-4 w-4 text-blue-600" />
                  </div>
                  <p className="text-lg font-medium text-blue-700">¥{totalAmount.toLocaleString()}</p>
                  <p className="text-xs text-gray-500">总金额</p>
                </div>
                <div className="text-center">
                  <div className="p-2 bg-purple-100 rounded-lg mx-auto w-fit mb-1">
                    <CreditCard className="h-4 w-4 text-purple-600" />
                  </div>
                  <p className="text-lg font-medium text-purple-700">¥{parseFloat((totalAmount / Math.max(reconciliationOrdersTotal, 1)).toFixed(2))}</p>
                  <p className="text-xs text-gray-500">平均金额</p>
                </div>
              </div>
            </div>
          </Card>
        )}

        <Card className="bg-white border border-gray-200 rounded-lg shadow-sm">
          <div className="border-b border-gray-200 p-4">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-2">
                <CreditCard className="h-5 w-5 text-green-600" />
                <h3 className="text-lg font-medium text-gray-900">对账单</h3>
                {reconciliationOrdersTotal > 0 && (
                  <span className="ml-2 bg-green-100 text-green-800 text-xs font-medium px-2 py-1 rounded-full">
                    {reconciliationOrdersTotal}
                  </span>
                )}
              </div>
            </div>

            {/* 搜索区域 */}
            <div className="flex flex-col gap-3">
              {/* 搜索输入框和操作按钮 */}
              <div className="flex flex-col lg:flex-row gap-4 items-end">
                {/* 左侧：搜索框区域 */}
                <div className="flex-1 grid grid-cols-1 md:grid-cols-3 gap-3">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">出行人姓名</label>
                    <input
                      type="text"
                      placeholder="请输入出行人姓名"
                      value={reconciliationSearchTravelerName}
                      onChange={(e) => setReconciliationSearchTravelerName(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && handleReconciliationSearch()}
                      className="w-full h-9 px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent text-sm"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">手机号码</label>
                    <input
                      type="text"
                      placeholder="请输入手机号码"
                      value={reconciliationSearchMobilePhone}
                      onChange={(e) => setReconciliationSearchMobilePhone(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && handleReconciliationSearch()}
                      className="w-full h-9 px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent text-sm"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">联系人手机号</label>
                    <input
                      type="text"
                      placeholder="请输入联系人手机号"
                      value={reconciliationSearchContactPhone}
                      onChange={(e) => setReconciliationSearchContactPhone(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && handleReconciliationSearch()}
                      className="w-full h-9 px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent text-sm"
                    />
                  </div>
                </div>

                {/* 右侧：操作按钮区域 */}
                <div className="flex gap-2 flex-shrink-0">
                  <Button
                    onClick={handleReconciliationSearch}
                    size="sm"
                    className="bg-green-600 hover:bg-green-700 text-white flex items-center gap-1"
                  >
                    <Search className="h-4 w-4" />
                    搜索
                  </Button>
                  <Button
                    onClick={handleClearReconciliationSearch}
                    variant="outline"
                    size="sm"
                    className="text-gray-600 hover:text-gray-800"
                  >
                    重置
                  </Button>
                  <Button
                    onClick={exportReconciliationOrders}
                    variant="outline"
                    size="sm"
                    disabled={reconciliationOrders.length === 0}
                    className="flex items-center gap-1"
                  >
                    <Download className="h-4 w-4" />
                    导出
                  </Button>
                </div>
              </div>
            </div>
          </div>

          <div className="p-0">
            {reconciliationOrdersLoading ? (
              <div className="flex items-center justify-center py-8">
                <RefreshCw className="h-6 w-6 animate-spin text-green-500 mr-2" />
                <span className="text-gray-600">加载对账单数据中...</span>
              </div>
            ) : reconciliationOrders.length > 0 ? (
              <>
                <div className="overflow-x-auto custom-scrollbar" style={{
                  scrollbarWidth: 'auto',
                  scrollbarColor: '#cbd5e1 #f1f5f9'
                }}>
                  <style dangerouslySetInnerHTML={{
                    __html: `
                      .custom-scrollbar::-webkit-scrollbar {
                        height: 8px;
                        background-color: #f1f5f9;
                      }
                      .custom-scrollbar::-webkit-scrollbar-thumb {
                        background-color: #cbd5e1;
                        border-radius: 4px;
                      }
                      .custom-scrollbar::-webkit-scrollbar-thumb:hover {
                        background-color: #94a3b8;
                      }
                    `
                  }} />
                  <table className="w-full text-xs" style={{ minWidth: '2700px' }}>
                    <thead className="bg-green-50 border-b border-green-200">
                      <tr>
                        <th className="text-center p-2 font-medium text-green-900 text-xs whitespace-nowrap" style={{ minWidth: '60px' }}>序号</th>
                        <th className="text-left p-2 font-medium text-green-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>出行人姓名</th>
                        <th className="text-left p-2 font-medium text-green-900 text-xs whitespace-nowrap" style={{ minWidth: '120px' }}>证件号码</th>
                        <th className="text-left p-2 font-medium text-green-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>手机号</th>
                        <th className="text-left p-2 font-medium text-green-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>出行日期</th>
                        <th className="text-left p-2 font-medium text-green-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>出发站</th>
                        <th className="text-left p-2 font-medium text-green-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>到达站</th>
                        <th className="text-left p-2 font-medium text-green-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>车次</th>
                        <th className="text-left p-2 font-medium text-green-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>座位类型</th>
                        <th className="text-left p-2 font-medium text-green-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>联系人</th>
                        <th className="text-left p-2 font-medium text-green-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>联系人手机</th>
                        <th className="text-left p-2 font-medium text-green-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>金额</th>
                        <th className="text-left p-2 font-medium text-green-900 text-xs whitespace-nowrap" style={{ minWidth: '120px' }}>公司名称</th>
                        <th className="text-left p-2 font-medium text-green-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>代订人</th>
                        <th className="text-left p-2 font-medium text-green-900 text-xs whitespace-nowrap" style={{ minWidth: '120px' }}>订单号</th>
                        <th className="text-left p-2 font-medium text-green-900 text-xs whitespace-nowrap" style={{ minWidth: '120px' }}>账单号</th>
                        <th className="text-left p-2 font-medium text-green-900 text-xs whitespace-nowrap" style={{ minWidth: '120px' }}>创建时间</th>
                        <th className="text-center p-2 font-medium text-green-900 text-xs whitespace-nowrap" style={{ minWidth: '120px' }}>操作</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200">
                      {reconciliationOrders.map((order, index) => (
                        <tr key={order.id} className="hover:bg-gray-50">
                          <td className="text-center p-2 text-gray-900">{(reconciliationOrdersPage - 1) * reconciliationOrdersPageSize + index + 1}</td>
                          <td className="p-2 text-gray-900">{order.traveler_full_name || '-'}</td>
                          <td className="p-2 text-gray-900 font-mono">{order.id_number || '-'}</td>
                          <td className="p-2 text-gray-900">{order.mobile_phone || '-'}</td>
                          <td className="p-2 text-gray-900">{order.travel_date || '-'}</td>
                          <td className="p-2 text-gray-900">{order.departure_station || '-'}</td>
                          <td className="p-2 text-gray-900">{order.arrival_station || '-'}</td>
                          <td className="p-2 text-blue-600 font-medium">{order.train_number || '-'}</td>
                          <td className="p-2 text-gray-900">{order.seat_type || '-'}</td>
                          <td className="p-2 text-gray-900">{order.contact_person || '-'}</td>
                          <td className="p-2 text-gray-900">{order.contact_phone || '-'}</td>
                          <td className="p-2 text-green-600 font-medium">¥{parseFloat(order.amount) || 0}</td>
                          <td className="p-2 text-gray-900">{order.company_name || '-'}</td>
                          <td className="p-2 text-gray-900">{order.booking_agent || '-'}</td>
                          <td className="p-2 text-gray-900 font-mono">{order.order_number || '-'}</td>
                          <td className="p-2 text-gray-900 font-mono">{order.bill_number || '-'}</td>
                          <td className="p-2 text-gray-900">{formatDateTime(order.created_at)}</td>
                          <td className="text-center p-2">
                            <div className="flex items-center justify-center gap-1">
                              <button
                                onClick={() => handleViewOrder(order)}
                                className="p-1 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded transition-colors"
                                title="查看详情"
                              >
                                <Eye className="h-4 w-4" />
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>

                {/* 分页 */}
                {Math.ceil(reconciliationOrdersTotal / reconciliationOrdersPageSize) > 1 && (
                  <div className="flex items-center justify-between p-4 border-t border-gray-200">
                    <div className="text-sm text-gray-700">
                      显示第 {(reconciliationOrdersPage - 1) * reconciliationOrdersPageSize + 1} 到 {Math.min(reconciliationOrdersPage * reconciliationOrdersPageSize, reconciliationOrdersTotal)} 条，共 {reconciliationOrdersTotal} 条记录
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => loadReconciliationOrders(reconciliationOrdersPage - 1)}
                        disabled={reconciliationOrdersPage <= 1 || reconciliationOrdersLoading}
                      >
                        上一页
                      </Button>
                      <span className="text-sm text-gray-700">
                        第 {reconciliationOrdersPage} 页，共 {Math.ceil(reconciliationOrdersTotal / reconciliationOrdersPageSize)} 页
                      </span>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => loadReconciliationOrders(reconciliationOrdersPage + 1)}
                        disabled={reconciliationOrdersPage >= Math.ceil(reconciliationOrdersTotal / reconciliationOrdersPageSize) || reconciliationOrdersLoading}
                      >
                        下一页
                      </Button>
                    </div>
                  </div>
                )}
              </>
            ) : (
              <div className="text-center py-8">
                <CreditCard className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">暂无对账单数据</h3>
                <p className="text-gray-500">
                  {reconciliationSearchTravelerName || reconciliationSearchMobilePhone || reconciliationSearchContactPhone
                    ? "没有找到符合搜索条件的已完成订单"
                    : "该项目暂无已完成的订单"
                  }
                </p>
              </div>
            )}
          </div>
        </Card>
      </div>
    );
  };

  if (loading) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center h-96">
          <div className="text-center">
            <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-500" />
            <p className="text-gray-600">加载任务详情中...</p>
          </div>
        </div>
      </MainLayout>
    );
  }

  if (error || !project) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center h-96">
          <div className="text-center">
            <p className="text-red-500 mb-4">{error || '项目不存在'}</p>
            <Button onClick={() => navigate('/projects')}>
              返回项目列表
            </Button>
          </div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="p-4 bg-gray-50 min-h-screen">
        {/* 页面头部 */}
        <div className="mb-4">
          {/* 项目和任务类型信息卡片 */}
          <Card className="bg-white shadow-sm border border-gray-200 border-l-4 border-l-blue-500 p-4">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="flex items-center gap-3 mb-2">
                  <div className={`p-2 ${currentConfig?.bgColor || 'bg-blue-50'} rounded-lg`}>
                    <IconComponent className={`h-5 w-5 ${currentConfig?.color || 'text-blue-600'}`} />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <h1 className="text-xl font-bold text-gray-900">
                        {project.project_name} - {currentConfig?.label || taskType}
                      </h1>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => navigate(`/project-detail/${projectId}`)}
                        className="flex items-center gap-2"
                      >
                        <ArrowLeft className="h-4 w-4" />
                        返回
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </Card>
        </div>

        {/* Tab导航 */}
        <div className="mb-6">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8" aria-label="Tabs">
              <button
                onClick={() => handleTabChange('booking')}
                className={`py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap transition-colors duration-200 ${
                  activeTab === 'booking'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center gap-2">
                  <Clock2Icon className="h-4 w-4" />
                  待预订
                </div>
              </button>
              <button
                onClick={() => handleTabChange('all-orders')}
                className={`py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap transition-colors duration-200 ${
                  activeTab === 'all-orders'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center gap-2">
                  <Eye className="h-4 w-4" />
                  所有订单
                  {allOrdersTotal > 0 && (
                    <span className="ml-1 bg-blue-100 text-blue-800 text-xs font-medium px-2 py-0.5 rounded-full">
                      {allOrdersTotal}
                    </span>
                  )}
                </div>
              </button>
              <button
                onClick={() => handleTabChange('exception-orders')}
                className={`py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap transition-colors duration-200 ${
                  activeTab === 'exception-orders'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center gap-2">
                  <AlertTriangle className="h-4 w-4" />
                  异常订单
                  {exceptionOrdersTotal > 0 && (
                    <span className="ml-1 bg-blue-100 text-blue-800 text-xs font-medium px-2 py-0.5 rounded-full">
                      {exceptionOrdersTotal}
                    </span>
                  )}
                </div>
              </button>
              <button
                onClick={() => handleTabChange('details')}
                className={`py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap transition-colors duration-200 ${
                  activeTab === 'details'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center gap-2">
                  <FileText className="h-4 w-4" />
                  预订任务
                  {tasks.length > 0 && (
                    <span className="ml-1 bg-blue-100 text-blue-800 text-xs font-medium px-2 py-0.5 rounded-full">
                      {tasks.length}
                    </span>
                  )}
                </div>
              </button>
              <button
                onClick={() => handleTabChange('reconciliation')}
                className={`py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap transition-colors duration-200 ${
                  activeTab === 'reconciliation'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center gap-2">
                  <CreditCard className="h-4 w-4" />
                  对账单
                  {reconciliationOrdersTotal > 0 && (
                    <span className="ml-1 bg-green-100 text-green-800 text-xs font-medium px-2 py-0.5 rounded-full">
                      {reconciliationOrdersTotal}
                    </span>
                  )}
                </div>
              </button>
            </nav>
          </div>
        </div>

        {/* Tab内容 */}
        <div className="min-h-96">
          {activeTab === 'booking' && renderBookingContent()}
          {activeTab === 'all-orders' && renderAllOrdersContent()}
          {activeTab === 'exception-orders' && renderExceptionOrdersContent()}
          {activeTab === 'details' && renderDetailsContent()}
          {activeTab === 'reconciliation' && renderReconciliationContent()}
        </div>

        {/* 订单详情模态框 - 与火车票预订页面保持一致 */}
        {isViewModalOpen && selectedOrder && (
          <div className="fixed inset-0 z-50 flex items-center justify-center">
            {/* 背景遮罩 */}
            <div className="absolute inset-0 bg-black/50 backdrop-blur-sm" onClick={() => setIsViewModalOpen(false)}></div>
            
            {/* 弹窗内容 */}
            <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden z-50 mx-4">
              {/* 弹窗头部 */}
              <div className="flex justify-between items-center px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-white">
                <h2 className="text-xl font-semibold text-blue-700 flex items-center">
                  <Eye className="h-5 w-5 mr-2" />
                  火车票订单详情 {isEditModalOpen && <span className="ml-2 text-sm text-orange-600">(编辑模式)</span>}
                </h2>
                <button 
                  onClick={() => setIsViewModalOpen(false)}
                  className="h-8 w-8 rounded-full text-gray-400 hover:text-gray-600 hover:bg-gray-100 flex items-center justify-center transition-colors"
                >
                  <X className="h-5 w-5" />
                </button>
              </div>
              
              {/* 弹窗内容 */}
              <div className="p-6 overflow-y-auto max-h-[calc(90vh-180px)]">
                <div className="space-y-8">
                  {/* 订单状态区域 */}
                  <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                    <div className="flex items-center justify-between mb-3">
                      <h3 className="text-lg font-medium text-gray-900">订单状态</h3>
                      <div className="flex items-center">
                        {getOrderStatusDisplay(selectedOrder.order_status)}
                      </div>
                    </div>
                    
                    {/* 失败原因展示 - 只在验证失败或预定失败时显示 */}
                    {(selectedOrder.order_status === 'check_failed' || selectedOrder.order_status === 'failed') && selectedOrder.fail_reason && (
                      <div className="mt-4 p-4 bg-white rounded-lg border border-gray-200">
                        <div className="flex items-start gap-3">
                          <div className="flex-shrink-0">
                            <AlertTriangle className={`h-5 w-5 ${
                              selectedOrder.order_status === 'check_failed' ? 'text-amber-500' : 'text-red-500'
                            }`} />
                          </div>
                          <div className="flex-1">
                            <h4 className={`font-medium text-sm ${
                              selectedOrder.order_status === 'check_failed' ? 'text-amber-800' : 'text-red-800'
                            }`}>
                              {selectedOrder.order_status === 'check_failed' ? '验证失败原因' : '预定失败原因'}
                            </h4>
                            <p className={`mt-1 text-sm ${
                              selectedOrder.order_status === 'check_failed' ? 'text-amber-700' : 'text-red-700'
                            }`}>
                              {selectedOrder.fail_reason}
                            </p>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* 出行人基础信息 */}
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-4 pb-2 border-b border-gray-200">出行人基础信息</h3>
                    <div className="overflow-hidden border border-gray-300 rounded-lg">
                      <table className="w-full">
                        <tbody>
                          <tr className="border-b border-gray-200">
                            <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                              <span className="text-sm font-medium text-gray-700">出行人姓名</span>
                            </td>
                            <td className="py-3 px-4 w-1/4 border-r border-gray-200">
                              <span className="text-sm text-gray-900 font-medium">{selectedOrder.traveler_full_name}</span>
                            </td>
                            <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                              <span className="text-sm font-medium text-gray-700">姓</span>
                            </td>
                            <td className="py-3 px-4 w-1/4">
                              <span className="text-sm text-gray-900">{selectedOrder.traveler_surname || '-'}</span>
                            </td>
                          </tr>
                          <tr className="border-b border-gray-200">
                            <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                              <span className="text-sm font-medium text-gray-700">名</span>
                            </td>
                            <td className="py-3 px-4 border-r border-gray-200">
                              <span className="text-sm text-gray-900">{selectedOrder.traveler_given_name || '-'}</span>
                            </td>
                            <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                              <span className="text-sm font-medium text-gray-700">国籍</span>
                            </td>
                            <td className="py-3 px-4">
                              <span className="text-sm text-gray-900">{selectedOrder.nationality || '-'}</span>
                            </td>
                          </tr>
                          <tr className="border-b border-gray-200">
                            <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                              <span className="text-sm font-medium text-gray-700">性别</span>
                            </td>
                            <td className="py-3 px-4 border-r border-gray-200">
                              <span className="text-sm text-gray-900">{selectedOrder.gender || '-'}</span>
                            </td>
                            <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                              <span className="text-sm font-medium text-gray-700">出生日期</span>
                            </td>
                            <td className="py-3 px-4">
                              <span className="text-sm text-gray-900">{selectedOrder.birth_date || '-'}</span>
                            </td>
                          </tr>
                          <tr className="border-b border-gray-200">
                            <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                              <span className="text-sm font-medium text-gray-700">证件类型</span>
                            </td>
                            <td className="py-3 px-4 border-r border-gray-200">
                              <span className="text-sm text-gray-900">{selectedOrder.id_type || '-'}</span>
                            </td>
                            <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                              <span className="text-sm font-medium text-gray-700">证件号码</span>
                            </td>
                            <td className="py-3 px-4">
                              <span className="text-sm text-gray-900 font-mono">{selectedOrder.id_number || '-'}</span>
                            </td>
                          </tr>
                          <tr className="border-b border-gray-200">
                            <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                              <span className="text-sm font-medium text-gray-700">证件有效期</span>
                            </td>
                            <td className="py-3 px-4 border-r border-gray-200">
                              <span className="text-sm text-gray-900">{selectedOrder.id_expiry_date || '-'}</span>
                            </td>
                            <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                              <span className="text-sm font-medium text-gray-700">手机国际区号</span>
                            </td>
                            <td className="py-3 px-4">
                              <span className="text-sm text-gray-900">{selectedOrder.mobile_phone_country_code || '+86'}</span>
                            </td>
                          </tr>
                          <tr>
                            <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                              <span className="text-sm font-medium text-gray-700">手机号</span>
                            </td>
                            <td className="py-3 px-4">
                              <span className="text-sm text-gray-900">{selectedOrder.mobile_phone || '-'}</span>
                            </td>
                            <td className="py-3 px-4 bg-gray-50 border-r border-gray-200"></td>
                            <td className="py-3 px-4"></td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>

                  {/* 出行信息 */}
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-4 pb-2 border-b border-gray-200">出行信息</h3>
                    <div className="overflow-hidden border border-gray-300 rounded-lg">
                      <table className="w-full">
                        <tbody>
                          <tr className="border-b border-gray-200">
                            <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                              <span className="text-sm font-medium text-gray-700">差旅单号</span>
                            </td>
                            <td className="py-3 px-4 w-1/4 border-r border-gray-200">
                              <span className="text-sm text-gray-900">{selectedOrder.trip_submission_item || '-'}</span>
                            </td>
                            <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                              <span className="text-sm font-medium text-gray-700">出行日期</span>
                            </td>
                            <td className="py-3 px-4 w-1/4">
                              <span className="text-sm text-gray-900">{selectedOrder.travel_date || '-'}</span>
                            </td>
                          </tr>
                          <tr className="border-b border-gray-200">
                            <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                              <span className="text-sm font-medium text-gray-700">出发站名</span>
                            </td>
                            <td className="py-3 px-4 border-r border-gray-200">
                              <span className="text-sm text-gray-900">{selectedOrder.departure_station || '-'}</span>
                            </td>
                            <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                              <span className="text-sm font-medium text-gray-700">到达站名</span>
                            </td>
                            <td className="py-3 px-4">
                              <span className="text-sm text-gray-900">{selectedOrder.arrival_station || '-'}</span>
                            </td>
                          </tr>
                          <tr className="border-b border-gray-200">
                            <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                              <span className="text-sm font-medium text-gray-700">车次</span>
                            </td>
                            <td className="py-3 px-4 border-r border-gray-200">
                              <span className="text-sm text-gray-900 font-medium text-blue-600">{selectedOrder.train_number || '-'}</span>
                            </td>
                            <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                              <span className="text-sm font-medium text-gray-700">座位类型</span>
                            </td>
                            <td className="py-3 px-4">
                              <span className="text-sm text-gray-900">{selectedOrder.seat_type || '-'}</span>
                            </td>
                          </tr>
                          <tr className="border-b border-gray-200">
                            <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                              <span className="text-sm font-medium text-gray-700">出发时间</span>
                            </td>
                            <td className="py-3 px-4 border-r border-gray-200">
                              <span className="text-sm text-gray-900">{selectedOrder.departure_time || '-'}</span>
                            </td>
                            <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                              <span className="text-sm font-medium text-gray-700">到达时间</span>
                            </td>
                            <td className="py-3 px-4">
                              <span className="text-sm text-gray-900">{selectedOrder.arrival_time || '-'}</span>
                            </td>
                          </tr>
                          <tr>
                            <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                              <span className="text-sm font-medium text-gray-700">成本中心</span>
                            </td>
                            <td className="py-3 px-4">
                              <span className="text-sm text-gray-900">{selectedOrder.cost_center || '-'}</span>
                            </td>
                            <td className="py-3 px-4 bg-gray-50 border-r border-gray-200"></td>
                            <td className="py-3 px-4"></td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>

                  {/* 对账单信息 */}
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-4 pb-2 border-b border-gray-200">对账单信息</h3>
                    <div className="overflow-hidden border border-gray-300 rounded-lg">
                      <table className="w-full">
                        <tbody>
                          <tr className="border-b border-gray-200">
                            <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                              <span className="text-sm font-medium text-gray-700">公司名称</span>
                            </td>
                            <td className="py-3 px-4 w-1/4 border-r border-gray-200">
                              <span className="text-sm text-gray-900">{selectedOrder.company_name || '-'}</span>
                            </td>
                            <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                              <span className="text-sm font-medium text-gray-700">代订人</span>
                            </td>
                            <td className="py-3 px-4 w-1/4">
                              <span className="text-sm text-gray-900">{selectedOrder.booking_agent || '-'}</span>
                            </td>
                          </tr>
                          <tr className="border-b border-gray-200">
                            <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                              <span className="text-sm font-medium text-gray-700">金额</span>
                            </td>
                            <td className="py-3 px-4 border-r border-gray-200">
                              <span className="text-sm text-gray-900 font-medium text-green-600">{formatAmount(selectedOrder.amount)}</span>
                            </td>
                            <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                              <span className="text-sm font-medium text-gray-700">订单号</span>
                            </td>
                            <td className="py-3 px-4">
                              <span className="text-sm text-gray-900 font-mono">{selectedOrder.order_number || '-'}</span>
                            </td>
                          </tr>
                          <tr className={selectedOrder.ticket_sms ? "border-b border-gray-200" : ""}>
                            <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                              <span className="text-sm font-medium text-gray-700">账单号</span>
                            </td>
                            <td className="py-3 px-4">
                              <span className="text-sm text-gray-900 font-mono">{selectedOrder.bill_number || '-'}</span>
                            </td>
                            <td className="py-3 px-4 bg-gray-50 border-r border-gray-200"></td>
                            <td className="py-3 px-4"></td>
                          </tr>
                          {/* 出票短信 - 占据整行 */}
                          {selectedOrder.ticket_sms && (
                            <tr>
                              <td className="py-3 px-4 bg-yellow-50 border-r border-gray-200">
                                <span className="text-sm font-medium text-yellow-800">出票短信</span>
                              </td>
                              <td className="py-3 px-4 bg-yellow-50" colSpan={3}>
                                <div className="text-sm text-gray-900 bg-yellow-50 p-2 rounded border max-h-24 overflow-y-auto">
                                  <pre className="whitespace-pre-wrap text-xs">{selectedOrder.ticket_sms}</pre>
                                </div>
                              </td>
                            </tr>
                          )}
                        </tbody>
                      </table>
                    </div>
                  </div>

                </div>
              </div>
              
              {/* 弹窗底部 */}
              <div className="px-6 py-4 border-t border-gray-200 bg-gray-50 flex justify-end space-x-3">
                <Button
                  variant="outline"
                  onClick={() => setIsViewModalOpen(false)}
                >
                  关闭
                </Button>
                <Button
                  onClick={() => {
                    setIsViewModalOpen(false);
                    setIsEditModalOpen(true);
                  }}
                  className="bg-blue-600 hover:bg-blue-700 text-white"
                >
                  <Edit className="h-4 w-4 mr-2" />
                  编辑
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* 编辑订单模态框 - 与火车票预订页面保持一致 */}
        {isEditModalOpen && selectedOrder && (
          <div className="fixed inset-0 z-50 flex items-center justify-center">
            {/* 背景遮罩 */}
            <div className="absolute inset-0 bg-black/50 backdrop-blur-sm" onClick={() => setIsEditModalOpen(false)}></div>
            
            {/* 弹窗内容 */}
            <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden z-50 mx-4">
              {/* 弹窗头部 */}
              <div className="flex justify-between items-center px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-orange-50 to-white">
                <h2 className="text-xl font-semibold text-orange-700 flex items-center">
                  <Edit className="h-5 w-5 mr-2" />
                  编辑火车票订单
                </h2>
                <button 
                  onClick={() => setIsEditModalOpen(false)}
                  className="h-8 w-8 rounded-full text-gray-400 hover:text-gray-600 hover:bg-gray-100 flex items-center justify-center transition-colors"
                >
                  <X className="h-5 w-5" />
                </button>
              </div>
              
              {/* 弹窗内容 - 添加滚动条 */}
              <div className="p-6 overflow-y-auto max-h-[calc(90vh-180px)]">
                <div className="space-y-8">
                  {/* 订单状态区域 */}
                  <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                    <div className="flex items-center justify-between mb-3">
                      <h3 className="text-lg font-medium text-gray-900">订单状态</h3>
                      <div className="flex items-center">
                        {getOrderStatusDisplay(selectedOrder.order_status)}
                      </div>
                    </div>
                    
                    {/* 失败原因展示 - 只在验证失败或预定失败时显示 */}
                    {(selectedOrder.order_status === 'check_failed' || selectedOrder.order_status === 'failed') && selectedOrder.fail_reason && (
                      <div className="mt-4 p-4 bg-white rounded-lg border border-gray-200">
                        <div className="flex items-start gap-3">
                          <div className="flex-shrink-0">
                            <AlertTriangle className={`h-5 w-5 ${
                              selectedOrder.order_status === 'check_failed' ? 'text-amber-500' : 'text-red-500'
                            }`} />
                          </div>
                          <div className="flex-1">
                            <h4 className={`font-medium text-sm ${
                              selectedOrder.order_status === 'check_failed' ? 'text-amber-800' : 'text-red-800'
                            }`}>
                              {selectedOrder.order_status === 'check_failed' ? '验证失败原因' : '预定失败原因'}
                            </h4>
                            <p className={`mt-1 text-sm ${
                              selectedOrder.order_status === 'check_failed' ? 'text-amber-700' : 'text-red-700'
                            }`}>
                              {selectedOrder.fail_reason}
                            </p>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* 出行人基础信息 */}
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-4 pb-2 border-b border-gray-200">出行人基础信息</h3>
                    <div className="overflow-hidden border border-gray-300 rounded-lg">
                      <table className="w-full">
                        <tbody>
                          <tr className="border-b border-gray-200">
                            <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                              <label className="text-sm font-medium text-gray-700">出行人姓名</label>
                            </td>
                            <td className="py-3 px-4 w-1/4 border-r border-gray-200">
                              <EditableInput
                                field="traveler_full_name"
                                value={editingOrder?.traveler_full_name}
                                placeholder="请输入出行人姓名"
                                onFieldChange={handleFieldChange}
                                validationErrors={validationErrors}
                              />
                            </td>
                            <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                              <label className="text-sm font-medium text-gray-700">姓</label>
                            </td>
                            <td className="py-3 px-4 w-1/4">
                              <EditableInput
                                field="traveler_surname"
                                value={editingOrder?.traveler_surname}
                                placeholder="请输入姓"
                                onFieldChange={handleFieldChange}
                                validationErrors={validationErrors}
                              />
                            </td>
                          </tr>
                          <tr className="border-b border-gray-200">
                            <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                              <label className="text-sm font-medium text-gray-700">名</label>
                            </td>
                            <td className="py-3 px-4 border-r border-gray-200">
                              <input
                                type="text"
                                value={editingOrder?.traveler_given_name || ''}
                                onChange={(e) => handleFieldChange('traveler_given_name', e.target.value)}
                                className="w-full px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                              />
                            </td>
                            <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                              <label className="text-sm font-medium text-gray-700">国籍</label>
                            </td>
                            <td className="py-3 px-4">
                              <input
                                type="text"
                                value={editingOrder?.nationality || ''}
                                onChange={(e) => handleFieldChange('nationality', e.target.value)}
                                className="w-full px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                              />
                            </td>
                          </tr>
                          <tr className="border-b border-gray-200">
                            <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                              <label className="text-sm font-medium text-gray-700">性别</label>
                            </td>
                            <td className="py-3 px-4 border-r border-gray-200">
                              <EditableInput
                                field="gender"
                                value={editingOrder?.gender}
                                options={[
                                  { value: '男', label: '男' },
                                  { value: '女', label: '女' }
                                ]}
                                onFieldChange={handleFieldChange}
                                validationErrors={validationErrors}
                              />
                            </td>
                            <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                              <label className="text-sm font-medium text-gray-700">出生日期</label>
                            </td>
                            <td className="py-3 px-4">
                              <input
                                type="date"
                                value={editingOrder?.birth_date || ''}
                                onChange={(e) => handleFieldChange('birth_date', e.target.value)}
                                className="w-full px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                              />
                            </td>
                          </tr>
                          <tr className="border-b border-gray-200">
                            <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                              <label className="text-sm font-medium text-gray-700">证件类型</label>
                            </td>
                            <td className="py-3 px-4 border-r border-gray-200">
                              <EditableInput
                                field="id_type"
                                value={editingOrder?.id_type}
                                options={[
                                  { value: '身份证', label: '身份证' },
                                  { value: '公务护照', label: '公务护照' },
                                  { value: '普通护照', label: '普通护照' },
                                  { value: '港澳通行证', label: '港澳通行证' },
                                  { value: '台胞证', label: '台胞证' },
                                  { value: '回乡证', label: '回乡证' },
                                  { value: '军人证', label: '军人证' },
                                  { value: '海员证', label: '海员证' },
                                  { value: '台湾通行证', label: '台湾通行证' },
                                  { value: '外国永久居留证', label: '外国永久居留证' },
                                  { value: '港澳台居民居住证', label: '港澳台居民居住证' },
                                  { value: '其他', label: '其他' }
                                ]}
                                onFieldChange={handleFieldChange}
                                validationErrors={validationErrors}
                              />
                            </td>
                            <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                              <label className="text-sm font-medium text-gray-700">证件号码</label>
                            </td>
                            <td className="py-3 px-4">
                              <EditableInput
                                field="id_number"
                                value={editingOrder?.id_number}
                                placeholder="请输入证件号码"
                                onFieldChange={handleFieldChange}
                                validationErrors={validationErrors}
                              />
                            </td>
                          </tr>
                          <tr className="border-b border-gray-200">
                            <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                              <label className="text-sm font-medium text-gray-700">证件有效期</label>
                            </td>
                            <td className="py-3 px-4 border-r border-gray-200">
                              <input
                                type="date"
                                value={editingOrder?.id_expiry_date || ''}
                                onChange={(e) => handleFieldChange('id_expiry_date', e.target.value)}
                                className="w-full px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                              />
                            </td>
                            <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                              <label className="text-sm font-medium text-gray-700">手机国际区号</label>
                            </td>
                            <td className="py-3 px-4">
                              <input
                                type="text"
                                value={editingOrder?.mobile_phone_country_code || '+86'}
                                onChange={(e) => handleFieldChange('mobile_phone_country_code', e.target.value)}
                                className="w-full px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                              />
                            </td>
                          </tr>
                          <tr>
                            <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                              <label className="text-sm font-medium text-gray-700">手机号</label>
                            </td>
                            <td className="py-3 px-4">
                              <EditableInput
                                field="mobile_phone"
                                value={editingOrder?.mobile_phone}
                                placeholder="请输入手机号"
                                onFieldChange={handleFieldChange}
                                validationErrors={validationErrors}
                              />
                            </td>
                            <td className="py-3 px-4 bg-gray-50 border-r border-gray-200"></td>
                            <td className="py-3 px-4"></td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>

                  {/* 出行信息 */}
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-4 pb-2 border-b border-gray-200">出行信息</h3>
                    <div className="overflow-hidden border border-gray-300 rounded-lg">
                      <table className="w-full">
                        <tbody>
                          <tr className="border-b border-gray-200">
                            <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                              <label className="text-sm font-medium text-gray-700">差旅单号</label>
                            </td>
                            <td className="py-3 px-4 w-1/4 border-r border-gray-200">
                              <input
                                type="text"
                                value={editingOrder?.trip_submission_item || ''}
                                onChange={(e) => handleFieldChange('trip_submission_item', e.target.value)}
                                className="w-full px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                              />
                            </td>
                            <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                              <label className="text-sm font-medium text-gray-700">出行日期</label>
                            </td>
                            <td className="py-3 px-4 w-1/4">
                              <input
                                type="date"
                                value={editingOrder?.travel_date || ''}
                                onChange={(e) => handleFieldChange('travel_date', e.target.value)}
                                className="w-full px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                              />
                            </td>
                          </tr>
                          <tr className="border-b border-gray-200">
                            <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                              <label className="text-sm font-medium text-gray-700">出发站名</label>
                            </td>
                            <td className="py-3 px-4 border-r border-gray-200">
                              <EditableInput
                                field="departure_station"
                                value={editingOrder?.departure_station}
                                placeholder="请输入出发站名"
                                onFieldChange={handleFieldChange}
                                validationErrors={validationErrors}
                              />
                            </td>
                            <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                              <label className="text-sm font-medium text-gray-700">到达站名</label>
                            </td>
                            <td className="py-3 px-4">
                              <EditableInput
                                field="arrival_station"
                                value={editingOrder?.arrival_station}
                                placeholder="请输入到达站名"
                                onFieldChange={handleFieldChange}
                                validationErrors={validationErrors}
                              />
                            </td>
                          </tr>
                          <tr className="border-b border-gray-200">
                            <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                              <label className="text-sm font-medium text-gray-700">车次</label>
                            </td>
                            <td className="py-3 px-4 border-r border-gray-200">
                              <EditableInput
                                field="train_number"
                                value={editingOrder?.train_number}
                                placeholder="请输入车次"
                                onFieldChange={handleFieldChange}
                                validationErrors={validationErrors}
                              />
                            </td>
                            <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                              <label className="text-sm font-medium text-gray-700">座位类型</label>
                            </td>
                            <td className="py-3 px-4">
                              <EditableInput
                                field="seat_type"
                                value={editingOrder?.seat_type}
                                options={[
                                  { value: '商务座', label: '商务座' },
                                  { value: '一等座', label: '一等座' },
                                  { value: '二等座', label: '二等座' },
                                  { value: '硬卧', label: '硬卧' },
                                  { value: '软卧', label: '软卧' },
                                  { value: '硬座', label: '硬座' },
                                  { value: '无座', label: '无座' }
                                ]}
                                onFieldChange={handleFieldChange}
                                validationErrors={validationErrors}
                              />
                            </td>
                          </tr>
                          <tr className="border-b border-gray-200">
                            <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                              <label className="text-sm font-medium text-gray-700">出发时间</label>
                            </td>
                            <td className="py-3 px-4 border-r border-gray-200">
                              <input
                                type="time"
                                value={editingOrder?.departure_time || ''}
                                onChange={(e) => handleFieldChange('departure_time', e.target.value)}
                                className="w-full px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                              />
                            </td>
                            <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                              <label className="text-sm font-medium text-gray-700">到达时间</label>
                            </td>
                            <td className="py-3 px-4">
                              <input
                                type="time"
                                value={editingOrder?.arrival_time || ''}
                                onChange={(e) => handleFieldChange('arrival_time', e.target.value)}
                                className="w-full px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                              />
                            </td>
                          </tr>
                          <tr>
                            <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                              <label className="text-sm font-medium text-gray-700">成本中心</label>
                            </td>
                            <td className="py-3 px-4">
                              <input
                                type="text"
                                value={editingOrder?.cost_center || ''}
                                onChange={(e) => handleFieldChange('cost_center', e.target.value)}
                                className="w-full px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                              />
                            </td>
                            <td className="py-3 px-4 bg-gray-50 border-r border-gray-200"></td>
                            <td className="py-3 px-4"></td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>

                  {/* 对账单信息 */}
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-4 pb-2 border-b border-gray-200">对账单信息</h3>
                    <div className="overflow-hidden border border-gray-300 rounded-lg">
                      <table className="w-full">
                        <tbody>
                          <tr className="border-b border-gray-200">
                            <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                              <label className="text-sm font-medium text-gray-700">公司名称</label>
                            </td>
                            <td className="py-3 px-4 w-1/4 border-r border-gray-200">
                              <input
                                type="text"
                                value={editingOrder?.company_name || ''}
                                onChange={(e) => handleFieldChange('company_name', e.target.value)}
                                className="w-full px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                              />
                            </td>
                            <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                              <label className="text-sm font-medium text-gray-700">代订人</label>
                            </td>
                            <td className="py-3 px-4 w-1/4">
                              <input
                                type="text"
                                value={editingOrder?.booking_agent || ''}
                                onChange={(e) => handleFieldChange('booking_agent', e.target.value)}
                                className="w-full px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                              />
                            </td>
                          </tr>
                          <tr className="border-b border-gray-200">
                            <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                              <label className="text-sm font-medium text-gray-700">金额</label>
                            </td>
                            <td className="py-3 px-4 border-r border-gray-200">
                              <input
                                type="number"
                                step="0.01"
                                value={editingOrder?.amount || ''}
                                onChange={(e) => handleFieldChange('amount', e.target.value ? parseFloat(e.target.value) : '')}
                                className="w-full px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                              />
                            </td>
                            <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                              <label className="text-sm font-medium text-gray-700">订单号</label>
                            </td>
                            <td className="py-3 px-4">
                              <input
                                type="text"
                                value={editingOrder?.order_number || ''}
                                onChange={(e) => handleFieldChange('order_number', e.target.value)}
                                className="w-full px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono"
                              />
                            </td>
                          </tr>
                          <tr className={editingOrder?.ticket_sms ? "border-b border-gray-200" : ""}>
                            <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                              <label className="text-sm font-medium text-gray-700">账单号</label>
                            </td>
                            <td className="py-3 px-4">
                              <input
                                type="text"
                                value={editingOrder?.bill_number || ''}
                                onChange={(e) => handleFieldChange('bill_number', e.target.value)}
                                className="w-full px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono"
                              />
                            </td>
                            <td className="py-3 px-4 bg-gray-50 border-r border-gray-200"></td>
                            <td className="py-3 px-4"></td>
                          </tr>
                          <tr>
                            <td className="py-3 px-4 bg-yellow-50 border-r border-gray-200">
                              <label className="text-sm font-medium text-yellow-800">出票短信</label>
                            </td>
                            <td className="py-3 px-4 bg-yellow-50" colSpan={3}>
                              <textarea
                                value={editingOrder?.ticket_sms || ''}
                                onChange={(e) => handleFieldChange('ticket_sms', e.target.value)}
                                rows={3}
                                className="w-full px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 resize-vertical"
                                placeholder="购票成功短信内容..."
                              />
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </div>
              
              {/* 弹窗底部 */}
              <div className="px-6 py-4 border-t border-gray-200 bg-gray-50 flex justify-end space-x-3">
                <Button
                  variant="outline"
                  onClick={() => setIsEditModalOpen(false)}
                  disabled={orderOperationLoading}
                >
                  取消
                </Button>
                <Button
                  onClick={saveEditingOrder}
                  disabled={orderOperationLoading}
                  className="bg-orange-600 hover:bg-orange-700 text-white"
                >
                  {orderOperationLoading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      保存中...
                    </>
                  ) : (
                    '保存'
                  )}
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* 删除确认对话框 */}
        {isDeleteDialogOpen && selectedOrder && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
              <div className="p-6">
                <div className="flex items-center justify-center w-12 h-12 mx-auto mb-4 bg-red-100 rounded-full">
                  <Trash2 className="h-6 w-6 text-red-600" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 text-center mb-4">确认删除订单</h3>
                <div className="mb-6 p-4 bg-gray-50 rounded-lg">
                  <div className="text-sm space-y-2">
                    <p><span className="font-medium">出行人：</span>{selectedOrder.traveler_full_name}</p>
                    <p><span className="font-medium">订单号：</span>{selectedOrder.order_number}</p>
                    <p><span className="font-medium">行程：</span>{selectedOrder.departure_station} → {selectedOrder.arrival_station}</p>
                    <p><span className="font-medium">车次：</span>{selectedOrder.train_number}</p>
                    <p><span className="font-medium">金额：</span>{formatAmount(selectedOrder.amount)}</p>
                  </div>
                </div>
                <p className="text-sm text-gray-600 text-center mb-6">
                  此操作不可撤销，确定要删除这个订单吗？
                </p>
                <div className="flex justify-center gap-3">
                  <Button
                    variant="outline"
                    onClick={() => setIsDeleteDialogOpen(false)}
                    disabled={orderOperationLoading}
                  >
                    取消
                  </Button>
                  <Button
                    variant="destructive"
                    onClick={confirmDeleteOrder}
                    disabled={orderOperationLoading}
                  >
                    {orderOperationLoading ? '删除中...' : '确认删除'}
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </MainLayout>
  );
};

export default ProjectTaskDetailPage; 