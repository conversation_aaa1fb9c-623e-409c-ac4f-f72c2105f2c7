import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import MainLayout from '@/components/layout/MainLayout';
import { 
  ArrowLeftIcon,
  RefreshCwIcon,
  CalendarIcon,
  UserIcon,
  BuildingIcon,
  Plane,
  Train,
  Building2
} from 'lucide-react';
import { ProjectService, ProjectTaskService } from '@/services/project';
import { trainOrderApi, ProjectOrderDetailStatsResponse } from '@/api/trainOrder';
import { hotelOrderApi, ProjectOrderDetailStatsResponse as HotelProjectOrderDetailStatsResponse } from '@/api/hotel';
import { Project } from '@/types/project';
import { TaskCardInfo, ProjectTask } from '@/types/project-task';

// 任务类型配置
const taskTypeConfigs: Record<string, {
  type: string;
  label: string;
  description: string;
  icon: React.ComponentType<any>;
  color: string;
  bgColor: string;
  borderColor: string;
}> = {
  '火车票预订': {
    type: '火车票预订',
    label: '火车票预订',
    description: '创建火车票预订任务',
    icon: Train,
    color: 'text-blue-600',
    bgColor: 'bg-blue-50',
    borderColor: 'border-blue-500'
  },
  '酒店预订': {
    type: '酒店预订',
    label: '酒店预订',
    description: '创建酒店预订任务',
    icon: Building2,
    color: 'text-purple-600',
    bgColor: 'bg-purple-50',
    borderColor: 'border-purple-500'
  },
  '机票预订': {
    type: '机票预订',
    label: '飞机票预订',
    description: '创建飞机票预订任务',
    icon: Plane,
    color: 'text-green-600',
    bgColor: 'bg-green-50',
    borderColor: 'border-green-500'
  }
}

// 任务类型汇总信息
interface TaskTypeSummary {
  type: string;
  config: {
    type: string;
    label: string;
    description: string;
    icon: React.ComponentType<any>;
    color: string;
    bgColor: string;
    borderColor: string;
  };
  taskCount: number;
  orderCount: number;
  totalAmount: number;
  totalPeople: number;
  tasks: TaskCardInfo[];
  hasException: boolean;
  latestStatus: string;
  completedCount: number;
  progressPercentage?: number;
}

const ProjectDetailPage: React.FC = () => {
  const { projectId } = useParams<{ projectId: string }>();
  const navigate = useNavigate();
  
  const [project, setProject] = useState<Project | null>(null);
  const [tasks, setTasks] = useState<TaskCardInfo[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [totalOrderAmount, setTotalOrderAmount] = useState(0);
  const [trainOrderStats, setTrainOrderStats] = useState<ProjectOrderDetailStatsResponse | null>(null);
  const [hotelOrderStats, setHotelOrderStats] = useState<HotelProjectOrderDetailStatsResponse | null>(null);

  // 删除未使用的函数

  // 删除未使用的函数

  // 格式化日期
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    });
  };

  // 模拟任务卡片数据的生成
  const generateTaskCardInfo = (task: ProjectTask): TaskCardInfo => {
    const mockOrderCount = Math.floor(Math.random() * 10) + 1;
    const mockAmount = Math.floor(Math.random() * 50000) + 5000;
    const mockPeople = Math.floor(Math.random() * 20) + 5;
    const statuses = ['已确认', '待确认', '已取消'];
    const mockStatus = statuses[Math.floor(Math.random() * statuses.length)];
    const mockException = Math.random() > 0.8;

    return {
      task,
      order_count: mockOrderCount,
      total_amount: mockAmount,
      total_people: mockPeople,
      booking_status: mockStatus,
      has_exception: mockException
    };
  };

  // 加载项目详情和任务列表
  const loadProjectDetails = async () => {
    if (!projectId) return;

    setLoading(true);
    setError(null);

    try {
      // 加载项目详情
      const projectResponse = await ProjectService.getProject(parseInt(projectId));
      if (!projectResponse.success) {
        throw new Error(projectResponse.error || '获取项目详情失败');
      }
      setProject(projectResponse.data!);

      // 加载项目任务列表
      const tasksResponse = await ProjectTaskService.getTasksByProject(
        parseInt(projectId),
        { page: 1, page_size: 100 }
      );

      // 生成任务卡片信息
      const taskCardInfos = tasksResponse.items.map(generateTaskCardInfo);
      setTasks(taskCardInfos);

      // 加载火车票订单统计
      let trainCompletedAmount = 0;
      try {
        const trainStatsResponse = await trainOrderApi.getProjectDetailStats(parseInt(projectId));
        setTrainOrderStats(trainStatsResponse.data);
        trainCompletedAmount = parseFloat(trainStatsResponse.data.completed_amount) || 0;
      } catch (trainStatsError) {
        console.warn('获取火车票统计失败:', trainStatsError);
      }

      // 加载酒店订单统计
      let hotelCompletedAmount = 0;
      try {
        const hotelStatsResponse = await hotelOrderApi.getProjectDetailStats(parseInt(projectId));
        setHotelOrderStats(hotelStatsResponse);
        hotelCompletedAmount = parseFloat(hotelStatsResponse.total_amount) || 0;
      } catch (hotelStatsError) {
        console.warn('获取酒店统计失败:', hotelStatsError);
      }

      // 计算总的预定完成金额（火车票 + 酒店）
      const totalCompletedAmount = trainCompletedAmount + hotelCompletedAmount;
      if (totalCompletedAmount > 0) {
        setTotalOrderAmount(totalCompletedAmount);
      } else {
        // 如果获取统计失败，使用任务模拟数据
        const totalAmount = taskCardInfos.reduce((sum, info) => sum + info.total_amount, 0);
        setTotalOrderAmount(totalAmount);
      }

    } catch (err) {
      console.error('加载项目详情失败:', err);
      setError(err instanceof Error ? err.message : '加载项目详情失败');
    } finally {
      setLoading(false);
    }
  };

  // 在组件加载时获取项目详情
  useEffect(() => {
    loadProjectDetails();
  }, [projectId]);

  // 处理任务类型操作
  const handleStartTaskType = (summary: TaskTypeSummary) => {
    // 酒店预订使用专门的酒店预订页面
    if (summary.type === '酒店预订') {
      navigate(`/hotel-booking/${projectId}`);
    } else {
      // 其他类型导航到项目任务详情页面，默认显示预订tab
      navigate(`/project-task-detail/${projectId}?type=${encodeURIComponent(summary.type)}&tab=booking`);
    }
  };

  const handleViewTaskType = (summary: TaskTypeSummary) => {
    // 酒店预订使用专门的酒店预订页面，跳转到所有订单标签页
    if (summary.type === '酒店预订') {
      navigate(`/hotel-booking/${projectId}?tab=all-orders`);
    } else {
      // 其他类型导航到项目任务详情页面，默认显示所有订单tab
      navigate(`/project-task-detail/${projectId}?type=${encodeURIComponent(summary.type)}&tab=all-orders`);
    }
  };

  const renderTaskTypeCard = (summary: TaskTypeSummary) => {
    const { config } = summary;
    const IconComponent = config.icon;
    const hasData = summary.orderCount > 0; // 使用订单数判断是否有数据
    const progressPercentage = summary.progressPercentage !== undefined ? summary.progressPercentage : (hasData ? Math.min((summary.orderCount / 10) * 100, 100) : 0);

    return (
      <Card key={config.type} className={`bg-white border border-gray-200 hover:shadow-lg transition-all duration-200 overflow-hidden border-t-4 ${config.borderColor}`}>
        <div className="p-4">
          {/* 卡片头部 */}
          <div className="flex items-center gap-3 mb-4">
            <div className={`p-3 ${config.bgColor} rounded-lg`}>
              <IconComponent className={`h-6 w-6 ${config.color}`} />
            </div>
            <div className="flex-1">
              <h3 className="font-medium text-gray-900">{config.label}</h3>
              <p className="text-sm text-gray-500">
                {hasData ? `${summary.taskCount}个任务，${summary.orderCount}次预订` : config.description}
              </p>
            </div>
            {summary.hasException && (
              <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
            )}
          </div>

          {/* 统计数据 */}
          <div className="mb-4">
            {config.type === '火车票预订' && trainOrderStats ? (
              // 火车票详细统计
              <div className="grid grid-cols-3 gap-2 mb-3">
                <div className={`text-center ${config.bgColor} rounded p-2`}>
                  <div className="text-sm font-bold text-gray-900">{trainOrderStats.total_orders}</div>
                  <div className="text-xs text-gray-500">订单数</div>
                </div>
                <div className={`text-center ${config.bgColor} rounded p-2`}>
                  <div className="text-sm font-bold text-green-600">{trainOrderStats.completed_orders}</div>
                  <div className="text-xs text-gray-500">预定成功</div>
                </div>
                <div className={`text-center ${config.bgColor} rounded p-2`}>
                  <div className="text-sm font-bold text-red-600">{trainOrderStats.failed_orders}</div>
                  <div className="text-xs text-gray-500">预定失败</div>
                </div>
              </div>
            ) : config.type === '酒店预订' && hotelOrderStats ? (
              // 酒店详细统计
              <div className="grid grid-cols-3 gap-2 mb-3">
                <div className={`text-center ${config.bgColor} rounded p-2`}>
                  <div className="text-sm font-bold text-gray-900">{hotelOrderStats.total_orders}</div>
                  <div className="text-xs text-gray-500">订单数</div>
                </div>
                <div className={`text-center ${config.bgColor} rounded p-2`}>
                  <div className="text-sm font-bold text-green-600">{hotelOrderStats.completed_orders}</div>
                  <div className="text-xs text-gray-500">预定成功</div>
                </div>
                <div className={`text-center ${config.bgColor} rounded p-2`}>
                  <div className="text-sm font-bold text-red-600">{hotelOrderStats.failed_orders}</div>
                  <div className="text-xs text-gray-500">预定失败</div>
                </div>
              </div>
            ) : (
              // 其他类型的通用统计
              <div className="grid grid-cols-3 gap-2 mb-3">
                <div className={`text-center ${config.bgColor} rounded p-2`}>
                  <div className="text-sm font-bold text-gray-900">{summary.orderCount}</div>
                  <div className="text-xs text-gray-500">订单数</div>
                </div>
                <div className={`text-center ${config.bgColor} rounded p-2`}>
                  <div className="text-sm font-bold text-gray-900">{summary.totalPeople}</div>
                  <div className="text-xs text-gray-500">总人数</div>
                </div>
                <div className={`text-center ${config.bgColor} rounded p-2`}>
                  <div className={`text-sm font-bold ${config.color}`}>¥{(summary.totalAmount / 1000).toFixed(0)}K</div>
                  <div className="text-xs text-gray-500">总金额</div>
                </div>
              </div>
            )}
          </div>

          {/* 进度条 */}
          <div className="mb-3 mt-3">
            <div className="flex items-center justify-between mb-1">
              <span className="text-xs text-gray-600">
                {config.type === '火车票预订' || config.type === '酒店预订' ? '预定进度' : '预订进度'}
                {config.type === '火车票预订' && trainOrderStats ? ` (${trainOrderStats.completed_orders + trainOrderStats.failed_orders}/${trainOrderStats.total_orders})` : ''}
                {config.type === '酒店预订' && hotelOrderStats ? ` (${hotelOrderStats.completed_orders + hotelOrderStats.failed_orders}/${hotelOrderStats.total_orders})` : ''}
              </span>
              <span className="text-xs text-gray-900">{Math.round(progressPercentage)}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-1.5">
              <div 
                className={`${config.borderColor.replace('border', 'bg')} h-1.5 rounded-full transition-all duration-300`} 
                style={{ width: `${progressPercentage}%` }}
              ></div>
            </div>
          </div>

          {/* 操作按钮 */}
          {!hasData ? (
            <Button 
              onClick={() => handleStartTaskType(summary)}
              className={`w-full ${config.color.replace('text', 'bg').replace('-600', '-600')} ${config.color.replace('text', 'hover:bg').replace('-600', '-700')} text-white font-medium`}
            >
              开始{config.label}
            </Button>
          ) : (
            <div className="grid grid-cols-2 gap-2">
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => handleStartTaskType(summary)}
                className="text-gray-600 border-gray-300 hover:bg-gray-50"
              >
                继续预订
              </Button>
              <Button 
                size="sm"
                onClick={() => handleViewTaskType(summary)}
                className={`${config.color.replace('text', 'bg').replace('-600', '-600')} ${config.color.replace('text', 'hover:bg').replace('-600', '-700')} text-white`}
              >
                查看详情
              </Button>
            </div>
          )}
        </div>
      </Card>
    );
  };

  // 获取任务类型汇总数据
  const taskTypeSummaries = Object.entries(taskTypeConfigs).map(([type, config]) => {
    if (type === '火车票预订' && trainOrderStats) {
      // 使用真实的火车票统计数据
      const hasData = trainOrderStats.total_orders > 0;
      const hasException = trainOrderStats.check_failed_orders > 0 || trainOrderStats.failed_orders > 0;
      const completedCount = trainOrderStats.completed_orders + trainOrderStats.failed_orders; // 预定成功 + 预定失败
      const progressPercentage = hasData ? Math.min((completedCount / trainOrderStats.total_orders) * 100, 100) : 0;
      
      return {
        type,
        config,
        taskCount: tasks.filter(task => task.task.task_type === type).length || (hasData ? 1 : 0), // 如果有订单数据，默认显示至少1个任务
        orderCount: trainOrderStats.total_orders,
        totalAmount: parseFloat(trainOrderStats.total_amount) || 0,
        totalPeople: trainOrderStats.total_orders, // 订单数作为人数的近似值
        tasks: tasks.filter(task => task.task.task_type === type),
        hasException,
        latestStatus: hasData ? '处理中' : '暂无数据',
        completedCount,
        progressPercentage
      };
    } else if (type === '酒店预订' && hotelOrderStats) {
      // 使用真实的酒店统计数据
      const hasData = hotelOrderStats.total_orders > 0;
      const hasException = hotelOrderStats.check_failed_orders > 0 || hotelOrderStats.failed_orders > 0;
      const completedCount = hotelOrderStats.completed_orders + hotelOrderStats.failed_orders; // 预定成功 + 预定失败
      const progressPercentage = hasData ? Math.min((completedCount / hotelOrderStats.total_orders) * 100, 100) : 0;
      
      return {
        type,
        config,
        taskCount: tasks.filter(task => task.task.task_type === type).length || (hasData ? 1 : 0), // 如果有订单数据，默认显示至少1个任务
        orderCount: hotelOrderStats.total_orders,
        totalAmount: parseFloat(hotelOrderStats.total_amount) || 0,
        totalPeople: hotelOrderStats.total_orders, // 订单数作为人数的近似值
        tasks: tasks.filter(task => task.task.task_type === type),
        hasException,
        latestStatus: hasData ? '处理中' : '暂无数据',
        completedCount,
        progressPercentage
      };
    } else {
      // 其他类型使用原有的模拟数据
      return {
        type,
        config,
        taskCount: tasks.filter(task => task.task.task_type === type || (type === '机票预订' && task.task.task_type === '飞机票预订')).length,
        orderCount: tasks.filter(task => task.task.task_type === type || (type === '机票预订' && task.task.task_type === '飞机票预订')).reduce((sum, t) => sum + t.order_count, 0),
        totalAmount: tasks.filter(task => task.task.task_type === type || (type === '机票预订' && task.task.task_type === '飞机票预订')).reduce((sum, t) => sum + t.total_amount, 0),
        totalPeople: tasks.filter(task => task.task.task_type === type || (type === '机票预订' && task.task.task_type === '飞机票预订')).reduce((sum, t) => sum + t.total_people, 0),
        tasks: tasks.filter(task => task.task.task_type === type || (type === '机票预订' && task.task.task_type === '飞机票预订')),
        hasException: tasks.filter(task => task.task.task_type === type || (type === '机票预订' && task.task.task_type === '飞机票预订')).some(t => t.has_exception),
        latestStatus: tasks.filter(task => task.task.task_type === type || (type === '机票预订' && task.task.task_type === '飞机票预订')).length > 0 ? tasks.filter(task => task.task.task_type === type || (type === '机票预订' && task.task.task_type === '飞机票预订'))[tasks.filter(task => task.task.task_type === type || (type === '机票预订' && task.task.task_type === '飞机票预订')).length - 1].booking_status : '暂无任务',
        completedCount: tasks.filter(task => task.task.task_type === type || (type === '机票预订' && task.task.task_type === '飞机票预订')).filter(t => t.booking_status === 'completed').length,
        progressPercentage: 0
      };
    }
  });

  if (loading) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center h-96">
          <div className="text-center">
            <RefreshCwIcon className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-500" />
            <p className="text-gray-600">加载项目详情中...</p>
          </div>
        </div>
      </MainLayout>
    );
  }

  if (error || !project) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center h-96">
          <div className="text-center">
            <p className="text-red-500 mb-4">{error || '项目不存在'}</p>
            <Button onClick={() => navigate('/projects')}>
              返回项目列表
            </Button>
          </div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="p-4 bg-gray-50 min-h-screen">
        {/* 页面头部 */}
        <div className="mb-4">
          
          {/* 美化后的项目信息卡片 */}
          <Card className="bg-white rounded-2xl shadow-lg border border-gray-100 px-6 py-4 mb-2">
            <div className="flex flex-col gap-4">
              {/* 项目标题和描述 */}
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between mb-1">
                  <h1 className="text-xl font-bold text-gray-900 truncate">{project.project_name}</h1>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => navigate('/projects')}
                    className="flex items-center gap-2"
                  >
                    <ArrowLeftIcon className="h-4 w-4" />
                    返回
                  </Button>
                </div>
                <p className="text-sm text-gray-500 mb-2 truncate">{project.project_description || '技术团队前往华南工厂考察设备'}</p>
              </div>
              {/* 分隔线 */}
              <div className="my-3 border-b border-gray-100"></div>
              {/* 关键信息横向分布 */}
              <div className="flex items-center justify-between">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 flex-1">
                  <div className="flex items-center gap-2">
                    <div className="p-1.5 bg-blue-100 rounded-lg">
                      <BuildingIcon className="h-4 w-4 text-blue-600" />
                    </div>
                    <div className="min-w-0">
                      <div className="text-xs text-gray-500">客户名称</div>
                      <div className="text-sm font-semibold text-gray-900 truncate">{project.client_name}</div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="p-1.5 bg-green-100 rounded-lg">
                      <UserIcon className="h-4 w-4 text-green-600" />
                    </div>
                    <div className="min-w-0">
                      <div className="text-xs text-gray-500">创建人</div>
                      <div className="text-sm font-semibold text-gray-900 truncate">{project.creator_name}</div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="p-1.5 bg-purple-100 rounded-lg">
                      <CalendarIcon className="h-4 w-4 text-purple-600" />
                    </div>
                    <div className="min-w-0">
                      <div className="text-xs text-gray-500">创建时间</div>
                      <div className="text-sm font-semibold text-gray-900 truncate">{formatDate(project.created_at)}</div>
                    </div>
                  </div>
                </div>
                <div className="flex-shrink-0 ml-6">
                  <div className="bg-gradient-to-r from-blue-400 to-blue-600 rounded-xl px-4 py-2 text-right shadow-md">
                    <div className="text-xs text-white/80 font-medium mb-0.5">累计预订金额</div>
                    <div className="text-xl font-extrabold text-white tracking-wide">¥{totalOrderAmount.toLocaleString()}</div>
                  </div>
                </div>
              </div>
            </div>
          </Card>
        </div>

        {/* 任务管理section */}
        <Card className="bg-white shadow-sm border border-gray-200 mb-6">
          <div className="p-4">
            {/* 任务类型列表标题 */}
            <div className="flex items-center justify-between mb-3">
              <div>
                <h2 className="text-base font-semibold text-gray-900">预订管理</h2>
                <p className="text-xs text-gray-500">按类型汇总显示，共{tasks.length}个任务</p>
              </div>
            </div>

            {/* 任务类型卡片网格 - 固定显示3个 */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {taskTypeSummaries.map(summary => renderTaskTypeCard(summary))}
            </div>
          </div>
        </Card>
      </div>
    </MainLayout>
  );
};

export default ProjectDetailPage;
