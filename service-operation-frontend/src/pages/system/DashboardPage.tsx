import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import MainLayout from '@/components/layout/MainLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/store/auth-context';
import { FileText, Ticket, Upload, ArrowRight, CheckCircle } from 'lucide-react';

// 服务功能卡片数据
const serviceCards = [
  {
    id: 'passport',
    title: '护照自动化识别',
    description: '全球各国护照自动识别与验证，快速提取关键信息，提高客户处理效率',
    image: '/passport.png',
    icon: <FileText className="h-8 w-8 text-blue-500" />,
    features: [
      '支持多国护照格式',
      '高精度OCR识别',
      '自动提取姓名、护照号、出生日期等信息',
      '实时验证护照有效性'
    ],
    buttonText: '开始使用',
    buttonClass: 'bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white shadow-lg hover:shadow-xl transition-all duration-200'
  },
  {
    id: 'booking',
    title: '团房团票自动化预定',
    description: '一键完成团队房间和机票预订，提高运营效率',
    image: '/booking.png',
    icon: <Ticket className="h-8 w-8 text-green-500" />,
    features: [
      '批量预订酒店房间',
      '批量预定火车票',
      '批量预定机票',
      '自动对账管理'
    ],
    buttonText: '立即预订',
    buttonClass: 'bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white shadow-lg hover:shadow-xl transition-all duration-200'
  }
];

const DashboardPage: React.FC = () => {
  const { isAuthenticated, isLoading } = useAuth();
  const navigate = useNavigate();
  
  useEffect(() => {
    // 如果用户未登录且加载完成，则重定向到登录页面
    if (!isLoading && !isAuthenticated) {
      navigate('/');
    }
  }, [isLoading, isAuthenticated, navigate]);
  
  // 如果正在加载或未认证，显示加载状态
  if (isLoading || !isAuthenticated) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  const handlePassportClick = () => {
    navigate('/passport-recognition');
  };

  const handleBookingClick = () => {
    navigate('/projects');
  };

  return (
    <MainLayout>
      <div className="space-y-6">
        <div className="mb-6">
          <p className="text-gray-800 text-left">欢迎使用服务运营自动化平台，选择下方服务开始使用</p>
        </div>
        
        {/* 服务卡片 */}
        <div className="grid gap-6 md:grid-cols-2">
          {serviceCards.map(card => (
            <Card key={card.id} className="overflow-hidden border-2 hover:border-blue-200 transition-all duration-300">
              <div className="relative h-48 overflow-hidden bg-gray-100">
                <img 
                  src={card.image} 
                  alt={card.title}
                  className="w-full h-full object-cover transition-transform duration-500 hover:scale-105"
                />
              </div>
              
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-xl font-bold">{card.title}</CardTitle>
                </div>
                <CardDescription className="text-sm mt-2">
                  {card.description}
                </CardDescription>
              </CardHeader>
              
              <CardContent>
                <ul className="space-y-2">
                  {card.features.map((feature, index) => (
                    <li key={index} className="flex items-start">
                      <div className="mr-2 mt-0.5">
                        <CheckCircle size={16} className="text-blue-500" />
                      </div>
                      <span className="text-sm text-gray-700">{feature}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
              
              <CardFooter className="pt-0">
                <Button 
                  className={`w-full flex items-center justify-center gap-2 mt-4 ${card.buttonClass}`}
                  onClick={() => {
                    if (card.id === 'passport') {
                      handlePassportClick();
                    } else if (card.id === 'booking') {
                      handleBookingClick();
                    }
                  }}
                >
                  {card.buttonText}
                  <ArrowRight size={16} />
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>
        
        {/* 使用说明 */}
        <Card className="mt-8 bg-blue-50 border-blue-100">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Upload className="h-5 w-5" />
              <span>快速开始</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-700">
              选择上方服务卡片，点击按钮开始使用相应功能。如需帮助，请联系系统管理员。
            </p>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
};

export default DashboardPage;
