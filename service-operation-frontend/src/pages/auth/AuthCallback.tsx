import React, { useEffect, useState, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { SSOService } from '@/services/auth';
import { useAuth } from '@/store/auth-context';

/**
 * 授权回调页面
 * 处理同程 SSO 授权回调，获取访问令牌和用户信息
 */
const AuthCallback: React.FC = () => {
  const [error, setError] = useState<string | null>(null);
  const navigate = useNavigate();
  const { user } = useAuth();
  const hasProcessedRef = useRef(false); // 防止重复处理

  useEffect(() => {
    // 如果用户已经登录，直接跳转到首页
    if (user) {
      navigate('/');
      return;
    }

    // 防止重复处理（React严格模式会导致useEffect执行两次）
    if (hasProcessedRef.current) {
      return;
    }
    hasProcessedRef.current = true;

    // 处理授权回调
    const handleCallback = async () => {
      try {
        // 从 URL 参数中获取授权码和状态值
        const urlParams = new URLSearchParams(window.location.search);
        const code = urlParams.get('code');
        const state = urlParams.get('state');
        const returnUri = urlParams.get('returnUri');
        
        if (!code || !state) {
          throw new Error('授权回调缺少必要参数');
        }
        
        console.log('开始处理SSO回调 - code:', code.substring(0, 10) + '...', 'state:', state);
        
        // 处理授权回调，获取访问令牌和用户信息
        const { accessToken, userInfo } = await SSOService.handleCallback(code, state);
        
        // 创建用户对象
        const userData = {
          id: userInfo.userId || userInfo.id || '1',
          username: userInfo.username || 'SSO User',
          email: userInfo.email || '<EMAIL>',
          role: userInfo.role || 'user',
          department: userInfo.department || ''
        };
        
        // 将用户信息保存到本地存储
        localStorage.setItem('user', JSON.stringify(userData));
        localStorage.setItem('access_token', accessToken);
        
        console.log('SSO回调处理成功，准备跳转');
        
        // 如果有返回 URI，则重定向到该 URI
        if (returnUri) {
          window.location.href = decodeURIComponent(returnUri);
        } else {
          // 否则重定向到首页
          window.location.href = '/';
        }
      } catch (error) {
        console.error('处理授权回调时出错:', error);
        setError(error instanceof Error ? error.message : '授权失败，请重试');
      }
    };
    
    handleCallback();
  }, [navigate, user]);
  
  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-100">
      <div className="w-full max-w-md bg-white rounded-lg shadow-lg overflow-hidden">
        {!error ? (
          <div>
            <div className="bg-gradient-to-r from-blue-500 to-blue-600 px-8 py-6">
              <div className="flex items-center justify-center mb-4">
                <img src="/img_home_logo_white.png" alt="Logo" className="h-8 w-auto" />
              </div>
              <h2 className="text-xl text-white font-medium text-center">授权处理中...</h2>
            </div>
            <div className="px-8 py-6">
              <div className="text-center space-y-3">
                <div className="bg-blue-50 rounded-lg px-6 py-4">
                  <p className="font-medium text-blue-900">让运营更高效 · 更智能 · 更便捷</p>
                  <div className="w-16 h-0.5 bg-blue-500/50 mx-auto my-3"></div>
                  <p className="text-sm text-blue-800">让服务更有价值</p>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div>
            <div className="bg-red-500 px-8 py-6">
              <div className="flex items-center justify-center mb-4">
                <img src="/img_home_logo_white.png" alt="Logo" className="h-8 w-auto" />
              </div>
              <h2 className="text-xl text-white font-medium text-center">授权失败</h2>
            </div>
            <div className="px-8 py-6">
              <div className="bg-red-50 rounded-lg px-6 py-4 mb-4">
                <p className="text-red-600 text-sm text-center">{error}</p>
              </div>
              <button
                onClick={() => navigate('/login')}
                className="w-full px-4 py-2 bg-red-500 text-white text-sm rounded hover:bg-red-600 transition-colors"
              >
                返回登录页
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AuthCallback;
