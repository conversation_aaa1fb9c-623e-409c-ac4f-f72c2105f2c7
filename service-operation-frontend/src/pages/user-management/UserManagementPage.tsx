import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import {
  Users,
  Shield,
  Key,
  UserCheck,
  Settings
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { toast } from '@/hooks/use-toast';
import MainLayout from '@/components/layout/MainLayout';
import UserManagementTab from '@/components/user-management/UserManagementTab';
import RoleManagementTab from '@/components/user-management/RoleManagementTab';
import PermissionManagementTab from '@/components/user-management/PermissionManagementTab';
import UserPermissionManagementTab from '@/components/user-management/UserPermissionManagementTab';

// Tab类型定义
type TabType = 'users' | 'roles' | 'permissions' | 'user-permissions';

const UserManagementPage: React.FC = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const [activeTab, setActiveTab] = useState<TabType>('users');
  const [loading, setLoading] = useState(false);

  // 从URL参数获取当前Tab
  useEffect(() => {
    const tab = searchParams.get('tab') as TabType;
    if (tab && ['users', 'roles', 'permissions', 'user-permissions'].includes(tab)) {
      setActiveTab(tab);
    }
  }, [searchParams]);

  // 处理Tab切换
  const handleTabChange = (tab: TabType) => {
    setActiveTab(tab);
    setSearchParams({ tab });
  };

  // 渲染Tab内容
  const renderTabContent = () => {
    switch (activeTab) {
      case 'users':
        return <UserManagementTab />;
      case 'roles':
        return <RoleManagementTab />;
      case 'permissions':
        return <PermissionManagementTab />;
      case 'user-permissions':
        return <UserPermissionManagementTab />;
      default:
        return <UserManagementTab />;
    }
  };

  if (loading) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <span className="text-lg text-gray-600">加载中...</span>
          </div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* 页面标题 */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">用户权限管理</h1>
            <p className="text-gray-600">管理系统用户、角色和权限</p>
          </div>
        </div>

        {/* Tab导航 */}
        <div className="mb-6">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8" aria-label="Tabs">
              <button
                onClick={() => handleTabChange('users')}
                className={`py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap transition-colors duration-200 ${
                  activeTab === 'users'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center gap-2">
                  <Users className="h-4 w-4" />
                  用户管理
                </div>
              </button>
              
              <button
                onClick={() => handleTabChange('roles')}
                className={`py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap transition-colors duration-200 ${
                  activeTab === 'roles'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center gap-2">
                  <Shield className="h-4 w-4" />
                  角色管理
                </div>
              </button>
              
              <button
                onClick={() => handleTabChange('permissions')}
                className={`py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap transition-colors duration-200 ${
                  activeTab === 'permissions'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center gap-2">
                  <Key className="h-4 w-4" />
                  权限管理
                </div>
              </button>
              
              <button
                onClick={() => handleTabChange('user-permissions')}
                className={`py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap transition-colors duration-200 ${
                  activeTab === 'user-permissions'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center gap-2">
                  <UserCheck className="h-4 w-4" />
                  用户权限管理
                </div>
              </button>
            </nav>
          </div>
        </div>

        {/* Tab内容区域 */}
        <div className="min-h-96">
          {renderTabContent()}
        </div>
      </div>
    </MainLayout>
  );
};

export default UserManagementPage;
