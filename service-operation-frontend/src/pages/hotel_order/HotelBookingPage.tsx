import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, useSearchParams } from 'react-router-dom';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import MainLayout from '@/components/layout/MainLayout';

import HotelBookingContent from '@/components/booking/HotelBookingContent';
import HotelOrderDetailModal from '@/components/booking/HotelOrderDetailModal';
import { 
  ArrowLeft, 
  Building, 
  RefreshCw,
  Eye,
  Edit,
  Search,
  Clock2Icon,
  AlertTriangle,
  AlertCircle,
  Loader,
  Settings,
  Trash2,
  X,
  Download,
  CheckCircle,
  XCircle,
  Clock,
  Send,
  DollarSign,
  CreditCard,
  FileText,
  Bell,
  User,
} from 'lucide-react';
import { ProjectService } from '@/services/project';
import { Project } from '@/types/project';
import { ProjectTaskService } from '@/services/project/projectTaskService';
import { ProjectTask } from '@/types/project-task';
import { useToast } from '@/hooks/use-toast';

import * as XLSX from 'xlsx-js-style';
import { saveAs } from 'file-saver';

import { 
  hotelOrderApi, 
  HotelOrder,
  ProjectOrderDetailStatsResponse
} from '@/api/hotel';

// Tab类型定义
type TabType = 'booking' | 'all-orders' | 'exception-orders' | 'details';

// 可编辑输入组件 - 独立定义避免重新渲染导致焦点丢失
// EditableInput组件已移至HotelOrderDetailModal组件中

const HotelBookingPage: React.FC = () => {
  const { projectId } = useParams<{ projectId: string }>();
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  const initialTab = searchParams.get('tab') as TabType || 'booking';
  const { toast } = useToast();
  
  const [project, setProject] = useState<Project | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<TabType>(initialTab);

  // 所有订单相关状态
  const [allOrders, setAllOrders] = useState<HotelOrder[]>([]);
  const [allOrdersLoading, setAllOrdersLoading] = useState(false);
  const [allOrdersPage, setAllOrdersPage] = useState(1);
  const [allOrdersPageSize, setAllOrdersPageSize] = useState(20);
  const [allOrdersTotal, setAllOrdersTotal] = useState(0);

  // 异常订单相关状态
  const [exceptionOrders, setExceptionOrders] = useState<HotelOrder[]>([]);
  const [exceptionOrdersLoading, setExceptionOrdersLoading] = useState(false);
  const [exceptionOrdersPage, setExceptionOrdersPage] = useState(1);
  const [exceptionOrdersPageSize] = useState(20);
  const [exceptionOrdersTotal, setExceptionOrdersTotal] = useState(0);

  // 搜索相关状态
  const [searchGuestName, setSearchGuestName] = useState('');
  const [searchMobilePhone, setSearchMobilePhone] = useState('');
  const [searchContactPhone, setSearchContactPhone] = useState('');

  // 状态筛选相关状态
  const [selectedStatuses, setSelectedStatuses] = useState<string[]>([
    'check_failed', 'initial', 'submitted', 'processing', 'completed', 'failed'
  ]);
  const [exceptionSelectedStatuses, setExceptionSelectedStatuses] = useState<string[]>([
    'check_failed', 'failed'
  ]);

  // 预订任务相关状态
  const [bookingTasks, setBookingTasks] = useState<any[]>([]);
  const [bookingTasksLoading, setBookingTasksLoading] = useState(false);
  const [taskStats, setTaskStats] = useState<{[key: string]: any}>({});

  // 项目统计数据
  const [projectStats, setProjectStats] = useState<ProjectOrderDetailStatsResponse | null>(null);

  // 查看详情相关状态 - 简化为只需要viewingOrder，其他由模态框组件管理
  const [viewingOrder, setViewingOrder] = useState<HotelOrder | null>(null);
  const [editingOrder, setEditingOrder] = useState<HotelOrder | null>(null);

  // 删除确认对话框状态
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [orderToDelete, setOrderToDelete] = useState<HotelOrder | null>(null);

  useEffect(() => {
    if (projectId) {
      loadProjectDetails();
      if (activeTab === 'all-orders') {
        loadAllOrders();
        loadProjectStats();
      } else if (activeTab === 'exception-orders') {
        loadExceptionOrders();
      } else if (activeTab === 'details') {
        loadBookingTasks();
      }
    }
  }, [projectId, activeTab]);

  // 监听状态筛选变化
  useEffect(() => {
    if (projectId && activeTab === 'all-orders') {
      setAllOrdersPage(1);
      loadAllOrders(1);
    }
  }, [selectedStatuses]);

  // 监听pageSize变化
  useEffect(() => {
    if (projectId && activeTab === 'all-orders') {
      setAllOrdersPage(1);
      loadAllOrders(1);
    }
  }, [allOrdersPageSize]);

  // 监听异常订单状态筛选变化
  useEffect(() => {
    if (projectId && activeTab === 'exception-orders') {
      setExceptionOrdersPage(1);
      loadExceptionOrders(1);
    }
  }, [exceptionSelectedStatuses]);

  const loadProjectDetails = async () => {
    try {
      setLoading(true);
      const response = await ProjectService.getProject(Number(projectId));
      setProject(response.data || null);
    } catch (error) {
      console.error('加载项目详情失败:', error);
      setError('加载项目详情失败');
      toast({
        title: "加载失败",
        description: "无法加载项目详情",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const loadAllOrders = async (page: number = 1) => {
    try {
      setAllOrdersLoading(true);
      const statusParam = selectedStatuses.length > 0 ? selectedStatuses.join(',') : undefined;
      
      const response = await hotelOrderApi.getProjectOrders(
        Number(projectId),
        page,
        allOrdersPageSize,
        statusParam,
        searchGuestName || undefined,
        searchMobilePhone || undefined,
        searchContactPhone || undefined,
        true // 将验证失败和预定失败的订单排在前面
      );
      
      setAllOrders(response.items);
      setAllOrdersTotal(response.total);
      setAllOrdersPage(page);
    } catch (error) {
      console.error('加载所有订单失败:', error);
      toast({
        title: "加载失败",
        description: "无法加载订单列表",
        variant: "destructive",
      });
    } finally {
      setAllOrdersLoading(false);
    }
  };

  const loadProjectStats = async () => {
    try {
      const response = await hotelOrderApi.getProjectDetailStats(Number(projectId));
      setProjectStats(response);
    } catch (error) {
      console.error('加载项目统计失败:', error);
    }
  };

  const loadExceptionOrders = async (page: number = 1) => {
    try {
      setExceptionOrdersLoading(true);
      const statusParam = exceptionSelectedStatuses.length > 0 ? exceptionSelectedStatuses.join(',') : 'check_failed,failed';
      
      const response = await hotelOrderApi.getProjectOrders(
        Number(projectId),
        page,
        exceptionOrdersPageSize,
        statusParam,
        searchGuestName || undefined,
        searchMobilePhone || undefined,
        searchContactPhone || undefined,
        true // 将验证失败和预定失败的订单排在前面
      );
      
      setExceptionOrders(response.items);
      setExceptionOrdersTotal(response.total);
      setExceptionOrdersPage(page);
    } catch (error) {
      console.error('加载异常订单失败:', error);
      toast({
        title: "加载失败",
        description: "无法加载异常订单列表",
        variant: "destructive",
      });
    } finally {
      setExceptionOrdersLoading(false);
    }
  };

  // 异常订单搜索处理函数
  const handleExceptionSearch = () => {
    setExceptionOrdersPage(1);
    loadExceptionOrders(1);
  };

  const handleClearExceptionSearch = () => {
    setSearchGuestName('');
    setSearchMobilePhone('');
    setSearchContactPhone('');
    setExceptionOrdersPage(1);
    loadExceptionOrders(1);
  };

  // 导出异常订单Excel
  const exportExceptionOrders = async () => {
    if (exceptionOrders.length === 0) {
      toast({
        title: "导出失败",
        description: "没有异常订单数据可导出",
        variant: "destructive",
      });
      return;
    }

    try {
      const filename = `酒店异常订单_${project?.project_name || 'Unknown'}_${new Date().toLocaleDateString('zh-CN').replace(/\//g, '-')}.xlsx`;
      const title = `${project?.project_name || '项目' + projectId} - 酒店异常订单明细`;
      exportToExcel(exceptionOrders, filename, title);
      
      toast({
        title: "导出成功",
        description: `已导出 ${exceptionOrders.length} 条异常订单记录`,
      });
    } catch (error) {
      console.error('导出异常订单失败:', error);
      toast({
        title: "导出失败",
        description: "导出异常订单时发生错误",
        variant: "destructive",
      });
    }
  };

  const handleSearch = () => {
    if (activeTab === 'all-orders') {
      setAllOrdersPage(1);
      loadAllOrders(1);
    } else if (activeTab === 'exception-orders') {
      setExceptionOrdersPage(1);
      loadExceptionOrders(1);
    }
  };

  const handleClearSearch = () => {
    setSearchGuestName('');
    setSearchMobilePhone('');
    setSearchContactPhone('');
    setTimeout(() => {
      if (activeTab === 'all-orders') {
        setAllOrdersPage(1);
        loadAllOrders(1);
      } else if (activeTab === 'exception-orders') {
        setExceptionOrdersPage(1);
        loadExceptionOrders(1);
      }
    }, 0);
  };

  // 获取任务统计信息
  const getTaskStatistics = async (taskId: string) => {
    try {
      // 获取该任务相关的订单，然后统计
      const response = await hotelOrderApi.getTaskOrders(taskId, 1, 1000); // 获取足够多的订单进行统计
      const orders = response.items;
      
      const stats = {
        order_count: orders.length,
        total_amount: orders.reduce((sum, order) => sum + (order.amount || 0), 0),
        completed_orders: orders.filter(order => order.order_status === 'completed').length,
        failed_orders: orders.filter(order => order.order_status === 'failed').length,
        submitted_orders: orders.filter(order => order.order_status === 'submitted').length,
        processing_orders: orders.filter(order => order.order_status === 'processing').length,
        check_failed_orders: orders.filter(order => order.order_status === 'check_failed').length
      };
      
      return stats;
    } catch (error) {
      console.error(`获取任务 ${taskId} 统计信息失败:`, error);
      return {
        order_count: 0,
        total_amount: 0,
        completed_orders: 0,
        failed_orders: 0,
        submitted_orders: 0,
        processing_orders: 0,
        check_failed_orders: 0
      };
    }
  };

  const loadBookingTasks = async () => {
    if (!projectId) return;
    
    try {
      setBookingTasksLoading(true);
      const response = await ProjectTaskService.getTasksByProject(parseInt(projectId), {
        page: 1,
        page_size: 100
      });
      
      // 只获取酒店预订类型的任务，按创建时间倒序排列
      const hotelTasks = response.items
        .filter((task: ProjectTask) => task.task_type === '酒店预订')
        .sort((a: ProjectTask, b: ProjectTask) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
      
      setBookingTasks(hotelTasks);

      // 获取每个酒店任务的统计信息
      const statsPromises = hotelTasks.map(async (task) => {
        const stats = await getTaskStatistics(task.task_id);
        return { taskId: task.task_id, stats };
      });

      const statsResults = await Promise.all(statsPromises);
      const statsMap = statsResults.reduce((acc, { taskId, stats }) => {
        acc[taskId] = stats;
        return acc;
      }, {} as {[key: string]: any});

      setTaskStats(statsMap);
    } catch (error) {
      console.error('加载预订任务失败:', error);
      toast({
        title: "加载失败",
        description: "无法加载预订任务列表",
        variant: "destructive",
      });
    } finally {
      setBookingTasksLoading(false);
    }
  };

  // 编辑订单相关函数
  const handleViewOrder = (order: HotelOrder) => {
    setViewingOrder(order);
  };

  const handleEditOrder = (order: HotelOrder) => {
    setViewingOrder(order);
    setEditingOrder(order);
  };

  // 编辑功能现在由HotelOrderDetailModal组件处理，移除相关函数

  const handleDeleteOrder = (order: HotelOrder) => {
    setOrderToDelete(order);
    setShowDeleteConfirm(true);
  };

  const confirmDeleteOrder = async () => {
    if (!orderToDelete) return;
    
    try {
      await hotelOrderApi.deleteOrder(orderToDelete.id);
      
      toast({
        title: "删除成功",
        description: `订单 ${orderToDelete.guest_full_name} 已删除`,
      });
      
      // 重新加载订单列表
      loadAllOrders(allOrdersPage);
      
    } catch (error) {
      console.error('删除订单失败:', error);
      toast({
        title: "删除失败",
        description: "删除订单时发生错误",
        variant: "destructive",
      });
    } finally {
      setShowDeleteConfirm(false);
      setOrderToDelete(null);
    }
  };

  const handleTabChange = (tab: TabType) => {
    setActiveTab(tab);
    setSearchParams(prev => {
      prev.set('tab', tab);
      return prev;
    });
  };

  // 导出Excel功能
  const exportToExcel = (data: HotelOrder[], filename: string, title?: string) => {
    const headers = [
      '序号', '状态', '入住人姓名', '入住人姓', '入住人名', '国籍', '性别', '出生日期',
      '证件类型', '证件号码', '手机号', '邮箱', '目的地', '酒店ID', '酒店名称', '房型',
      '房间数量', '入住时间', '离店时间', '是否团队预订', '团队预订名称', '联系人',
      '联系人手机', '成本中心', '审批人', '金额', '订单号', '账单号', '失败原因'
    ];

    const excelData = data.map((order, index) => [
      index + 1,
      getOrderStatusText(order.order_status),
      order.guest_full_name || '',
      order.guest_surname || '',
      order.guest_given_name || '',
      order.guest_nationality || '',
      order.guest_gender || '',
      order.guest_birth_date || '',
      order.guest_id_type || '',
      order.guest_id_number || '',
      order.guest_mobile_phone || '',
      order.guest_email || '',
      order.destination || '',
      order.hotel_id || '',
      order.hotel_name || '',
      order.room_type || '',
      order.room_count || '',
      order.check_in_time || '',
      order.check_out_time || '',
      order.is_group_booking || '',
      order.group_booking_name || '',
      order.contact_person || '',
      order.contact_mobile_phone || '',
      order.cost_center || '',
      order.approver || '',
      order.amount || '',
      order.order_number || '',
      order.bill_number || '',
      order.fail_reason || ''
    ]);

    const ws = XLSX.utils.aoa_to_sheet([headers, ...excelData]);
    
    // 设置列宽
    const colWidths = headers.map((_, i) => ({ wch: i === 0 ? 8 : 15 }));
    ws['!cols'] = colWidths;
    
    // 设置标题样式
    const headerStyle = {
      font: { bold: true, color: { rgb: 'FFFFFF' } },
      fill: { fgColor: { rgb: '366092' } },
      alignment: { horizontal: 'center', vertical: 'center' }
    };
    
    // 应用标题样式
    headers.forEach((_, i) => {
      const cellRef = XLSX.utils.encode_cell({ r: 0, c: i });
      if (!ws[cellRef]) ws[cellRef] = {};
      ws[cellRef].s = headerStyle;
    });

    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, title || '酒店订单');
    
    // 生成Excel文件并下载
    const excelBuffer = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });
    const blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
    saveAs(blob, filename);
  };

  const exportAllOrders = async () => {
    try {
      // 获取所有订单数据（不分页）
      const response = await hotelOrderApi.getProjectOrders(
        Number(projectId),
        1,
        99999, // 大数值获取所有数据
        selectedStatuses.join(','),
        searchGuestName || undefined,
        searchMobilePhone || undefined,
        searchContactPhone || undefined,
        true
      );
      
      const filename = `酒店订单_${project?.project_name || 'Unknown'}_${new Date().toISOString().split('T')[0]}.xlsx`;
      exportToExcel(response.items, filename, '所有订单');
      
      toast({
        title: "导出成功",
        description: `已导出 ${response.items.length} 条订单记录`,
      });
    } catch (error) {
      console.error('导出失败:', error);
      toast({
        title: "导出失败",
        description: "导出订单数据时发生错误",
        variant: "destructive",
      });
    }
  };

  // 获取状态文本（用于导出）
  const getOrderStatusText = (status: string): string => {
    const statusMap = {
      'check_failed': '验证失败',
      'initial': '待提交',
      'submitted': '已提交',
      'processing': '处理中',
      'completed': '已完成',
      'failed': '预订失败'
    };
    return statusMap[status as keyof typeof statusMap] || status;
  };

  const getOrderStatusDisplay = (status: string) => {
    const statusMap = {
      'check_failed': { text: '验证失败', color: 'text-amber-600', bgColor: 'bg-amber-50' },
      'initial': { text: '待提交', color: 'text-gray-600', bgColor: 'bg-gray-50' },
      'submitted': { text: '已提交', color: 'text-blue-600', bgColor: 'bg-blue-50' },
      'processing': { text: '处理中', color: 'text-purple-600', bgColor: 'bg-purple-50' },
      'completed': { text: '已完成', color: 'text-green-600', bgColor: 'bg-green-50' },
      'failed': { text: '预订失败', color: 'text-red-600', bgColor: 'bg-red-50' }
    };
    const statusConfig = statusMap[status as keyof typeof statusMap] || { text: status, color: 'text-gray-600', bgColor: 'bg-gray-50' };
    
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusConfig.color} ${statusConfig.bgColor}`}>
        {statusConfig.text}
      </span>
    );
  };

  // 格式化金额
  const formatAmount = (amount: number | string | undefined | null): string => {
    if (!amount) return '¥0.00';
    const num = typeof amount === 'string' ? parseFloat(amount) : amount;
    if (isNaN(num)) return '¥0.00';
    return `¥${num.toFixed(2)}`;
  };

  // 基于project_tasks表的task_status字段获取状态显示
  const getProjectTaskStatusDisplay = (task_status: string) => {
    const statusMap = {
      'pending': { text: '待处理', color: 'bg-yellow-100 text-yellow-700' },
      'submitted': { text: '已提交', color: 'bg-blue-100 text-blue-700' },
      'processing': { text: '处理中', color: 'bg-blue-100 text-blue-700' },
      'in_progress': { text: '进行中', color: 'bg-blue-100 text-blue-700' },
      'completed': { text: '已完成', color: 'bg-green-100 text-green-700' },
      'failed': { text: '失败', color: 'bg-red-100 text-red-700' },
      'cancelled': { text: '已取消', color: 'bg-gray-100 text-gray-700' }
    };
    return statusMap[task_status as keyof typeof statusMap] || { text: task_status, color: 'bg-gray-100 text-gray-600' };
  };

  // 获取任务类型配置
  const getTaskTypeConfig = (task_type: string) => {
    const typeConfigs = {
      '火车票预订': {
        icon: Building,
        color: 'text-blue-600',
        bgColor: 'bg-blue-50',
        borderColor: 'border-blue-200',
        buttonColor: 'bg-blue-600 hover:bg-blue-700'
      },
      '飞机票预订': {
        icon: '✈️',
        color: 'text-green-600',
        bgColor: 'bg-green-50',
        borderColor: 'border-green-200',
        buttonColor: 'bg-green-600 hover:bg-green-700'
      },
      '机票预订': {
        icon: '✈️',
        color: 'text-green-600',
        bgColor: 'bg-green-50',
        borderColor: 'border-green-200',
        buttonColor: 'bg-green-600 hover:bg-green-700'
      },
      '酒店预订': {
        icon: '🏨',
        color: 'text-purple-600',
        bgColor: 'bg-purple-50',
        borderColor: 'border-purple-200',
        buttonColor: 'bg-purple-600 hover:bg-purple-700'
      },
      '交通安排': {
        icon: '🚗',
        color: 'text-orange-600',
        bgColor: 'bg-orange-50',
        borderColor: 'border-orange-200',
        buttonColor: 'bg-orange-600 hover:bg-orange-700'
      },
      '会议安排': {
        icon: '🏢',
        color: 'text-indigo-600',
        bgColor: 'bg-indigo-50',
        borderColor: 'border-indigo-200',
        buttonColor: 'bg-indigo-600 hover:bg-indigo-700'
      }
    };
    return typeConfigs[task_type as keyof typeof typeConfigs] || {
      icon: FileText,
      color: 'text-gray-600',
      bgColor: 'bg-gray-50',
      borderColor: 'border-gray-200',
      buttonColor: 'bg-gray-600 hover:bg-gray-700'
    };
  };

  // 获取任务状态标签（结合project_tasks状态和统计数据）
  const getTaskStatusLabel = (task: ProjectTask, stats: any) => {
    // 优先显示project_tasks表的task_status
    const projectTaskStatus = getProjectTaskStatusDisplay(task.task_status);
    
    // 如果有统计数据，可以进一步细化状态
    if (stats && stats.order_count > 0) {
      if (stats.failed_orders > 0) {
        return { text: '部分失败', color: 'bg-red-100 text-red-700' };
      }
      if (stats.completed_orders === stats.order_count && task.task_status === 'completed') {
        return { text: '全部完成', color: 'bg-green-100 text-green-700' };
      }
    }
    
    return projectTaskStatus;
  };

  // 渲染基于project_tasks表的任务卡片
  const renderProjectTaskCard = (task: ProjectTask) => {
    const stats = taskStats[task.task_id] || { order_count: 0, total_amount: 0, completed_orders: 0, failed_orders: 0, submitted_orders: 0, processing_orders: 0, check_failed_orders: 0 };
    const statusInfo = getTaskStatusLabel(task, stats);
    const typeConfig = getTaskTypeConfig(task.task_type);

    return (
      <Card key={task.id} className={`bg-white border ${typeConfig.borderColor} hover:shadow-md hover:scale-105 transition-all duration-200 overflow-hidden`}>
        <div className="p-4">
          {/* 卡片头部 - 更紧凑的布局 */}
          <div className="flex items-start justify-between mb-3">
            <div className="flex items-center gap-2">
              <div className={`p-1.5 ${typeConfig.bgColor} rounded-md`}>
                {typeof typeConfig.icon === 'string' ? (
                  <span className="text-sm">{typeConfig.icon}</span>
                ) : (
                  <typeConfig.icon className={`h-4 w-4 ${typeConfig.color}`} />
                )}
              </div>
              <div className="flex-1 min-w-0">
                <h3 className="text-sm font-semibold text-gray-900 truncate">{task.task_type}</h3>
                <p className="text-xs text-gray-500 truncate">{task.task_title}</p>
              </div>
            </div>
            <span className={`px-2 py-0.5 rounded-full text-xs font-medium whitespace-nowrap ${statusInfo.color}`}>
              {statusInfo.text}
            </span>
          </div>

          {/* 统计数据 - 按状态显示订单数量 */}
          <div className="grid grid-cols-4 gap-2 mb-3">
            <div className="text-center">
              <div className="text-lg font-bold text-blue-600">{stats.submitted_orders || 0}</div>
              <div className="text-xs text-gray-500">已提交</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-bold text-cyan-600">{stats.processing_orders || 0}</div>
              <div className="text-xs text-gray-500">处理中</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-bold text-green-600">{stats.completed_orders || 0}</div>
              <div className="text-xs text-gray-500">预定完成</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-bold text-red-600">{stats.failed_orders || 0}</div>
              <div className="text-xs text-gray-500">预定失败</div>
            </div>
          </div>

          {/* 分割线 */}
          <div className="border-t border-gray-200 mb-3"></div>

          {/* 任务信息 - 更简洁的展示 */}
          <div className="space-y-1 mb-3">
            <div className="flex items-center justify-between text-xs">
              <span className="text-gray-500">创建人</span>
              <span className="text-gray-900 font-medium">{task.creator_name}</span>
            </div>
            <div className="flex items-center justify-between text-xs">
              <span className="text-gray-500">创建时间</span>
              <span className="text-gray-900">{new Date(task.created_at).toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })}</span>
            </div>
            {/* 特殊标记 - 固定高度确保按钮对齐 */}
            <div className="flex gap-2 mt-1 h-6 items-center">
              {(task as any).sms_notify && (
                <div className="flex items-center gap-1 px-1.5 py-0.5 bg-blue-50 rounded text-xs text-blue-600">
                  <Bell className="h-2.5 w-2.5" />
                  <span>短信</span>
                </div>
              )}
              {(task as any).has_agent && (
                <div className="flex items-center gap-1 px-1.5 py-0.5 bg-green-50 rounded text-xs text-green-600">
                  <User className="h-2.5 w-2.5" />
                  <span>代订</span>
                </div>
              )}
            </div>
          </div>

          {/* 单个操作按钮 - 更现代的设计 */}
          <Button
            size="sm"
            className={`w-full ${typeConfig.buttonColor} text-white hover:opacity-90 transition-opacity`}
            onClick={() => {
              if (task.task_type === '酒店预订') {
                navigate(`/hotel-task-detail/${task.task_id}`);
              } else {
                console.log('查看任务详情:', task.task_id);
              }
            }}
          >
            <Eye className="h-4 w-4 mr-1" />
            查看详情
          </Button>
        </div>
      </Card>
    );
  };

  // Excel上传处理函数（暂时保留，后续实现时使用）
  // const handleExcelUpload = async (file: File) => {
  //   try {
  //     await hotelOrderApi.uploadExcel(Number(projectId), file);
  //     toast({
  //       title: "上传成功",
  //       description: "Excel文件上传成功",
  //     });
  //     // 重新加载订单列表
  //     if (activeTab === 'all-orders') {
  //       loadAllOrders();
  //     }
  //   } catch (error) {
  //     console.error('Excel上传失败:', error);
  //     toast({
  //       title: "上传失败",
  //       description: "Excel文件上传失败",
  //       variant: "destructive",
  //     });
  //   }
  // };

  // const handleClearOrders = async () => {
  //   if (!confirm('确定要清空所有酒店订单吗？此操作不可撤销。')) {
  //     return;
  //   }
    
  //   try {
  //     await hotelOrderApi.clearOrders(Number(projectId));
  //     toast({
  //       title: "清空成功",
  //       description: "所有酒店订单已清空",
  //     });
  //     // 重新加载数据
  //     loadAllOrders();
  //   } catch (error) {
  //     console.error('清空订单失败:', error);
  //     toast({
  //       title: "清空失败",
  //       description: "清空订单失败",
  //       variant: "destructive",
  //     });
  //   }
  // };

  // 渲染预订内容
  const renderBookingContent = () => {
    return <HotelBookingContent onNavigateToAllOrders={() => handleTabChange('all-orders')} />;
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'booking':
        return renderBookingContent();
      case 'all-orders':
        return renderAllOrdersTab();
      case 'exception-orders':
        return renderExceptionOrdersTab();
      case 'details':
        return renderDetailsTab();
      default:
        return null;
    }
  };

  const renderAllOrdersTab = () => {
    // 计算各状态订单数量（前端计算，用于状态筛选显示）
    const statusCounts = allOrders.reduce((counts, order) => {
      counts[order.order_status] = (counts[order.order_status] || 0) + 1;
      return counts;
    }, {} as Record<string, number>);

    return (
      <div className="space-y-4">
        {/* 订单统计卡片 */}
        {allOrdersTotal > 0 && (
          <Card className="bg-white border border-gray-200">
            <div className="p-4">
              <h3 className="text-sm font-medium text-gray-900 mb-3">订单状态统计</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-4">
                {/* 总订单数 */}
                <div className="text-center">
                  <div className="p-2 bg-purple-100 rounded-lg mx-auto w-fit mb-1">
                    <CreditCard className="h-4 w-4 text-purple-600" />
                  </div>
                  <p className="text-lg font-medium text-gray-900">{projectStats?.total_orders || allOrdersTotal}</p>
                  <p className="text-xs text-gray-500">总订单数</p>
                </div>
                {/* 验证失败 */}
                <div className="text-center">
                  <div className="p-2 bg-amber-100 rounded-lg mx-auto w-fit mb-1">
                    <AlertCircle className="h-4 w-4 text-amber-600" />
                  </div>
                  <p className="text-lg font-medium text-gray-900">{statusCounts['check_failed'] || 0}</p>
                  <p className="text-xs text-gray-500">验证失败</p>
                </div>
                {/* 待提交 */}
                <div className="text-center">
                  <div className="p-2 bg-gray-100 rounded-lg mx-auto w-fit mb-1">
                    <Clock className="h-4 w-4 text-gray-600" />
                  </div>
                  <p className="text-lg font-medium text-gray-900">{statusCounts['initial'] || 0}</p>
                  <p className="text-xs text-gray-500">待提交</p>
                </div>
                {/* 已提交 */}
                <div className="text-center">
                  <div className="p-2 bg-blue-100 rounded-lg mx-auto w-fit mb-1">
                    <Send className="h-4 w-4 text-blue-600" />
                  </div>
                  <p className="text-lg font-medium text-gray-900">{statusCounts['submitted'] || 0}</p>
                  <p className="text-xs text-gray-500">已提交</p>
                </div>
                {/* 处理中 */}
                <div className="text-center">
                  <div className="p-2 bg-cyan-100 rounded-lg mx-auto w-fit mb-1">
                    <Loader className="h-4 w-4 text-cyan-600" />
                  </div>
                  <p className="text-lg font-medium text-gray-900">{statusCounts['processing'] || 0}</p>
                  <p className="text-xs text-gray-500">处理中</p>
                </div>
                {/* 预定完成 */}
                <div className="text-center">
                  <div className="p-2 bg-green-100 rounded-lg mx-auto w-fit mb-1">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                  </div>
                  <p className="text-lg font-medium text-gray-900">{statusCounts['completed'] || 0}</p>
                  <p className="text-xs text-gray-500">预定完成</p>
                </div>
                {/* 预定失败 */}
                <div className="text-center">
                  <div className="p-2 bg-red-100 rounded-lg mx-auto w-fit mb-1">
                    <XCircle className="h-4 w-4 text-red-600" />
                  </div>
                  <p className="text-lg font-medium text-gray-900">{statusCounts['failed'] || 0}</p>
                  <p className="text-xs text-gray-500">预定失败</p>
                </div>
                {/* 完成金额 */}
                <div className="text-center">
                  <div className="p-2 bg-purple-100 rounded-lg mx-auto w-fit mb-1">
                    <DollarSign className="h-4 w-4 text-purple-600" />
                  </div>
                  <p className="text-lg font-medium text-gray-900">¥{projectStats?.total_amount || '0'}</p>
                  <p className="text-xs text-gray-500">完成金额</p>
                </div>
              </div>
            </div>
          </Card>
        )}

        <Card className="bg-white border border-gray-200 rounded-lg shadow-sm">
          <div className="border-b border-gray-200 p-4">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-2">
                <Eye className="h-5 w-5 text-gray-600" />
                <h3 className="text-lg font-medium text-gray-900">所有订单</h3>
                {allOrdersTotal > 0 && (
                  <span className="ml-2 bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded-full">
                    {allOrdersTotal}
                  </span>
                )}
              </div>
            </div>
            
            {/* 搜索区域 */}
            <div className="flex flex-col gap-3">
              {/* 第一行：状态筛选 */}
              <div className="flex items-center gap-3">
                <span className="text-sm font-medium text-gray-700 whitespace-nowrap">状态筛选：</span>
                <div className="flex flex-wrap gap-2">
                  {[
                    { value: 'check_failed', label: '验证失败', color: 'bg-amber-100 text-amber-700' },
                    { value: 'initial', label: '待提交', color: 'bg-gray-100 text-gray-600' },
                    { value: 'submitted', label: '已提交', color: 'bg-blue-100 text-blue-700' },
                    { value: 'processing', label: '处理中', color: 'bg-cyan-100 text-cyan-700' },
                    { value: 'completed', label: '预定完成', color: 'bg-green-100 text-green-700' },
                    { value: 'failed', label: '预定失败', color: 'bg-red-100 text-red-700' },
                  ].map((status) => (
                    <label
                      key={status.value}
                      className="flex items-center gap-1 cursor-pointer hover:bg-gray-50 p-1 rounded"
                    >
                      <input
                        type="checkbox"
                        checked={selectedStatuses.includes(status.value)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setSelectedStatuses([...selectedStatuses, status.value]);
                          } else {
                            setSelectedStatuses(selectedStatuses.filter(s => s !== status.value));
                          }
                        }}
                        className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
                      />
                      <span className={`px-2 py-1 rounded-full text-xs ${status.color}`}>
                        {status.label}
                      </span>
                    </label>
                  ))}
                </div>
              </div>
              
              {/* 第二行：搜索输入框 */}
              <div className="flex flex-col md:flex-row md:items-center gap-3">
                <div className="flex items-center gap-2 flex-1 max-w-xs">
                  <label className="text-sm text-gray-600 whitespace-nowrap min-w-fit">入住人姓名：</label>
            <input
              type="text"
                    placeholder="请输入入住人姓名"
              value={searchGuestName}
              onChange={(e) => setSearchGuestName(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                    className="flex-1 h-9 px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
            />
          </div>
                
                <div className="flex items-center gap-2 flex-1 max-w-xs">
                  <label className="text-sm text-gray-600 whitespace-nowrap min-w-fit">手机号码：</label>
            <input
              type="text"
                    placeholder="请输入手机号码"
              value={searchMobilePhone}
              onChange={(e) => setSearchMobilePhone(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                    className="flex-1 h-9 px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
            />
          </div>
                
                <div className="flex items-center gap-2 flex-1 max-w-xs">
                  <label className="text-sm text-gray-600 whitespace-nowrap min-w-fit">联系人手机：</label>
                  <input
                    type="text"
                    placeholder="请输入联系人手机号"
                    value={searchContactPhone}
                    onChange={(e) => setSearchContactPhone(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                    className="flex-1 h-9 px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                  />
                </div>

                <div className="flex items-center gap-2 ml-auto">
          <Button
                    size="sm"
                    onClick={handleSearch}
                    disabled={allOrdersLoading}
                    className="bg-blue-600 hover:bg-blue-700 text-white h-9"
                  >
                    <Search className="h-4 w-4 mr-2" />
            搜索
          </Button>

          <Button
            variant="outline"
            size="sm"
                    onClick={handleClearSearch}
            disabled={allOrdersLoading}
                    className="h-9"
          >
                    <X className="h-4 w-4 mr-2" />
                    重置
          </Button>

                  {allOrdersTotal > 0 && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={exportAllOrders}
                      disabled={allOrdersLoading}
                      className="h-9"
                    >
                      <Download className="h-4 w-4 mr-2" />
                      导出
                    </Button>
                  )}
          </div>
          </div>
            </div>
          </div>
          <div className="p-0">
            {allOrdersLoading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <p className="text-gray-600">加载中...</p>
              </div>
            ) : allOrders.length > 0 ? (
              <>
                <div className="overflow-x-auto custom-scrollbar" style={{ 
                  scrollbarWidth: 'auto', 
                  scrollbarColor: '#cbd5e1 #f1f5f9'
                }}>
                  <style dangerouslySetInnerHTML={{
                    __html: `
                      .custom-scrollbar::-webkit-scrollbar {
                        height: 12px;
                        background-color: #f1f5f9;
                      }
                      .custom-scrollbar::-webkit-scrollbar-track {
                        background-color: #f1f5f9;
                        border-radius: 6px;
                      }
                      .custom-scrollbar::-webkit-scrollbar-thumb {
                        background-color: #cbd5e1;
                        border-radius: 6px;
                        border: 2px solid #f1f5f9;
                      }
                      .custom-scrollbar::-webkit-scrollbar-thumb:hover {
                        background-color: #94a3b8;
                      }
                    `
                  }} />
                  <table className="w-full text-xs" style={{ minWidth: '3000px' }}>
                    <thead className="bg-gray-50 border-b border-gray-200">
                      <tr>
                        <th className="text-center p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '60px' }}>序号</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>状态</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '120px' }}>入住人姓名</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>入住人姓</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>入住人名</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>国籍</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '60px' }}>性别</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>出生日期</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>证件类型</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '150px' }}>证件号码</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '120px' }}>手机号</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '150px' }}>邮箱</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>目的地</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>酒店ID</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '150px' }}>酒店名称</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>房型</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>房间数量</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '120px' }}>入住时间</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '120px' }}>离店时间</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>团队预订</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '120px' }}>团队预订名称</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>联系人</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '120px' }}>联系人手机</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '120px' }}>成本中心</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>审批人</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>金额</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '120px' }}>订单号</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '120px' }}>账单号</th>
                        <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '150px' }}>失败原因</th>
                        <th className="text-center p-2 font-medium text-gray-900 text-xs whitespace-nowrap sticky right-0 bg-gray-50 border-l border-gray-200" style={{ minWidth: '120px' }}>操作</th>
                </tr>
              </thead>
              <tbody>
                      {allOrders.map((order, index) => (
                        <tr key={order.id} className="border-b border-gray-100 hover:bg-gray-50">
                          <td className="p-2 text-xs text-gray-900 whitespace-nowrap text-center">{(allOrdersPage - 1) * allOrdersPageSize + index + 1}</td>
                          <td className="p-2 text-xs whitespace-nowrap">
                            {getOrderStatusDisplay(order.order_status)}
                      </td>
                          <td className="p-2 text-xs font-medium text-gray-900 whitespace-nowrap">{order.guest_full_name || '-'}</td>
                          <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.guest_surname || '-'}</td>
                          <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.guest_given_name || '-'}</td>
                          <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.guest_nationality || '-'}</td>
                          <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.guest_gender || '-'}</td>
                          <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.guest_birth_date || '-'}</td>
                          <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.guest_id_type || '-'}</td>
                          <td className="p-2 text-xs text-gray-600 font-mono whitespace-nowrap">{order.guest_id_number || '-'}</td>
                          <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.guest_mobile_phone || '-'}</td>
                          <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.guest_email || '-'}</td>
                          <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.destination || '-'}</td>
                          <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.hotel_id || '-'}</td>
                          <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.hotel_name || '-'}</td>
                          <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.room_type || '-'}</td>
                          <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.room_count || '-'}</td>
                          <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.check_in_time || '-'}</td>
                          <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.check_out_time || '-'}</td>
                          <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.is_group_booking || '-'}</td>
                          <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.group_booking_name || '-'}</td>
                          <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.contact_person || '-'}</td>
                          <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.contact_mobile_phone || '-'}</td>
                          <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.cost_center || '-'}</td>
                          <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.approver || '-'}</td>
                          <td className="p-2 text-xs text-gray-900 font-medium whitespace-nowrap">
                            {formatAmount(order.amount)}
                          </td>
                          <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.order_number || '-'}</td>
                          <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.bill_number || '-'}</td>
                          <td className="p-2 text-xs text-gray-600 whitespace-nowrap relative group cursor-help">
                            {order.fail_reason ? (
                              <>
                                <span className={`${order.fail_reason.length > 15 ? 'border-b border-dotted border-gray-400' : ''}`}>
                                  {order.fail_reason.length > 15 ? order.fail_reason.substring(0, 15) + '...' : order.fail_reason}
                        </span>
                                {order.fail_reason.length > 15 && (
                                  <div className="invisible group-hover:visible absolute bottom-full mb-1 left-0 bg-gray-800 text-white text-xs rounded px-2 py-1 whitespace-normal max-w-xs z-50">
                                    {order.fail_reason}
                                  </div>
                                )}
                              </>
                            ) : (
                              '-'
                            )}
                      </td>
                          <td className="p-2 text-xs whitespace-nowrap sticky right-0 bg-white border-l border-gray-200 z-20">
                            <div className="flex items-center gap-1">
                          <Button
                            variant="ghost"
                                size="icon"
                                title="查看详情"
                                className="h-6 w-6 p-0"
                                onClick={() => handleViewOrder(order)}
                              >
                                <Eye className="h-4 w-4" />
                          </Button>
                              {/* 已提交和处理中的订单不能编辑 */}
                              {!(order.order_status === 'submitted' || order.order_status === 'processing') && (
                          <Button
                            variant="ghost"
                                  size="icon"
                                  title="编辑订单"
                                  className="h-6 w-6 p-0 text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                                  onClick={() => handleEditOrder(order)}
                                >
                                  <Edit className="h-4 w-4" />
                          </Button>
                              )}
                              {/* 只有验证失败和待提交的订单可以删除 */}
                              {(order.order_status === 'check_failed' || order.order_status === 'initial') && (
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  title="删除订单"
                                  className="h-6 w-6 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                                  onClick={() => handleDeleteOrder(order)}
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              )}
                        </div>
                      </td>
                    </tr>
                      ))}
              </tbody>
            </table>
          </div>
                
                {/* 分页 - 始终显示 */}
                <div className="flex items-center justify-between p-4 border-t border-gray-200">
                  <div className="text-sm text-gray-700">
                    {allOrdersTotal > 0 ? (
                      <>显示第 {(allOrdersPage - 1) * allOrdersPageSize + 1} 到 {Math.min(allOrdersPage * allOrdersPageSize, allOrdersTotal)} 条，共 {allOrdersTotal} 条记录</>
                    ) : (
                      <>共 0 条记录</>
                    )}
            </div>
                  <div className="flex items-center space-x-4">
                    {/* 每页显示数量选择 */}
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-gray-700">每页显示:</span>
                      <select
                        value={allOrdersPageSize}
                        onChange={(e) => {
                          const newPageSize = parseInt(e.target.value);
                          setAllOrdersPageSize(newPageSize);
                          // 页面重置和数据重载由useEffect自动处理
                        }}
                        className="text-sm border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500"
                        disabled={allOrdersLoading}
                      >
                        <option value={5}>5条</option>
                        <option value={10}>10条</option>
                        <option value={20}>20条</option>
                        <option value={50}>50条</option>
                      </select>
                    </div>
                    
                    {/* 分页导航 - 只在有多页时显示 */}
                    {Math.ceil(allOrdersTotal / allOrdersPageSize) > 1 && (
                      <div className="flex items-center space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => loadAllOrders(1)}
                          disabled={allOrdersPage <= 1 || allOrdersLoading}
                        >
                          首页
                        </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => loadAllOrders(allOrdersPage - 1)}
                          disabled={allOrdersPage <= 1 || allOrdersLoading}
              >
                上一页
              </Button>
                        
                        {/* 页码显示 */}
                        <div className="flex items-center space-x-1">
                          {(() => {
                            const totalPages = Math.ceil(allOrdersTotal / allOrdersPageSize);
                            const pages = [];
                            const maxVisiblePages = 5;
                            
                            let startPage = Math.max(1, allOrdersPage - Math.floor(maxVisiblePages / 2));
                            let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
                            
                            if (endPage - startPage + 1 < maxVisiblePages) {
                              startPage = Math.max(1, endPage - maxVisiblePages + 1);
                            }
                            
                            for (let i = startPage; i <= endPage; i++) {
                              pages.push(
                                <button
                                  key={i}
                                  onClick={() => loadAllOrders(i)}
                                  disabled={allOrdersLoading}
                                  className={`px-3 py-1 text-sm border rounded ${
                                    i === allOrdersPage
                                      ? 'bg-blue-600 text-white border-blue-600'
                                      : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                                  }`}
                                >
                                  {i}
                                </button>
                              );
                            }
                            
                            return pages;
                          })()}
                        </div>
                        
              <Button
                variant="outline"
                size="sm"
                onClick={() => loadAllOrders(allOrdersPage + 1)}
                          disabled={allOrdersPage >= Math.ceil(allOrdersTotal / allOrdersPageSize) || allOrdersLoading}
              >
                下一页
              </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => loadAllOrders(Math.ceil(allOrdersTotal / allOrdersPageSize))}
                          disabled={allOrdersPage >= Math.ceil(allOrdersTotal / allOrdersPageSize) || allOrdersLoading}
                        >
                          末页
              </Button>
            </div>
                    )}
                  </div>
                </div>
              </>
            ) : (
              <div className="text-center py-8">
                <AlertCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">暂无订单数据</p>
          </div>
        )}
          </div>
      </Card>
    </div>
  );
  };

  const renderExceptionOrdersTab = () => {
    // 计算异常订单统计信息
    const checkFailedCount = exceptionOrders.filter(order => order.order_status === 'check_failed').length;
    const bookingFailedCount = exceptionOrders.filter(order => order.order_status === 'failed').length;
    const totalAmount = exceptionOrders.reduce((sum, order) => sum + (Number(order.amount) || 0), 0);

    return (
      <div className="space-y-4">
        {/* 异常订单统计卡片 */}
        {exceptionOrdersTotal > 0 && (
          <Card className="bg-white border border-red-200">
            <div className="p-4">
              <h3 className="text-sm font-medium text-red-900 mb-3">异常订单统计</h3>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="p-2 bg-red-100 rounded-lg mx-auto w-fit mb-1">
                    <AlertTriangle className="h-4 w-4 text-red-600" />
                  </div>
                  <p className="text-lg font-medium text-gray-900">{exceptionOrdersTotal}</p>
                  <p className="text-xs text-gray-500">异常订单总数</p>
                </div>
                <div className="text-center">
                  <div className="p-2 bg-amber-100 rounded-lg mx-auto w-fit mb-1">
                    <AlertCircle className="h-4 w-4 text-amber-600" />
                  </div>
                  <p className="text-lg font-medium text-amber-700">{checkFailedCount}</p>
                  <p className="text-xs text-gray-500">导入验证失败数</p>
                </div>
                <div className="text-center">
                  <div className="p-2 bg-red-100 rounded-lg mx-auto w-fit mb-1">
                    <X className="h-4 w-4 text-red-600" />
                  </div>
                  <p className="text-lg font-medium text-red-700">{bookingFailedCount}</p>
                  <p className="text-xs text-gray-500">预定失败订单数</p>
                </div>
                <div className="text-center">
                  <div className="p-2 bg-green-100 rounded-lg mx-auto w-fit mb-1">
                    <DollarSign className="h-4 w-4 text-green-600" />
                  </div>
                  <p className="text-lg font-medium text-green-700">{formatAmount(totalAmount)}</p>
                  <p className="text-xs text-gray-500">涉及金额</p>
                </div>
              </div>
            </div>
          </Card>
        )}

        <Card className="bg-white border border-gray-200 rounded-lg shadow-sm">
          <div className="border-b border-gray-200 p-4">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-2">
                <X className="h-5 w-5 text-red-600" />
                <h3 className="text-lg font-medium text-gray-900">异常订单</h3>
                {exceptionOrdersTotal > 0 && (
                  <span className="ml-2 bg-red-100 text-red-800 text-xs font-medium px-2 py-1 rounded-full">
                    {exceptionOrdersTotal}
                  </span>
                )}
              </div>
            </div>
            
            {/* 搜索区域 */}
            <div className="flex flex-col gap-3">
                             {/* 第一行：状态筛选 */}
               <div className="flex items-center gap-3">
                 <span className="text-sm font-medium text-gray-700 whitespace-nowrap">异常类型：</span>
                 <div className="flex flex-wrap gap-2">
                   {[
                     { value: 'check_failed', label: '验证失败', color: 'bg-amber-100 text-amber-700' },
                     { value: 'failed', label: '预定失败', color: 'bg-red-100 text-red-700' },
                   ].map((status) => (
                     <label
                       key={status.value}
                       className="flex items-center gap-1 cursor-pointer hover:bg-gray-50 p-1 rounded"
                     >
                       <input
                         type="checkbox"
                         checked={exceptionSelectedStatuses.includes(status.value)}
                         onChange={(e) => {
                           if (e.target.checked) {
                             setExceptionSelectedStatuses([...exceptionSelectedStatuses, status.value]);
                           } else {
                             setExceptionSelectedStatuses(exceptionSelectedStatuses.filter(s => s !== status.value));
                           }
                         }}
                         className="w-4 h-4 text-red-600 bg-gray-100 border-gray-300 rounded focus:ring-red-500"
                       />
                       <span className={`px-2 py-1 rounded-full text-xs ${status.color}`}>
                         {status.label}
                       </span>
                     </label>
                   ))}
                 </div>
               </div>
              
              {/* 第二行：搜索输入框 */}
              <div className="flex flex-col md:flex-row md:items-center gap-3 pt-2 border-t border-gray-100">
                <div className="flex items-center gap-2 flex-1 max-w-xs">
                  <label className="text-sm text-gray-600 whitespace-nowrap min-w-fit">入住人姓名：</label>
            <input
              type="text"
                    placeholder="请输入入住人姓名"
              value={searchGuestName}
              onChange={(e) => setSearchGuestName(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && handleExceptionSearch()}
                    className="flex-1 h-9 px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent text-sm"
            />
          </div>
                
                <div className="flex items-center gap-2 flex-1 max-w-xs">
                  <label className="text-sm text-gray-600 whitespace-nowrap min-w-fit">手机号码：</label>
            <input
              type="text"
                    placeholder="请输入手机号码"
              value={searchMobilePhone}
              onChange={(e) => setSearchMobilePhone(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && handleExceptionSearch()}
                    className="flex-1 h-9 px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent text-sm"
            />
          </div>
                
                <div className="flex items-center gap-2 flex-1 max-w-xs">
                  <label className="text-sm text-gray-600 whitespace-nowrap min-w-fit">联系人手机：</label>
                  <input
                    type="text"
                    placeholder="请输入联系人手机号"
                    value={searchContactPhone}
                    onChange={(e) => setSearchContactPhone(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && handleExceptionSearch()}
                    className="flex-1 h-9 px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent text-sm"
                  />
                </div>

                <div className="flex items-center gap-2 ml-auto">
          <Button
                    size="sm"
                    onClick={handleExceptionSearch}
                    disabled={exceptionOrdersLoading}
                    className="bg-red-600 hover:bg-red-700 text-white h-9"
                  >
                    <Search className="h-4 w-4 mr-2" />
            搜索
          </Button>

          <Button
            variant="outline"
            size="sm"
                    onClick={handleClearExceptionSearch}
            disabled={exceptionOrdersLoading}
                    className="h-9"
          >
                    <X className="h-4 w-4 mr-2" />
                    重置
          </Button>

                  {exceptionOrdersTotal > 0 && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={exportExceptionOrders}
                      disabled={exceptionOrdersLoading}
                      className="h-9"
                      title="导出异常订单数据"
                    >
                      <Download className="h-4 w-4 mr-2" />
                      导出
                    </Button>
                  )}
          </div>
          </div>
                      </div>
                      </div>
          <div className="p-0">
            {exceptionOrdersLoading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-600 mx-auto mb-4"></div>
                <p className="text-gray-600">加载中...</p>
                    </div>
            ) : exceptionOrders.length > 0 ? (
              <>
                <div className="overflow-x-auto custom-scrollbar" style={{ 
                  scrollbarWidth: 'auto', 
                  scrollbarColor: '#cbd5e1 #f1f5f9'
                }}>
                  <table className="w-full text-xs" style={{ minWidth: '2600px' }}>
                    <thead className="bg-red-50 border-b border-red-200">
                      <tr>
                        <th className="text-center p-2 font-medium text-red-900 text-xs whitespace-nowrap" style={{ minWidth: '60px' }}>序号</th>
                        <th className="text-left p-2 font-medium text-red-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>状态</th>
                        <th className="text-left p-2 font-medium text-red-900 text-xs whitespace-nowrap" style={{ minWidth: '120px' }}>失败原因</th>
                        <th className="text-left p-2 font-medium text-red-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>入住人姓名</th>
                        <th className="text-left p-2 font-medium text-red-900 text-xs whitespace-nowrap" style={{ minWidth: '150px' }}>证件号码</th>
                        <th className="text-left p-2 font-medium text-red-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>入住时间</th>
                        <th className="text-left p-2 font-medium text-red-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>离店时间</th>
                        <th className="text-left p-2 font-medium text-red-900 text-xs whitespace-nowrap" style={{ minWidth: '150px' }}>酒店名称</th>
                        <th className="text-left p-2 font-medium text-red-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>房型</th>
                        <th className="text-left p-2 font-medium text-red-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>金额</th>
                        <th className="text-left p-2 font-medium text-red-900 text-xs whitespace-nowrap" style={{ minWidth: '120px' }}>手机号</th>
                        <th className="text-left p-2 font-medium text-red-900 text-xs whitespace-nowrap" style={{ minWidth: '120px' }}>联系人</th>
                        <th className="text-left p-2 font-medium text-red-900 text-xs whitespace-nowrap sticky right-0 bg-red-50 border-l border-red-200" style={{ minWidth: '100px' }}>操作</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200">
                      {exceptionOrders.map((order, index) => (
                        <tr key={order.id} className="hover:bg-gray-50">
                          <td className="p-2 text-xs text-gray-900 whitespace-nowrap text-center">{(exceptionOrdersPage - 1) * exceptionOrdersPageSize + index + 1}</td>
                          <td className="p-2 text-xs whitespace-nowrap">
                            {getOrderStatusDisplay(order.order_status)}
                          </td>
                          <td className="p-2 text-xs text-gray-600 whitespace-nowrap relative group cursor-help">
                            {order.fail_reason ? (
                              <>
                                <span className={`${order.fail_reason.length > 15 ? 'border-b border-dotted border-red-400' : ''}`}>
                                  {order.fail_reason.length > 15 ? order.fail_reason.substring(0, 15) + '...' : order.fail_reason}
                                </span>
                                {order.fail_reason.length > 15 && (
                                  <div className="invisible group-hover:visible absolute bottom-full mb-1 left-0 bg-gray-800 text-white text-xs rounded px-2 py-1 whitespace-normal max-w-xs z-50">
                                    {order.fail_reason}
                                  </div>
                                )}
                              </>
                            ) : (
                              '-'
                            )}
                          </td>
                          <td className="p-2 text-xs text-gray-900 whitespace-nowrap font-medium">{order.guest_full_name}</td>
                          <td className="p-2 text-xs text-gray-600 whitespace-nowrap font-mono">{order.guest_id_number || '-'}</td>
                          <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.check_in_time || '-'}</td>
                          <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.check_out_time || '-'}</td>
                          <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.hotel_name || '-'}</td>
                          <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.room_type || '-'}</td>
                          <td className="p-2 text-xs text-gray-900 whitespace-nowrap font-medium">{formatAmount(order.amount)}</td>
                          <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.guest_mobile_phone || '-'}</td>
                          <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.contact_person || '-'}</td>
                          <td className="p-2 text-xs whitespace-nowrap sticky right-0 bg-white border-l border-gray-200 z-20">
                            <div className="flex items-center gap-1">
                      <Button
                        variant="ghost"
                                size="icon"
                                title="查看详情"
                                className="h-6 w-6 p-0"
                                onClick={() => handleViewOrder(order)}
                              >
                                <Eye className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                                size="icon"
                                title="编辑订单"
                                className="h-6 w-6 p-0 text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                                onClick={() => handleEditOrder(order)}
                              >
                                <Edit className="h-4 w-4" />
                      </Button>
                              {/* 只有验证失败和待提交的订单可以删除 */}
                              {(order.order_status === 'check_failed' || order.order_status === 'initial') && (
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  title="删除订单"
                                  className="h-6 w-6 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                                  onClick={() => handleDeleteOrder(order)}
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              )}
                    </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                  </div>

        {/* 分页 */}
                {Math.ceil(exceptionOrdersTotal / exceptionOrdersPageSize) > 1 && (
                  <div className="flex items-center justify-between p-4 border-t border-gray-200">
                    <div className="text-sm text-gray-700">
                      显示第 {(exceptionOrdersPage - 1) * exceptionOrdersPageSize + 1} 到 {Math.min(exceptionOrdersPage * exceptionOrdersPageSize, exceptionOrdersTotal)} 条，共 {exceptionOrdersTotal} 条记录
            </div>
                    <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => loadExceptionOrders(exceptionOrdersPage - 1)}
                        disabled={exceptionOrdersPage <= 1 || exceptionOrdersLoading}
              >
                上一页
              </Button>
                      <span className="text-sm text-gray-700">
                        第 {exceptionOrdersPage} 页，共 {Math.ceil(exceptionOrdersTotal / exceptionOrdersPageSize)} 页
                      </span>
              <Button
                variant="outline"
                size="sm"
                onClick={() => loadExceptionOrders(exceptionOrdersPage + 1)}
                        disabled={exceptionOrdersPage >= Math.ceil(exceptionOrdersTotal / exceptionOrdersPageSize) || exceptionOrdersLoading}
              >
                下一页
              </Button>
            </div>
          </div>
        )}
              </>
            ) : (
              <div className="text-center py-8">
                <CheckCircle className="h-12 w-12 text-green-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">暂无异常订单</h3>
                <p className="text-gray-500">该项目没有失败的订单，所有订单处理正常</p>
              </div>
            )}
            </div>
      </Card>
    </div>
  );
  };

  const renderDetailsTab = () => {
    return (
      <>
        {/* 任务概览标题 */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-2">
            <FileText className="h-5 w-5 text-gray-600" />
            <h3 className="text-lg font-semibold text-gray-900">任务概览</h3>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={loadBookingTasks}
            disabled={bookingTasksLoading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${bookingTasksLoading ? 'animate-spin' : ''}`} />
            刷新
          </Button>
        </div>

        {/* 任务卡片网格 */}
        {bookingTasksLoading ? (
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">加载任务数据中...</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {/* 显示project_tasks表中的所有任务 */}
            {bookingTasks.length > 0 ? (
              bookingTasks.map(renderProjectTaskCard)
            ) : (
              <Card className="bg-white border border-gray-200 p-8 text-center col-span-full">
                <div className="p-4 bg-blue-50 rounded-full mx-auto w-fit mb-4">
                  <Building className="h-8 w-8 text-blue-600" />
                    </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">暂无酒店预订任务</h3>
                <p className="text-gray-500 mb-4">该项目还没有创建酒店预订任务</p>
                <p className="text-sm text-gray-400">请先在"待预订"tab中上传酒店订单并创建预订任务</p>
              </Card>
        )}
    </div>
        )}
      </>
  );
  };

  if (loading) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center min-h-screen">
          <Loader className="w-8 h-8 animate-spin" />
        </div>
      </MainLayout>
    );
  }

  if (error) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
            <h2 className="text-xl font-semibold mb-2">加载失败</h2>
            <p className="text-gray-600 mb-4">{error}</p>
            <Button onClick={() => window.location.reload()}>
              <RefreshCw className="w-4 h-4 mr-2" />
              重新加载
            </Button>
          </div>
        </div>
      </MainLayout>
    );
  }

  return (
    <>
    <MainLayout>
      <div className="p-4 bg-gray-50 min-h-screen">
        {/* 页面头部 - 完全参考火车票预订页面 */}
        <div className="mb-4">
          {/* 项目和任务类型信息卡片 */}
          <Card className="bg-white shadow-sm border border-gray-200 border-l-4 border-l-blue-500 p-4">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="flex items-center gap-3 mb-2">
                  <div className="p-2 bg-blue-50 rounded-lg">
                    <Building className="h-5 w-5 text-blue-600" />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <h1 className="text-xl font-bold text-gray-900">
                        {project?.project_name || 'testing'} - 酒店预订
                      </h1>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => navigate(`/project-detail/${projectId}`)}
                        className="flex items-center gap-2"
                      >
                        <ArrowLeft className="h-4 w-4" />
                        返回
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </Card>
        </div>

        {/* Tab导航 */}
        <div className="mb-6">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8" aria-label="Tabs">
              <button
                onClick={() => handleTabChange('booking')}
                className={`py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap transition-colors duration-200 ${
                  activeTab === 'booking'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center gap-2">
                  <Clock2Icon className="h-4 w-4" />
                  待预订
                </div>
              </button>
              <button
                onClick={() => handleTabChange('all-orders')}
                className={`py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap transition-colors duration-200 ${
                  activeTab === 'all-orders'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center gap-2">
                  <Eye className="h-4 w-4" />
                  所有订单
                  {allOrdersTotal > 0 && (
                    <span className="ml-1 bg-blue-100 text-blue-800 text-xs font-medium px-2 py-0.5 rounded-full">
                      {allOrdersTotal}
                    </span>
                  )}
                </div>
              </button>
              <button
                onClick={() => handleTabChange('exception-orders')}
                className={`py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap transition-colors duration-200 ${
                  activeTab === 'exception-orders'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center gap-2">
                  <AlertTriangle className="h-4 w-4" />
                  异常订单
                  {exceptionOrdersTotal > 0 && (
                    <span className="ml-1 bg-red-100 text-red-800 text-xs font-medium px-2 py-0.5 rounded-full">
                      {exceptionOrdersTotal}
                    </span>
                  )}
                </div>
              </button>
              <button
                onClick={() => handleTabChange('details')}
                className={`py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap transition-colors duration-200 ${
                  activeTab === 'details'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center gap-2">
                  <Settings className="h-4 w-4" />
                  预订任务
                </div>
              </button>
            </nav>
          </div>
        </div>

        {/* Tab内容区域 */}
        <div className="min-h-96">
          {renderTabContent()}
        </div>
      </div>
    </MainLayout>

      {/* 酒店订单详情模态框 - 复用booking标签页的详情功能 */}
      <HotelOrderDetailModal 
        order={viewingOrder}
        isOpen={!!viewingOrder}
        initialEditMode={!!editingOrder}
        onClose={() => {
          setViewingOrder(null);
          setEditingOrder(null);
        }}
        onOrderUpdated={() => {
          // 订单更新后重新加载数据
          if (activeTab === 'all-orders') {
            loadAllOrders(allOrdersPage);
            loadProjectStats();
          } else if (activeTab === 'exception-orders') {
            loadExceptionOrders(exceptionOrdersPage);
          }
        }}
      />



      {/* 删除确认对话框 */}
      {showDeleteConfirm && orderToDelete && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex min-h-screen items-end justify-center px-4 pt-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
            
            <span className="hidden sm:inline-block sm:h-screen sm:align-middle">&#8203;</span>
            
            <div className="relative inline-block transform overflow-hidden rounded-lg bg-white px-4 pt-5 pb-4 text-left align-bottom shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6 sm:align-middle">
              <div className="sm:flex sm:items-start">
                <div className="mx-auto flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                  <AlertTriangle className="h-6 w-6 text-red-600" />
                </div>
                <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                  <h3 className="text-lg font-medium leading-6 text-gray-900">
                    删除订单
                  </h3>
                  <div className="mt-2">
                    <p className="text-sm text-gray-500">
                      确定要删除订单 "{orderToDelete.guest_full_name}" 吗？此操作不可撤销。
                    </p>
                  </div>
                </div>
              </div>
              <div className="mt-5 sm:mt-4 sm:flex sm:flex-row-reverse">
                <Button
                  onClick={confirmDeleteOrder}
                  className="w-full bg-red-600 hover:bg-red-700 sm:ml-3 sm:w-auto"
                >
                  删除
                </Button>
                <Button
                  variant="outline"
                  onClick={() => {
                    setShowDeleteConfirm(false);
                    setOrderToDelete(null);
                  }}
                  className="mt-3 w-full sm:mt-0 sm:w-auto"
                >
                  取消
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default HotelBookingPage; 