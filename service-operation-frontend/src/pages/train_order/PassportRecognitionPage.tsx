import React, { useState, useEffect } from 'react';
import { Upload, FileImage, CheckCircle, XCircle, Clock, AlertCircle, History, FolderOpen, Calendar, BarChart3, ChevronDown, ChevronUp, Download, RefreshCw, Copy, Check, StopCircle, Trash2 } from 'lucide-react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { PassportImage } from '@/components/PassportImage';
import { Pagination } from '@/components/ui/pagination';
import MainLayout from '@/components/layout/MainLayout';
import * as XLSX from 'xlsx-js-style';
import { saveAs } from 'file-saver';
import { Button } from '@/components/ui/button';
import { API_BASE_URL } from '@/utils/constants';

interface PassportRecord {
  id: number;
  task_id: string;
  uploaded_image_url: string;
  // 按指定顺序的字段
  certificate_type: string;
  certificate_number: string;
  surname: string;
  given_names: string;
  sex: string;
  date_of_birth: string;
  nationality: string;
  passenger_type: string;
  country_of_issue: string;
  date_of_issue: string;
  date_of_expiry: string;
  ssr_code: string;
  mrz_line1: string;
  mrz_line2: string;
  viz_mrz_consistency: string;
  // 保留的原有字段
  passport_number: string; // 兼容性保留
  place_of_birth: string;
  authority: string;
  processing_status: string;
  created_at: string;
  updated_at: string;
}

interface TaskSummary {
  task_id: string;
  total_files: number;
  completed_files: number;
  processing_files: number;
  failed_files: number;
  pending_files: number;
  created_at: string;
  updated_at: string;
  latest_upload_url: string;
}

interface APIResponse {
  total: number;
  items: PassportRecord[];
  page: number;
  size: number;
}

interface TaskListResponse {
  total: number;
  items: TaskSummary[];
  page: number;
  size: number;
}

const PassportRecognitionPage: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [activeTab, setActiveTab] = useState<'current' | 'history'>('current');
  
  // 当前任务状态
  const [currentTaskId, setCurrentTaskId] = useState<string>('');
  const [currentPassports, setCurrentPassports] = useState<PassportRecord[]>([]);
  const [uploadLoading, setUploadLoading] = useState(false);
  const [uploadError, setUploadError] = useState<string>('');
  const [taskStatusPolling, setTaskStatusPolling] = useState<boolean>(false);
  const [recognitionStarted, setRecognitionStarted] = useState<boolean>(false);
  const [progressCollapsed, setProgressCollapsed] = useState<boolean>(false);
  
  // 历史任务状态
  const [historyTasks, setHistoryTasks] = useState<TaskSummary[]>([]);
  const [historyLoading, setHistoryLoading] = useState(false);
  const [historyError, setHistoryError] = useState<string>('');
  const [historyCurrentPage, setHistoryCurrentPage] = useState(1);
  const [historyTotalItems, setHistoryTotalItems] = useState(0);
  const [historyPageSize] = useState(10);
  const [copiedRowId, setCopiedRowId] = useState<number | null>(null);

  // 检查URL参数，决定初始显示的标签页
  useEffect(() => {
    const tab = searchParams.get('tab');
    if (tab === 'history') {
      setActiveTab('history');
    }
  }, [searchParams]);

  // 任务状态轮询
  useEffect(() => {
    let intervalId: number;
    
    if (currentTaskId && taskStatusPolling) {
      intervalId = setInterval(async () => {
        await loadCurrentTaskPassports(currentTaskId);
        
        // 检查是否所有任务都已完成
        const allCompleted = currentPassports.every(p => 
          p.processing_status === 'completed' || p.processing_status === 'failed'
        );
        
        if (allCompleted && currentPassports.length > 0) {
          setTaskStatusPolling(false);
          // 识别完成后自动折叠进度面板
          setTimeout(() => {
            setProgressCollapsed(true);
          }, 1000);
        }
      }, 2000); // 每2秒轮询一次
    }
    
    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [currentTaskId, taskStatusPolling, currentPassports]);

  useEffect(() => {
    if (activeTab === 'history') {
      loadHistoryTasks();
    }
  }, [activeTab, historyCurrentPage]);

  // 加载历史任务列表
  const loadHistoryTasks = async () => {
    try {
      setHistoryLoading(true);
      setHistoryError('');

      const token = localStorage.getItem('access_token');
      if (!token) {
        setHistoryError('请先登录');
        return;
      }

      const response = await fetch(`${API_BASE_URL}/passport/tasks?page=${historyCurrentPage}&size=${historyPageSize}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.text();
        console.error('获取任务列表失败，状态码:', response.status, '错误信息:', errorData);
        throw new Error(`获取任务列表失败 (${response.status}): ${errorData || '未知错误'}`);
      }

      const data: TaskListResponse = await response.json();
      console.log('历史任务数据:', data);
      setHistoryTasks(data.items || []);
      setHistoryTotalItems(data.total || 0);
    } catch (error) {
      console.error('加载历史任务失败:', error);
      setHistoryError(error instanceof Error ? error.message : '加载失败');
    } finally {
      setHistoryLoading(false);
    }
  };

  // 处理文件上传到服务器
  const uploadFiles = async (files: FileList) => {
    try {
      setUploadLoading(true);
      setUploadError('');

      const token = localStorage.getItem('access_token');
      if (!token) {
        setUploadError('请先登录');
        return;
      }

      // 创建FormData并添加所有文件
      const formData = new FormData();
      Array.from(files).forEach(file => {
        formData.append('files', file);
      });

      const response = await fetch(`${API_BASE_URL}/passport/upload`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.text();
        console.error('上传文件失败，状态码:', response.status, '错误信息:', errorData);
        throw new Error(`上传文件失败 (${response.status}): ${errorData || '未知错误'}`);
      }

      const result = await response.json();
      console.log('上传结果:', result);

      // 获取任务ID作为当前任务
      if (result.task_id) {
        setCurrentTaskId(result.task_id);
        setRecognitionStarted(true);
        setTaskStatusPolling(true);
        setProgressCollapsed(false); // 重置折叠状态
        
        // 立即加载一次当前任务的护照记录
        await loadCurrentTaskPassports(result.task_id);
      }
      
    } catch (error) {
      console.error('上传失败:', error);
      setUploadError(error instanceof Error ? error.message : '上传失败');
    } finally {
      setUploadLoading(false);
    }
  };

  // 处理文件选择
  const handleUpload = () => {
    // 创建隐藏的文件输入框
    const fileInput = document.createElement('input');
    fileInput.type = 'file';
    fileInput.accept = 'image/*';
    fileInput.multiple = true;
    fileInput.style.display = 'none';
    
    // 监听文件选择
    fileInput.onchange = async (e) => {
      const target = e.target as HTMLInputElement;
      if (target.files && target.files.length > 0) {
        await uploadFiles(target.files);
      }
    };
    
    // 触发文件选择
    document.body.appendChild(fileInput);
    fileInput.click();
    document.body.removeChild(fileInput);
  };

  // 加载当前任务的护照记录
  const loadCurrentTaskPassports = async (taskId: string) => {
    try {
      const token = localStorage.getItem('access_token');
      if (!token) return;

      const response = await fetch(`${API_BASE_URL}/passport/task/${taskId}?page=1&size=100`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('获取任务护照记录失败');
      }

      const data: APIResponse = await response.json();
      console.log('当前任务护照记录:', data);
      setCurrentPassports(data.items || []);
    } catch (error) {
      console.error('加载当前任务护照记录失败:', error);
    }
  };

  // 刷新当前任务数据
  const refreshCurrentTask = async () => {
    if (!currentTaskId) return;
    
    try {
      // 显示加载状态
      const loadingToast = document.createElement('div');
      loadingToast.className = 'fixed top-4 right-4 bg-blue-500 text-white px-4 py-2 rounded-lg shadow-lg z-50 flex items-center gap-2';
      loadingToast.innerHTML = `
        <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
        <span>正在刷新数据...</span>
      `;
      document.body.appendChild(loadingToast);

      await loadCurrentTaskPassports(currentTaskId);
      
      // 移除加载提示
      document.body.removeChild(loadingToast);
      
      // 显示成功提示
      const successToast = document.createElement('div');
      successToast.className = 'fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg z-50 flex items-center gap-2';
      successToast.innerHTML = `
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
        </svg>
        <span>数据已刷新</span>
      `;
      document.body.appendChild(successToast);
      
      setTimeout(() => {
        successToast.style.transform = 'translateX(100%)';
        successToast.style.opacity = '0';
        setTimeout(() => document.body.removeChild(successToast), 300);
      }, 2000);
      
    } catch (error) {
      // 移除可能存在的加载提示
      const loadingToast = document.querySelector('.fixed.top-4.right-4.bg-blue-500');
      if (loadingToast) {
        document.body.removeChild(loadingToast);
      }
      
      console.error('刷新数据失败:', error);
      
      // 显示错误提示
      const errorToast = document.createElement('div');
      errorToast.className = 'fixed top-4 right-4 bg-red-500 text-white px-4 py-2 rounded-lg shadow-lg z-50 flex items-center gap-2';
      errorToast.innerHTML = `
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
        <span>刷新失败，请重试</span>
      `;
      document.body.appendChild(errorToast);
      
      setTimeout(() => {
        errorToast.style.transform = 'translateX(100%)';
        errorToast.style.opacity = '0';
        setTimeout(() => document.body.removeChild(errorToast), 300);
      }, 2000);
    }
  };

  // 复制护照信息到剪切板
  const copyPassportInfo = async (passport: PassportRecord) => {
    try {
      const formatValue = (value: string | undefined) => value || '-';
      
      const formattedText = [
        `证件类型：${formatValue(passport.certificate_type)}`,
        `证件号码：${formatValue(passport.certificate_number || passport.passport_number)}`,
        `姓氏：${formatValue(passport.surname)}`,
        `名字：${formatValue(passport.given_names)}`,
        `性别：${formatSex(passport.sex)}`,
        `出生日期：${formatPassportDate(passport.date_of_birth)}`,
        `国籍：${formatValue(passport.nationality)}`,
        `旅客类型：${formatValue(passport.passenger_type)}`,
        `签发国：${formatValue(passport.country_of_issue)}`,
        `签发日期：${formatPassportDate(passport.date_of_issue)}`,
        `有效期至：${formatPassportDate(passport.date_of_expiry)}`,
        `SSR DOCS码：${formatValue(passport.ssr_code)}`,
        `MRZ第一行：${formatValue(passport.mrz_line1)}`,
        `MRZ第二行：${formatValue(passport.mrz_line2)}`,
        `一致性检查：${formatValue(passport.viz_mrz_consistency)}`,
        `处理状态：${getStatusText(passport.processing_status)}`,
        `任务ID：${passport.task_id}`,
        `创建时间：${formatDate(passport.created_at)}`
      ].join('\n');

      await navigator.clipboard.writeText(formattedText);
      setCopiedRowId(passport.id);
      
      // 显示成功提示
      const toast = document.createElement('div');
      toast.className = 'fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg z-50 flex items-center gap-2';
      toast.innerHTML = `
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
        </svg>
        <span>护照信息已复制到剪贴板</span>
      `;
      document.body.appendChild(toast);
      
      setTimeout(() => {
        toast.style.transform = 'translateX(100%)';
        toast.style.opacity = '0';
        setTimeout(() => document.body.removeChild(toast), 300);
      }, 2000);
      
      // 2秒后重置复制状态
      setTimeout(() => setCopiedRowId(null), 2000);
      
    } catch (error) {
      console.error('复制失败:', error);
      
      // 显示错误提示
      const toast = document.createElement('div');
      toast.className = 'fixed top-4 right-4 bg-red-500 text-white px-4 py-2 rounded-lg shadow-lg z-50 flex items-center gap-2';
      toast.innerHTML = `
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
        <span>复制失败，请重试</span>
      `;
      document.body.appendChild(toast);
      
      setTimeout(() => {
        toast.style.transform = 'translateX(100%)';
        toast.style.opacity = '0';
        setTimeout(() => document.body.removeChild(toast), 300);
      }, 3000);
    }
  };

  // 复制单个字段值到剪切板
  const copyFieldValue = async (value: string | undefined, fieldName: string) => {
    if (!value || value === '-') {
      return; // 不复制空值或"-"
    }

    try {
      await navigator.clipboard.writeText(value);
      
      // 显示成功提示
      const toast = document.createElement('div');
      toast.className = 'fixed top-4 right-4 bg-blue-500 text-white px-4 py-2 rounded-lg shadow-lg z-50 flex items-center gap-2';
      toast.innerHTML = `
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
        </svg>
        <span>${fieldName}已复制：${value}</span>
      `;
      document.body.appendChild(toast);
      
      setTimeout(() => {
        toast.style.transform = 'translateX(100%)';
        toast.style.opacity = '0';
        setTimeout(() => document.body.removeChild(toast), 300);
      }, 1500);
      
    } catch (error) {
      console.error('复制字段值失败:', error);
    }
  };

  // 格式化日期
  const formatDate = (dateString: string) => {
    if (!dateString) return '-';
    try {
      return new Date(dateString).toLocaleDateString('zh-CN');
    } catch {
      return dateString;
    }
  };

  // 格式化护照日期字段（YYYY-MM-DD格式）
  const formatPassportDate = (dateString: string) => {
    if (!dateString) return '-';
    try {
      const date = new Date(dateString);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    } catch {
      return dateString;
    }
  };

  // 格式化性别字段
  const formatSex = (sex: string) => {
    if (!sex) return '-';
    const sexMap: Record<string, string> = {
      'M': '男',
      'F': '女',
      'Male': '男',
      'Female': '女',
      '男': '男',
      '女': '女'
    };
    return sexMap[sex] || sex;
  };

  // 格式化一致性检查字段
  const formatConsistency = (consistency: string) => {
    if (!consistency) return { text: '-', color: 'text-gray-500' };
    
    const lowerConsistency = consistency.toLowerCase();
    // 只有完全一致才显示绿色
    if (lowerConsistency === '一致' || lowerConsistency === 'consistent' || lowerConsistency === 'ok') {
      return { text: consistency, color: 'text-green-600 bg-green-50' };
    } else {
      // 其他所有情况都显示红色（包括部分一致、不一致等）
      return { text: consistency, color: 'text-red-600 bg-red-50' };
    }
  };

  // 状态相关函数
  const getStatusText = (status: string) => {
    const statusMap: Record<string, string> = {
      'pending': '待处理',
      'processing': '处理中',
      'completed': '已完成',
      'failed': '处理失败'
    };
    return statusMap[status] || status;
  };

  const getStatusColor = (status: string) => {
    const colorMap: Record<string, string> = {
      'pending': 'text-yellow-600 bg-yellow-50',
      'processing': 'text-blue-600 bg-blue-50',
      'completed': 'text-green-600 bg-green-50',
      'failed': 'text-red-600 bg-red-50'
    };
    return colorMap[status] || 'text-gray-600 bg-gray-50';
  };

  // 导出Excel功能
  const exportToExcel = (data: PassportRecord[], filename: string) => {
    try {
      // 准备导出数据（按指定字段顺序）
      const exportData = data.map((passport, index) => ({
        '序号': index + 1,
        '任务ID': passport.task_id,
        '证件类型': passport.certificate_type || '-',
        '证件号码': passport.certificate_number || passport.passport_number || '-',
        '姓氏': passport.surname || '-',
        '名字': passport.given_names || '-',
        '性别': formatSex(passport.sex),
        '出生日期': formatPassportDate(passport.date_of_birth),
        '国籍': passport.nationality || '-',
        '旅客类型': passport.passenger_type || '-',
        '签发国': passport.country_of_issue || '-',
        '签发日期': formatPassportDate(passport.date_of_issue),
        '有效期至': formatPassportDate(passport.date_of_expiry),
        'SSR码': passport.ssr_code || '-',
        'MRZ第一行': passport.mrz_line1 || '-',
        'MRZ第二行': passport.mrz_line2 || '-',
        '一致性检查': passport.viz_mrz_consistency || '-',
        '处理状态': getStatusText(passport.processing_status),
        '创建时间': formatDate(passport.created_at),
        '更新时间': formatDate(passport.updated_at)
      }));

      // 创建工作簿
      const ws = XLSX.utils.json_to_sheet(exportData);
      const wb = XLSX.utils.book_new();
      
      // 设置列宽
      const colWidths = [
        { wch: 6 },   // 序号
        { wch: 20 },  // 任务ID
        { wch: 10 },  // 证件类型
        { wch: 15 },  // 证件号码
        { wch: 12 },  // 姓氏
        { wch: 12 },  // 名字
        { wch: 6 },   // 性别
        { wch: 12 },  // 出生日期
        { wch: 10 },  // 国籍
        { wch: 10 },  // 旅客类型
        { wch: 10 },  // 签发国
        { wch: 12 },  // 签发日期
        { wch: 12 },  // 有效期至
        { wch: 25 },  // SSR码
        { wch: 30 },  // MRZ第一行
        { wch: 30 },  // MRZ第二行
        { wch: 15 },  // 一致性检查
        { wch: 10 },  // 处理状态
        { wch: 18 },  // 创建时间
        { wch: 18 }   // 更新时间
      ];
      ws['!cols'] = colWidths;

      // 获取工作表范围
      const range = XLSX.utils.decode_range(ws['!ref'] || 'A1');
      
      // 定义完整的边框样式
      const borderAll = {
        style: 'thin',
        color: { rgb: '000000' }
      };
      
      // 为所有单元格添加边框和样式
      for (let row = range.s.r; row <= range.e.r; row++) {
        for (let col = range.s.c; col <= range.e.c; col++) {
          const cellAddress = XLSX.utils.encode_cell({ r: row, c: col });
          if (!ws[cellAddress]) ws[cellAddress] = { t: 's', v: '' };
          
          // 设置单元格样式
          ws[cellAddress].s = {
            border: {
              top: borderAll,
              bottom: borderAll,
              left: borderAll,
              right: borderAll
            },
            alignment: { 
              horizontal: 'left', 
              vertical: 'center',
              wrapText: true 
            },
            font: { 
              name: '微软雅黑', 
              sz: 10 
            }
          };
          
          // 为标题行添加特殊样式
          if (row === 0) {
            ws[cellAddress].s.fill = {
              patternType: 'solid',
              fgColor: { rgb: 'F3F4F6' }
            };
            ws[cellAddress].s.font = {
              name: '微软雅黑',
              sz: 10,
              bold: true
            };
            ws[cellAddress].s.alignment = {
              horizontal: 'center',
              vertical: 'center',
              wrapText: true
            };
          }
        }
      }

      // 添加工作表
      XLSX.utils.book_append_sheet(wb, ws, '护照识别结果');
      
      // 生成Excel文件
      const excelBuffer = XLSX.write(wb, { 
        bookType: 'xlsx', 
        type: 'array',
        cellStyles: true
      });
      const blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      
      // 下载文件
      saveAs(blob, filename);
      
      // 显示成功提示
      const toast = document.createElement('div');
      toast.className = 'fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg z-50 flex items-center gap-2';
      toast.innerHTML = `
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
        </svg>
        <span>Excel文件导出成功</span>
      `;
      document.body.appendChild(toast);
      
      setTimeout(() => {
        toast.style.transform = 'translateX(100%)';
        toast.style.opacity = '0';
        setTimeout(() => document.body.removeChild(toast), 300);
      }, 3000);
      
    } catch (error) {
      console.error('导出Excel失败:', error);
      
      // 显示错误提示
      const toast = document.createElement('div');
      toast.className = 'fixed top-4 right-4 bg-red-500 text-white px-4 py-2 rounded-lg shadow-lg z-50 flex items-center gap-2';
      toast.innerHTML = `
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
        <span>导出失败，请重试</span>
      `;
      document.body.appendChild(toast);
      
      setTimeout(() => {
        toast.style.transform = 'translateX(100%)';
        toast.style.opacity = '0';
        setTimeout(() => document.body.removeChild(toast), 300);
      }, 3000);
    }
  };

  // 导出当前任务数据
  const exportCurrentTask = () => {
    if (currentPassports.length === 0) {
      alert('暂无数据可导出');
      return;
    }
    
    const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
    const filename = `护照识别结果_${currentTaskId}_${timestamp}.xlsx`;
    exportToExcel(currentPassports, filename);
  };

  // 获取任务详细数据
  const getTaskPassports = async (taskId: string): Promise<PassportRecord[]> => {
    try {
      const token = localStorage.getItem('access_token');
      if (!token) {
        throw new Error('请先登录');
      }

      const response = await fetch(`${API_BASE_URL}/api/passport/task/${taskId}?page=1&size=100`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('获取任务数据失败');
      }

      const data: APIResponse = await response.json();
      return data.items || [];
    } catch (error) {
      console.error('获取任务数据失败:', error);
      throw error;
    }
  };

  // 导出历史任务数据
  const exportHistoryTask = async (taskId: string) => {
    try {
      // 显示加载状态
      const loadingToast = document.createElement('div');
      loadingToast.className = 'fixed top-4 right-4 bg-blue-500 text-white px-4 py-2 rounded-lg shadow-lg z-50 flex items-center gap-2';
      loadingToast.innerHTML = `
        <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
        <span>正在导出数据...</span>
      `;
      document.body.appendChild(loadingToast);

      const passports = await getTaskPassports(taskId);
      
      // 移除加载提示
      document.body.removeChild(loadingToast);
      
      if (passports.length === 0) {
        alert('该任务暂无数据可导出');
        return;
      }

      const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
      const filename = `护照识别结果_${taskId}_${timestamp}.xlsx`;
      exportToExcel(passports, filename);
      
    } catch (error) {
      // 移除可能存在的加载提示
      const loadingToast = document.querySelector('.fixed.top-4.right-4.bg-blue-500');
      if (loadingToast) {
        document.body.removeChild(loadingToast);
      }
      
      console.error('导出历史任务失败:', error);
      alert('导出失败，请重试');
    }
  };

  // 刷新历史任务列表
  const refreshHistoryTasks = async () => {
    await loadHistoryTasks();
  };

  // 刷新单个任务状态
  const refreshSingleTask = async () => {
    // 刷新单个任务的逻辑
    console.log('刷新单个任务');
  };

  // 停止任务
  const stopTask = async (taskId: string) => {
    if (!confirm('确定要停止这个任务吗？停止后正在处理的文件将中断。')) {
      return;
    }

    try {
      const token = localStorage.getItem('access_token');
      if (!token) {
        throw new Error('请先登录');
      }

      // 显示加载状态
      const loadingToast = document.createElement('div');
      loadingToast.className = 'fixed top-4 right-4 bg-orange-500 text-white px-4 py-2 rounded-lg shadow-lg z-50 flex items-center gap-2';
      loadingToast.innerHTML = `
        <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
        <span>正在停止任务...</span>
      `;
      document.body.appendChild(loadingToast);

      const response = await fetch(`${API_BASE_URL}/passport/task/${taskId}/stop`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.text();
        console.error('停止任务失败，状态码:', response.status, '错误信息:', errorData);
        throw new Error(`停止任务失败 (${response.status}): ${errorData || '未知错误'}`);
      }

      // 移除加载提示
      document.body.removeChild(loadingToast);

      // 显示成功提示
      const successToast = document.createElement('div');
      successToast.className = 'fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg z-50 flex items-center gap-2';
      successToast.innerHTML = `
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
        </svg>
        <span>任务已停止</span>
      `;
      document.body.appendChild(successToast);

      setTimeout(() => {
        successToast.style.transform = 'translateX(100%)';
        successToast.style.opacity = '0';
        setTimeout(() => document.body.removeChild(successToast), 300);
      }, 2000);

      // 如果是当前任务，刷新当前任务数据
      if (taskId === currentTaskId) {
        setTaskStatusPolling(false); // 立即停止轮询
        
        // 等待一段时间确保后端状态更新完成，然后刷新数据
        setTimeout(async () => {
          await loadCurrentTaskPassports(taskId);
        }, 1000); // 等待1秒钟
      }

      // 刷新历史任务列表
      if (activeTab === 'history') {
        setTimeout(async () => {
          await loadHistoryTasks();
        }, 1000); // 等待1秒钟
      }

    } catch (error) {
      // 移除可能存在的加载提示
      const loadingToast = document.querySelector('.fixed.top-4.right-4.bg-orange-500');
      if (loadingToast) {
        document.body.removeChild(loadingToast);
      }

      console.error('停止任务失败:', error);

      // 显示错误提示
      const errorToast = document.createElement('div');
      errorToast.className = 'fixed top-4 right-4 bg-red-500 text-white px-4 py-2 rounded-lg shadow-lg z-50 flex items-center gap-2';
      errorToast.innerHTML = `
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
        <span>${error instanceof Error ? error.message : '停止任务失败，请重试'}</span>
      `;
      document.body.appendChild(errorToast);

      setTimeout(() => {
        errorToast.style.transform = 'translateX(100%)';
        errorToast.style.opacity = '0';
        setTimeout(() => document.body.removeChild(errorToast), 300);
      }, 2000);
    }
  };

  // 删除任务
  const deleteTask = async (taskId: string) => {
    if (!confirm('确定要删除这个任务吗？删除后无法恢复，所有相关数据将被永久删除。')) {
      return;
    }

    try {
      const token = localStorage.getItem('access_token');
      if (!token) {
        throw new Error('请先登录');
      }

      // 显示加载状态
      const loadingToast = document.createElement('div');
      loadingToast.className = 'fixed top-4 right-4 bg-red-500 text-white px-4 py-2 rounded-lg shadow-lg z-50 flex items-center gap-2';
      loadingToast.innerHTML = `
        <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
        <span>正在删除任务...</span>
      `;
      document.body.appendChild(loadingToast);

      const response = await fetch(`${API_BASE_URL}/passport/task/${taskId}/delete`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.text();
        console.error('删除任务失败，状态码:', response.status, '错误信息:', errorData);
        throw new Error(`删除任务失败 (${response.status}): ${errorData || '未知错误'}`);
      }

      // 移除加载提示
      document.body.removeChild(loadingToast);

      // 显示成功提示
      const successToast = document.createElement('div');
      successToast.className = 'fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg z-50 flex items-center gap-2';
      successToast.innerHTML = `
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
        </svg>
        <span>任务已删除</span>
      `;
      document.body.appendChild(successToast);

      setTimeout(() => {
        successToast.style.transform = 'translateX(100%)';
        successToast.style.opacity = '0';
        setTimeout(() => document.body.removeChild(successToast), 300);
      }, 2000);

      // 如果删除的是当前任务，清空当前任务数据
      if (taskId === currentTaskId) {
        setCurrentTaskId('');
        setCurrentPassports([]);
        setRecognitionStarted(false);
        setTaskStatusPolling(false);
      }

      // 刷新历史任务列表
      if (activeTab === 'history') {
        await loadHistoryTasks();
      }

    } catch (error) {
      // 移除可能存在的加载提示
      const loadingToast = document.querySelector('.fixed.top-4.right-4.bg-red-500');
      if (loadingToast) {
        document.body.removeChild(loadingToast);
      }

      console.error('删除任务失败:', error);

      // 显示错误提示
      const errorToast = document.createElement('div');
      errorToast.className = 'fixed top-4 right-4 bg-red-500 text-white px-4 py-2 rounded-lg shadow-lg z-50 flex items-center gap-2';
      errorToast.innerHTML = `
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
        <span>${error instanceof Error ? error.message : '删除任务失败，请重试'}</span>
      `;
      document.body.appendChild(errorToast);

      setTimeout(() => {
        errorToast.style.transform = 'translateX(100%)';
        errorToast.style.opacity = '0';
        setTimeout(() => document.body.removeChild(errorToast), 300);
      }, 2000);
    }
  };

  // 检查是否可以停止任务
  const canStopTask = () => {
    return currentTaskId && currentPassports.length > 0;
  };

  const isTaskCompleted = (task: TaskSummary) => {
    return task.completed_files === task.total_files && task.total_files > 0;
  };

  const renderCurrentTask = () => (
    <div className="flex flex-col space-y-4">
      {/* 上传区域 */}
      <div className="flex-shrink-0 bg-gray-50 rounded-lg border border-gray-200 p-6">
        <div className="text-center">
          <div className="mb-4">
            <p className="text-gray-600 mb-4">选择一张或多张护照图片进行识别处理</p>
          </div>

          <input
            type="file"
            id="passport-upload"
            multiple
            accept="image/*"
            onChange={(e) => {
              const target = e.target as HTMLInputElement;
              if (target.files && target.files.length > 0) {
                uploadFiles(target.files);
              }
            }}
            className="hidden"
            disabled={uploadLoading}
          />
          
          <label
            htmlFor="passport-upload"
            className={`inline-flex items-center gap-3 px-6 py-3 rounded-lg text-white font-medium transition-all duration-200 ${
              uploadLoading
                ? 'bg-gray-400 cursor-not-allowed'
                : 'bg-blue-600 hover:bg-blue-700 cursor-pointer hover:shadow-lg transform hover:-translate-y-0.5'
            }`}
          >
            <FileImage className="h-5 w-5" />
            {uploadLoading ? '上传中...' : '选择图片文件'}
          </label>
          
          {/* <p className="text-sm text-gray-500 mt-3">支持 JPG、PNG、GIF 格式</p> */}
        </div>

        {/* 上传状态 */}
        {uploadError && (
          <div className="mt-4 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
            <div className="flex items-center">
              <XCircle className="h-5 w-5 mr-2" />
              <span className="font-medium">上传失败</span>
            </div>
            <p className="text-sm mt-1">{uploadError}</p>
          </div>
        )}

        {uploadLoading && (
          <div className="mt-4 bg-blue-50 border border-blue-200 text-blue-700 px-4 py-3 rounded-lg">
            <div className="flex items-center">
              <Clock className="h-5 w-5 mr-2 animate-spin" />
              <span className="font-medium">正在处理图片...</span>
            </div>
            <p className="text-sm mt-1">请稍等，图片正在上传和识别中</p>
          </div>
        )}
      </div>

      {/* 识别进度显示 */}
      {recognitionStarted && (
        <div className="flex-shrink-0 bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center justify-between mb-3">
            <h3 className="font-medium text-blue-900">识别进度</h3>
            <div className="flex items-center space-x-3">
              {taskStatusPolling && (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
              )}
              <span className="text-sm text-blue-600">
                {taskStatusPolling ? '识别中...' : '识别完成'}
              </span>
              <button
                onClick={() => setProgressCollapsed(!progressCollapsed)}
                className="p-1 hover:bg-blue-100 rounded transition-colors"
                title={progressCollapsed ? '展开' : '折叠'}
              >
                {progressCollapsed ? (
                  <ChevronDown className="h-4 w-4 text-blue-600" />
                ) : (
                  <ChevronUp className="h-4 w-4 text-blue-600" />
                )}
              </button>
            </div>
          </div>
          
          {!progressCollapsed && currentPassports.length > 0 && (
            <div className="space-y-3">
              {/* 总体进度条 */}
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-700">总体进度</span>
                  <span className="text-gray-700">
                    {currentPassports.filter(p => p.processing_status === 'completed').length} / {currentPassports.length}
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                    style={{
                      width: `${(currentPassports.filter(p => p.processing_status === 'completed').length / currentPassports.length) * 100}%`
                    }}
                  ></div>
                </div>
              </div>
              
              {/* 状态统计 */}
              <div className="grid grid-cols-4 gap-4 text-center text-xs">
                <div className="bg-white rounded p-2">
                  <div className="text-lg font-semibold text-green-600">
                    {currentPassports.filter(p => p.processing_status === 'completed').length}
                  </div>
                  <div className="text-gray-500">已完成</div>
                </div>
                <div className="bg-white rounded p-2">
                  <div className="text-lg font-semibold text-blue-600">
                    {currentPassports.filter(p => p.processing_status === 'processing').length}
                  </div>
                  <div className="text-gray-500">识别中</div>
                </div>
                <div className="bg-white rounded p-2">
                  <div className="text-lg font-semibold text-yellow-600">
                    {currentPassports.filter(p => p.processing_status === 'pending').length}
                  </div>
                  <div className="text-gray-500">等待中</div>
                </div>
                <div className="bg-white rounded p-2">
                  <div className="text-lg font-semibold text-red-600">
                    {currentPassports.filter(p => p.processing_status === 'failed').length}
                  </div>
                  <div className="text-gray-500">失败</div>
                </div>
              </div>
            </div>
          )}
          
          {/* 折叠状态下的简要信息 */}
          {progressCollapsed && currentPassports.length > 0 && (
            <div className="flex items-center justify-between text-sm text-gray-600">
              <span>
                已完成 {currentPassports.filter(p => p.processing_status === 'completed').length} / {currentPassports.length} 张图片
              </span>
              <div className="w-32 bg-gray-200 rounded-full h-1">
                <div 
                  className="bg-blue-600 h-1 rounded-full transition-all duration-300"
                  style={{
                    width: `${(currentPassports.filter(p => p.processing_status === 'completed').length / currentPassports.length) * 100}%`
                  }}
                ></div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* 当前任务结果 */}
      {currentTaskId && (
        <div className="bg-gray-50 rounded-lg border border-gray-200 flex flex-col">
          <div className="flex-shrink-0 px-4 py-3 border-b border-gray-200 bg-white rounded-t-lg">
            <div className="flex items-center justify-between min-w-0">
              <h3 className="text-lg font-semibold text-gray-900 flex-shrink-0">识别结果</h3>
              <div className="flex items-center space-x-3 flex-shrink-0 ml-4">
                <span className="text-sm text-gray-500 truncate">任务ID: {currentTaskId}</span>
                {canStopTask() && (
                  <button
                    onClick={() => stopTask(currentTaskId)}
                    className="inline-flex items-center px-3 py-2 border border-red-300 shadow-sm text-sm leading-4 font-medium rounded-md text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors whitespace-nowrap"
                  >
                    <StopCircle className="h-4 w-4 mr-2" />
                    停止任务
                  </button>
                )}
                <button
                  onClick={refreshCurrentTask}
                  className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors whitespace-nowrap"
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  刷新
                </button>
                <button
                  onClick={exportCurrentTask}
                  className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors whitespace-nowrap"
                >
                  <Download className="h-4 w-4 mr-2" />
                  导出
                </button>
              </div>
            </div>
          </div>
          
          {currentPassports.length === 0 ? (
            <div className="flex-1 flex items-center justify-center text-gray-500 bg-white">
              <div className="text-center">
                <AlertCircle className="h-12 w-12 text-gray-300 mx-auto mb-2" />
                <p>暂无识别结果</p>
              </div>
            </div>
          ) : (
            <div className="bg-white overflow-y-auto">
              <div className="overflow-x-auto">
                <table className="w-full table-auto divide-y divide-gray-200 relative">
                  <thead className="bg-gray-50 sticky top-0 z-10">
                    <tr>
                      <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">
                        序号
                      </th>
                      <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">
                        护照图片
                      </th>
                      <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">
                        状态
                      </th>
                      <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">
                        一致性检查
                      </th>
                      <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">
                        证件类型
                      </th>
                      <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">
                        证件号码
                      </th>
                      <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">
                        姓氏
                      </th>
                      <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">
                        名字
                      </th>
                      <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">
                        性别
                      </th>
                      <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">
                        出生日期
                      </th>
                      <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">
                        国籍
                      </th>
                      <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">
                        旅客类型
                      </th>
                      <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">
                        签发国
                      </th>
                      <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">
                        签发日期
                      </th>
                      <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">
                        有效期至
                      </th>
                      <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">
                        SSR DOCS 码
                      </th>
                      <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">
                        MRZ第一行
                      </th>
                      <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">
                        MRZ第二行
                      </th>
                      <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap sticky right-0 bg-gray-50/95 backdrop-blur-sm border-l border-gray-200 shadow-lg z-20">
                        复制
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {currentPassports.map((passport, index) => (
                      <tr key={passport.id} className="hover:bg-gray-50">
                        <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-500 text-center cursor-pointer hover:bg-gray-50"
                            onDoubleClick={() => copyFieldValue((index + 1).toString(), '序号')}>
                          {index + 1}
                        </td>
                        <td className="px-3 py-4 whitespace-nowrap">
                          <PassportImage
                            imageUrl={passport.uploaded_image_url}
                            passportData={passport}
                            allPassports={currentPassports}
                            currentIndex={index}
                            className="w-16 h-10 object-cover rounded border"
                          />
                        </td>
                        <td className="px-3 py-4 whitespace-nowrap cursor-pointer hover:bg-gray-50"
                            onDoubleClick={() => copyFieldValue(getStatusText(passport.processing_status), '状态')}>
                          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(passport.processing_status)}`}>
                            {getStatusText(passport.processing_status)}
                          </span>
                        </td>
                        <td className="px-3 py-4 whitespace-nowrap cursor-pointer hover:bg-gray-50"
                            onDoubleClick={() => copyFieldValue(formatConsistency(passport.viz_mrz_consistency).text, '一致性检查')}>
                          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${formatConsistency(passport.viz_mrz_consistency).color}`}>
                            {formatConsistency(passport.viz_mrz_consistency).text}
                          </span>
                        </td>
                        <td className="px-3 py-4 text-sm text-gray-900 whitespace-nowrap cursor-pointer hover:bg-gray-50" 
                            title={passport.certificate_type || '-'}
                            onDoubleClick={() => copyFieldValue(passport.certificate_type, '证件类型')}>
                          {passport.certificate_type || '-'}
                        </td>
                        <td className="px-3 py-4 text-sm font-medium text-gray-900 whitespace-nowrap cursor-pointer hover:bg-gray-50" 
                            title={passport.certificate_number || passport.passport_number || '-'}
                            onDoubleClick={() => copyFieldValue(passport.certificate_number || passport.passport_number, '证件号码')}>
                          {passport.certificate_number || passport.passport_number || '-'}
                        </td>
                        <td className="px-3 py-4 text-sm text-gray-900 whitespace-nowrap cursor-pointer hover:bg-gray-50" 
                            title={passport.surname || '-'}
                            onDoubleClick={() => copyFieldValue(passport.surname, '姓氏')}>
                          {passport.surname || '-'}
                        </td>
                        <td className="px-3 py-4 text-sm text-gray-900 whitespace-nowrap cursor-pointer hover:bg-gray-50" 
                            title={passport.given_names || '-'}
                            onDoubleClick={() => copyFieldValue(passport.given_names, '名字')}>
                          {passport.given_names || '-'}
                        </td>
                        <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-900 text-center cursor-pointer hover:bg-gray-50"
                            onDoubleClick={() => copyFieldValue(formatSex(passport.sex), '性别')}>
                          {formatSex(passport.sex)}
                        </td>
                        <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-900 cursor-pointer hover:bg-gray-50"
                            onDoubleClick={() => copyFieldValue(formatPassportDate(passport.date_of_birth), '出生日期')}>
                          {formatPassportDate(passport.date_of_birth)}
                        </td>
                        <td className="px-3 py-4 text-sm text-gray-900 whitespace-nowrap cursor-pointer hover:bg-gray-50" 
                            title={passport.nationality || '-'}
                            onDoubleClick={() => copyFieldValue(passport.nationality, '国籍')}>
                          {passport.nationality || '-'}
                        </td>
                        <td className="px-3 py-4 text-sm text-gray-900 whitespace-nowrap cursor-pointer hover:bg-gray-50" 
                            title={passport.passenger_type || '-'}
                            onDoubleClick={() => copyFieldValue(passport.passenger_type, '旅客类型')}>
                          {passport.passenger_type || '-'}
                        </td>
                        <td className="px-3 py-4 text-sm text-gray-900 whitespace-nowrap cursor-pointer hover:bg-gray-50" 
                            title={passport.country_of_issue || '-'}
                            onDoubleClick={() => copyFieldValue(passport.country_of_issue, '签发国')}>
                          {passport.country_of_issue || '-'}
                        </td>
                        <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-900 cursor-pointer hover:bg-gray-50"
                            onDoubleClick={() => copyFieldValue(formatPassportDate(passport.date_of_issue), '签发日期')}>
                          {formatPassportDate(passport.date_of_issue)}
                        </td>
                        <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-900 cursor-pointer hover:bg-gray-50"
                            onDoubleClick={() => copyFieldValue(formatPassportDate(passport.date_of_expiry), '有效期至')}>
                          {formatPassportDate(passport.date_of_expiry)}
                        </td>
                        <td className="px-3 py-4 cursor-pointer hover:bg-gray-50" 
                            title={passport.ssr_code || '-'}
                            onDoubleClick={() => copyFieldValue(passport.ssr_code, 'SSR码')}>
                          <div className="text-sm text-gray-900 whitespace-nowrap font-mono text-xs">
                            {passport.ssr_code || '-'}
                          </div>
                        </td>
                        <td className="px-3 py-4 text-sm text-gray-900 cursor-pointer hover:bg-gray-50 font-mono text-xs whitespace-nowrap" 
                            title={passport.mrz_line1 || '-'}
                            onDoubleClick={() => copyFieldValue(passport.mrz_line1, 'MRZ第一行')}>
                          {passport.mrz_line1 || '-'}
                        </td>
                        <td className="px-3 py-4 text-sm text-gray-900 cursor-pointer hover:bg-gray-50 font-mono text-xs whitespace-nowrap" 
                            title={passport.mrz_line2 || '-'}
                            onDoubleClick={() => copyFieldValue(passport.mrz_line2, 'MRZ第二行')}>
                          {passport.mrz_line2 || '-'}
                        </td>
                        <td className="px-3 py-4 whitespace-nowrap sticky right-0 bg-white/95 backdrop-blur-sm border-l border-gray-200 shadow-lg z-10">
                          <button
                            onClick={() => copyPassportInfo(passport)}
                            className="text-blue-600 hover:text-blue-900 transition-colors p-1 hover:bg-blue-50 rounded"
                            title="复制全部信息"
                          >
                            {copiedRowId === passport.id ? (
                              <Check className="h-4 w-4 text-green-600" />
                            ) : (
                              <Copy className="h-4 w-4" />
                            )}
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );

  const renderHistoryTasks = () => (
    <div className="flex flex-col space-y-4">
      <div className="flex-shrink-0 bg-gray-50 rounded-lg border border-gray-200 p-4">
        <div className="flex items-center justify-between min-w-0">
          <div className="min-w-0 flex-1">
            <h3 className="text-lg font-semibold text-gray-900">识别历史</h3>
            <p className="text-sm text-gray-600 mt-1">点击任务查看详细的识别结果</p>
          </div>
          <button
            onClick={refreshHistoryTasks}
            className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors flex-shrink-0 ml-4 whitespace-nowrap"
            disabled={historyLoading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${historyLoading ? 'animate-spin' : ''}`} />
            刷新列表
          </button>
        </div>
      </div>
      
      <div className="flex-1 min-h-0 bg-gray-50 rounded-lg border border-gray-200 flex flex-col">
        {historyLoading ? (
          <div className="flex items-center justify-center h-64 bg-white">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          </div>
        ) : historyError ? (
          <div className="p-6 bg-red-50 border border-red-200 text-red-700 m-4 rounded-lg">
            <div className="flex items-center">
              <XCircle className="h-5 w-5 mr-2" />
              <span className="font-medium">加载失败</span>
            </div>
            <p className="text-sm mt-1">{historyError}</p>
          </div>
        ) : historyTasks.length === 0 ? (
          <div className="flex items-center justify-center h-64 text-gray-500 bg-white">
            <div className="text-center">
              <History className="h-12 w-12 text-gray-300 mx-auto mb-4" />
              <h4 className="text-lg font-medium text-gray-900 mb-2">暂无历史记录</h4>
              <p>还没有任何护照识别任务</p>
            </div>
          </div>
        ) : (
          <>
            <div className="bg-white rounded-t-lg overflow-x-auto">
              <table className="w-full divide-y divide-gray-200">
                <thead className="bg-gray-50 sticky top-0 z-10">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">
                      序号
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">
                      任务ID
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">
                      总文件数
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">
                      处理状态
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">
                      创建时间
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">
                      操作
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {historyTasks.map((task, index) => (
                    <tr key={task.task_id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">
                        {(historyCurrentPage - 1) * historyPageSize + index + 1}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <FolderOpen className="h-4 w-4 text-gray-400 mr-2" />
                          <span className="text-sm font-mono text-gray-900">{task.task_id}</span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div className="flex items-center">
                          <BarChart3 className="h-4 w-4 text-gray-400 mr-2" />
                          <span className="font-medium">{task.total_files}</span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center space-x-2 text-xs">
                          <span className="inline-flex items-center px-2 py-1 rounded-full bg-green-100 text-green-800">
                            <CheckCircle className="h-3 w-3 mr-1" />
                            {task.completed_files}
                          </span>
                          {task.processing_files > 0 && (
                            <span className="inline-flex items-center px-2 py-1 rounded-full bg-blue-100 text-blue-800">
                              <Clock className="h-3 w-3 mr-1" />
                              {task.processing_files}
                            </span>
                          )}
                          {task.failed_files > 0 && (
                            <span className="inline-flex items-center px-2 py-1 rounded-full bg-red-100 text-red-800">
                              <XCircle className="h-3 w-3 mr-1" />
                              {task.failed_files}
                            </span>
                          )}
                          {task.pending_files > 0 && (
                            <span className="inline-flex items-center px-2 py-1 rounded-full bg-yellow-100 text-yellow-800">
                              <AlertCircle className="h-3 w-3 mr-1" />
                              {task.pending_files}
                            </span>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div className="flex items-center">
                          <Calendar className="h-4 w-4 text-gray-400 mr-2" />
                          {formatDate(task.created_at)}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex items-center space-x-3">
                          <button
                            onClick={() => navigate(`/task-detail/${task.task_id}?returnTab=history`)}
                            className="text-blue-600 hover:text-blue-900 transition-colors"
                          >
                            查看详情
                          </button>
                          <button
                            onClick={() => exportHistoryTask(task.task_id)}
                            className="inline-flex items-center text-green-600 hover:text-green-900 transition-colors"
                            title="导出Excel"
                          >
                            <Download className="h-4 w-4 mr-1" />
                            导出
                          </button>
                          {!isTaskCompleted(task) && (
                            <button
                              onClick={() => refreshSingleTask()}
                              className="inline-flex items-center text-orange-600 hover:text-orange-900 transition-colors"
                              title="刷新任务状态"
                            >
                              <RefreshCw className="h-4 w-4 mr-1" />
                              刷新
                            </button>
                          )}
                          <button
                            onClick={() => deleteTask(task.task_id)}
                            className="inline-flex items-center text-red-600 hover:text-red-900 transition-colors"
                            title="删除任务"
                          >
                            <Trash2 className="h-4 w-4 mr-1" />
                            删除
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
            
            {/* 分页组件 */}
            {historyTotalItems > historyPageSize && (
              <div className="flex-shrink-0 px-6 py-4 border-t border-gray-200 bg-white rounded-b-lg">
                <Pagination
                  currentPage={historyCurrentPage}
                  totalPages={Math.ceil(historyTotalItems / historyPageSize)}
                  totalItems={historyTotalItems}
                  pageSize={historyPageSize}
                  onPageChange={setHistoryCurrentPage}
                />
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );

  return (
    <MainLayout>
      {/* 主框架容器 */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 flex flex-col min-h-0">
        {/* 固定头部区域 */}
        <div className="flex-shrink-0 p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">护照识别</h1>
              <p className="text-gray-600 mt-2">上传护照图片进行自动识别</p>
            </div>
            <div className="flex items-center space-x-4">
              <Button onClick={handleUpload} disabled={uploadLoading}>
                <Upload className={`h-5 w-5 mr-2 ${uploadLoading ? 'animate-spin' : ''}`} />
                {uploadLoading ? '上传中...' : '上传护照'}
              </Button>
            </div>
          </div>
        </div>

        {/* Tab切换区域 - 固定高度 */}
        <div className="flex-shrink-0 px-6 border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            <button
              onClick={() => setActiveTab('current')}
              className={`${
                activeTab === 'current'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-colors`}
            >
              当前任务
            </button>
            <button
              onClick={() => setActiveTab('history')}
              className={`${
                activeTab === 'history'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-colors`}
            >
              历史记录
            </button>
          </nav>
        </div>

        {/* Tab内容区域 - 可滚动 */}
        <div className="flex-1 p-6 overflow-y-auto">
          {activeTab === 'current' ? (
            <div className="space-y-6">
              {/* 当前任务列表 */}
              {renderCurrentTask()}
            </div>
          ) : (
            <div className="space-y-6">
              {/* 历史任务列表 */}
              {renderHistoryTasks()}
            </div>
          )}
        </div>
      </div>
    </MainLayout>
  );
};

export default PassportRecognitionPage;