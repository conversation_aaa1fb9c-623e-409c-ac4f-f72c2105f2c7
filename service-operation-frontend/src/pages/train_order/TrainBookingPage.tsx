import React, { useState, useEffect, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { ArrowLeft, Upload, FileSpreadsheet, Download, Train, Settings, Copy, Check, Trash2 } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { useToast } from '@/hooks/use-toast';
import MainLayout from '@/components/layout/MainLayout';
import { API_BASE_URL } from '@/utils/constants';

interface TrainOrder {
  id: number;
  sequence_number: number;
  traveler_full_name: string;
  id_number: string;
  mobile_phone: string;
  travel_date: string;
  departure_station: string;
  arrival_station: string;
  train_number: string;
  seat_type: string;
  departure_time: string;
  arrival_time: string;
  cost_center: string;
  amount: number | null;
  order_number: string;
  created_at: string;
  updated_at: string;
}

interface TrainOrderListResponse {
  total: number;
  page: number;
  page_size: number;
  items: TrainOrder[];
}

interface Project {
  id: string;
  project_name: string;
  client_name: string;
}

const TrainBookingPage: React.FC = () => {
  const { projectId } = useParams<{ projectId: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  const [project, setProject] = useState<Project | null>(null);
  const [loading, setLoading] = useState(true);
  const [orders, setOrders] = useState<TrainOrder[]>([]);
  const [uploading, setUploading] = useState(false);
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [smsNotify, setSmsNotify] = useState(false);
  const [textInput, setTextInput] = useState('');
  
  // 分页状态
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(20);
  const [totalCount, setTotalCount] = useState(0);
  const [pageLoading, setPageLoading] = useState(false);

  // 获取认证token
  const getAuthToken = () => {
    return localStorage.getItem('access_token');
  };

  // 获取认证头
  const getAuthHeaders = () => {
    const token = getAuthToken();
    return {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
    };
  };

  useEffect(() => {
    if (projectId) {
      // 检查认证状态
      const token = getAuthToken();
      console.log('页面加载时的认证状态:', {
        hasToken: !!token,
        tokenLength: token?.length || 0,
        projectId: projectId
      });
      
      loadProjectData();
      loadTrainOrders(1); // 加载第一页数据
    }
  }, [projectId]);

  const loadProjectData = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/project/${projectId}`, {
        headers: getAuthHeaders(),
      });
      
      if (response.ok) {
        const data = await response.json();
        setProject(data);
      } else {
        toast({
          title: "错误",
          description: "无法加载项目信息",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('加载项目失败:', error);
      toast({
        title: "错误",
        description: "网络错误，请稍后重试",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const loadTrainOrders = async (page: number) => {
    if (!projectId) return;
    
    setPageLoading(true);
    try {
      const response = await fetch(
        `${API_BASE_URL}/train-order/project/${projectId}?page=${page}&page_size=${pageSize}`,
        {
          headers: getAuthHeaders(),
        }
      );

      if (response.ok) {
        const data: TrainOrderListResponse = await response.json();
        setOrders(data.items);
        setTotalCount(data.total);
        setCurrentPage(data.page);
      } else {
        toast({
          title: "错误",
          description: "无法加载火车票订单数据",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('加载订单失败:', error);
      toast({
        title: "错误",
        description: "网络错误，请稍后重试",
        variant: "destructive",
      });
    } finally {
      setPageLoading(false);
    }
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setUploadedFile(file);
    }
  };

  const removeUploadedFile = () => {
    setUploadedFile(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const uploadExcelFile = async () => {
    if (!uploadedFile || !projectId) return;

    const token = getAuthToken();
    if (!token) {
      toast({
        title: "认证错误",
        description: "请先登录系统",
        variant: "destructive",
      });
      return;
    }

    setUploading(true);
    try {
      const formData = new FormData();
      formData.append('file', uploadedFile);
      formData.append('project_id', projectId);

      console.log('开始上传Excel文件:', uploadedFile.name);
      console.log('项目ID:', projectId);
      console.log('Token存在:', !!token);

      const response = await fetch(`${API_BASE_URL}/train-order/upload-excel`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
        body: formData,
      });

      console.log('上传响应状态:', response.status);

      if (response.ok) {
        const result = await response.json();
        console.log('上传成功结果:', result);
        
        toast({
          title: "上传成功",
          description: result.message,
        });

        // 重新加载数据
        loadTrainOrders(1);
        
        // 清空上传文件
        removeUploadedFile();
      } else {
        const errorText = await response.text();
        console.error('上传失败响应:', errorText);
        
        let errorMessage = "文件上传失败，请检查文件格式";
        try {
          const errorData = JSON.parse(errorText);
          errorMessage = errorData.detail || errorMessage;
        } catch (e) {
          errorMessage = errorText || errorMessage;
        }

        toast({
          title: "上传失败",
          description: errorMessage,
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('上传异常:', error);
      toast({
        title: "上传失败",
        description: "网络错误，请稍后重试",
        variant: "destructive",
      });
    } finally {
      setUploading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed':
        return 'bg-green-100 text-green-800';
      case 'error':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-yellow-100 text-yellow-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'confirmed':
        return '验证通过';
      case 'error':
        return '异常';
      default:
        return '待验证';
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: "复制成功",
      description: "内容已复制到剪贴板",
    });
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString();
  };

  const formatTime = (datetimeString: string) => {
    if (!datetimeString) return '-';
    try {
      // 处理datetime字符串，只显示时间部分
      const date = new Date(datetimeString);
      if (isNaN(date.getTime())) return '-';
      
      return date.toLocaleTimeString('zh-CN', { 
        hour: '2-digit',
        minute: '2-digit',
        hour12: false
      });
    } catch (error) {
      return '-';
    }
  };

  // 分页组件
  const renderPagination = () => {
    const totalPages = Math.ceil(totalCount / pageSize);
    if (totalPages <= 1) return null;

    const pages = [];
    for (let i = 1; i <= totalPages; i++) {
      if (i === 1 || i === totalPages || (i >= currentPage - 2 && i <= currentPage + 2)) {
        pages.push(i);
      } else if (i === currentPage - 3 || i === currentPage + 3) {
        pages.push('...');
      }
    }

    return (
      <div className="flex items-center justify-between mt-6">
        <div className="text-sm text-gray-500">
          共 {totalCount} 条记录，第 {currentPage} / {totalPages} 页
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => loadTrainOrders(currentPage - 1)}
            disabled={currentPage === 1 || pageLoading}
          >
            上一页
          </Button>
          {pages.map((page, index) => (
            <React.Fragment key={index}>
              {page === '...' ? (
                <span className="px-2">...</span>
              ) : (
                <Button
                  variant={page === currentPage ? "default" : "outline"}
                  size="sm"
                  onClick={() => loadTrainOrders(page as number)}
                  disabled={pageLoading}
                >
                  {page}
                </Button>
              )}
            </React.Fragment>
          ))}
          <Button
            variant="outline"
            size="sm"
            onClick={() => loadTrainOrders(currentPage + 1)}
            disabled={currentPage === totalPages || pageLoading}
          >
            下一页
          </Button>
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <MainLayout>
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">加载中...</p>
          </div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="min-h-screen bg-gray-50">
        <div className="bg-white border-b">
          <div className="max-w-7xl mx-auto">
            <div className="flex items-center justify-between p-6">
              <div className="flex items-center gap-4">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => navigate(`/project-detail/${projectId}`)}
                  className="text-gray-600 hover:text-gray-900 p-2"
                >
                  <ArrowLeft className="h-4 w-4" />
                </Button>
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <Train className="h-6 w-6 text-blue-600" />
                  </div>
                  <div>
                    <h1 className="text-xl font-semibold text-gray-900">
                      {project?.project_name || '项目名称'} - 火车票预订
                    </h1>
                    <p className="text-sm text-gray-500">
                      客户：{project?.client_name || '客户名称'}
                    </p>
                  </div>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <Button variant="outline" size="sm">
                  <Download className="h-4 w-4 mr-2" />
                  导出
                </Button>
                <Button variant="outline" size="sm">
                  预定
                </Button>
                <Button size="sm">
                  预定并出票
                </Button>
              </div>
            </div>
          </div>
        </div>

        <div className="max-w-7xl mx-auto p-6">
          <Tabs defaultValue="excel" className="space-y-6">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="excel" className="flex items-center gap-2">
                <FileSpreadsheet className="h-4 w-4" />
                Excel上传
              </TabsTrigger>
              <TabsTrigger value="text">文本粘贴</TabsTrigger>
              <TabsTrigger value="recognition">证照识别</TabsTrigger>
            </TabsList>

            <TabsContent value="excel" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Upload className="h-5 w-5" />
                    上传Excel文件
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                    {uploadedFile ? (
                      <div className="space-y-4">
                        <div className="flex items-center justify-center gap-2 text-green-600">
                          <Check className="h-5 w-5" />
                          <span>已选择文件: {uploadedFile.name}</span>
                        </div>
                        <div className="flex items-center justify-center gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={removeUploadedFile}
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            移除文件
                          </Button>
                          <Button
                            size="sm"
                            onClick={uploadExcelFile}
                            disabled={uploading}
                          >
                            {uploading ? (
                              <>
                                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                                上传中...
                              </>
                            ) : (
                              <>
                                <Upload className="h-4 w-4 mr-2" />
                                开始上传
                              </>
                            )}
                          </Button>
                        </div>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        <div className="mx-auto w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                          <FileSpreadsheet className="h-6 w-6 text-gray-400" />
                        </div>
                        <div>
                          <p className="text-gray-600 mb-2">点击上传或拖拽Excel文件到此处</p>
                          <p className="text-sm text-gray-400">支持 .xlsx 和 .xls 格式</p>
                        </div>
                        <div className="flex items-center justify-center gap-4">
                          <input
                            ref={fileInputRef}
                            type="file"
                            accept=".xlsx,.xls"
                            onChange={handleFileUpload}
                            className="hidden"
                          />
                          <Button
                            variant="outline"
                            onClick={() => fileInputRef.current?.click()}
                          >
                            选择文件
                          </Button>
                          <Button variant="outline">
                            下载模板
                          </Button>
                        </div>
                      </div>
                    )}
                  </div>
                  
                  {/* <div className="flex items-center space-x-2">
                    <Checkbox 
                      id="sms-notify" 
                      checked={smsNotify} 
                      onCheckedChange={(checked: boolean) => setSmsNotify(checked)}
                    />
                    <Label htmlFor="sms-notify" className="text-sm text-gray-600">
                      上传完成后发送短信通知
                    </Label>
                  </div> */}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="text" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>文本粘贴</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <Textarea
                      placeholder="请粘贴火车票预订信息，每行一条记录..."
                      value={textInput}
                      onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => setTextInput(e.target.value)}
                      className="min-h-[200px]"
                    />
                    <div className="flex items-center space-x-2">
                      <Checkbox 
                        id="text-sms-notify" 
                        checked={smsNotify} 
                        onCheckedChange={(checked: boolean) => setSmsNotify(checked)}
                      />
                      <Label htmlFor="text-sms-notify" className="text-sm text-gray-600">
                        处理完成后发送短信通知
                      </Label>
                    </div>
                    <Button>
                      解析并导入
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="recognition" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>证照识别</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-12">
                    <div className="mx-auto w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center mb-4">
                      <Settings className="h-8 w-8 text-gray-400" />
                    </div>
                    <p className="text-gray-500">证照识别功能开发中...</p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>

          {/* 数据表格 */}
          <div className="mt-8">
            <Card>
              <CardHeader>
                <CardTitle>火车票订单列表</CardTitle>
              </CardHeader>
              <CardContent>
                {pageLoading ? (
                  <div className="text-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                    <p className="text-gray-600">加载中...</p>
                  </div>
                ) : orders.length > 0 ? (
                  <>
                    <div className="overflow-x-auto">
                      <table className="w-full text-sm">
                        <thead>
                          <tr className="border-b border-gray-200 bg-gray-50">
                            <th className="text-left p-3 font-medium text-gray-900">序号</th>
                            <th className="text-left p-3 font-medium text-gray-900">状态</th>
                            <th className="text-left p-3 font-medium text-gray-900">出行人姓名</th>
                            <th className="text-left p-3 font-medium text-gray-900">证件号码</th>
                            <th className="text-left p-3 font-medium text-gray-900">手机号</th>
                            <th className="text-left p-3 font-medium text-gray-900">出行日期</th>
                            <th className="text-left p-3 font-medium text-gray-900">出发站</th>
                            <th className="text-left p-3 font-medium text-gray-900">到达站</th>
                            <th className="text-left p-3 font-medium text-gray-900">车次</th>
                            <th className="text-left p-3 font-medium text-gray-900">座位类型</th>
                            <th className="text-left p-3 font-medium text-gray-900">出发时间</th>
                            <th className="text-left p-3 font-medium text-gray-900">到达时间</th>
                            <th className="text-left p-3 font-medium text-gray-900">成本中心</th>
                            <th className="text-left p-3 font-medium text-gray-900">金额</th>
                            <th className="text-left p-3 font-medium text-gray-900">订单号</th>
                            <th className="sticky right-0 bg-gray-50/95 backdrop-blur-sm border-l border-gray-200 text-left p-3 font-medium text-gray-900 shadow-lg z-20">复制</th>
                          </tr>
                        </thead>
                        <tbody>
                          {orders.map((order, index) => {
                            // 模拟状态
                            const status = index % 3 === 0 ? 'confirmed' : index % 3 === 1 ? 'error' : 'pending';
                            
                            return (
                              <tr key={order.id} className="border-b border-gray-100 hover:bg-gray-50">
                                <td className="p-3 text-gray-900">{order.sequence_number}</td>
                                <td className="p-3">
                                  <span className={`inline-block px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(status)}`}>
                                    {getStatusText(status)}
                                  </span>
                                </td>
                                <td className="p-3 text-gray-900">{order.traveler_full_name}</td>
                                <td className="p-3 text-gray-600">{order.id_number || '-'}</td>
                                <td className="p-3 text-gray-600">{order.mobile_phone || '-'}</td>
                                <td className="p-3 text-gray-600">{formatDate(order.travel_date)}</td>
                                <td className="p-3 text-gray-600">{order.departure_station || '-'}</td>
                                <td className="p-3 text-gray-600">{order.arrival_station || '-'}</td>
                                <td className="p-3 text-gray-900 font-medium">{order.train_number || '-'}</td>
                                <td className="p-3 text-gray-600">{order.seat_type || '-'}</td>
                                <td className="p-3 text-gray-600">{formatTime(order.departure_time)}</td>
                                <td className="p-3 text-gray-600">{formatTime(order.arrival_time)}</td>
                                <td className="p-3 text-gray-600">{order.cost_center || '-'}</td>
                                <td className="p-3 text-gray-900">
                                  {order.amount ? `¥${order.amount}` : '-'}
                                </td>
                                <td className="p-3 text-gray-600">{order.order_number || '-'}</td>
                                <td className="sticky right-0 bg-white/95 backdrop-blur-sm border-l border-gray-200 p-3 shadow-lg z-10">
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => copyToClipboard(`${order.traveler_full_name} ${order.train_number} ${order.departure_station}-${order.arrival_station}`)}
                                  >
                                    <Copy className="h-4 w-4" />
                                  </Button>
                                </td>
                              </tr>
                            );
                          })}
                        </tbody>
                      </table>
                    </div>
                    {renderPagination()}
                  </>
                ) : (
                  <div className="text-center py-12">
                    <div className="mx-auto w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center mb-4">
                      <Train className="h-8 w-8 text-gray-400" />
                    </div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">暂无数据</h3>
                    <p className="text-gray-500 mb-4">请先上传Excel文件或通过其他方式添加火车票订单</p>
                    <Button
                      variant="outline"
                      onClick={() => fileInputRef.current?.click()}
                    >
                      <Upload className="h-4 w-4 mr-2" />
                      上传Excel
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </MainLayout>
  );
};

export default TrainBookingPage; 