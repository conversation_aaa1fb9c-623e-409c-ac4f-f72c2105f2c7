import { api } from '@/services/system';
import {
  ProjectTask,
  CreateTaskRequest,
  UpdateTaskRequest,
  TaskListResponse,
  TaskQueryParams,
  TaskStats
} from '@/types/project-task';

export class ProjectTaskService {
  // 创建任务
  static async createTask(taskData: CreateTaskRequest): Promise<ProjectTask> {
    const response = await api.post<ProjectTask>('/project-task/', taskData);
    if (!response.success) {
      throw new Error(response.error || '创建任务失败');
    }
    return response.data!;
  }

  // 根据数据库ID获取任务
  static async getTask(taskId: number): Promise<ProjectTask> {
    const response = await api.get<ProjectTask>(`/project-task/${taskId}`);
    if (!response.success) {
      throw new Error(response.error || '获取任务失败');
    }
    return response.data!;
  }

  // 根据任务ID获取任务
  static async getTaskByTaskId(taskId: string): Promise<ProjectTask> {
    const response = await api.get<ProjectTask>(`/project-task/by-task-id/${taskId}`);
    if (!response.success) {
      throw new Error(response.error || '获取任务失败');
    }
    return response.data!;
  }

  // 获取任务列表
  static async getTasks(params?: TaskQueryParams): Promise<TaskListResponse> {
    const response = await api.get<TaskListResponse>('/project-task/', { params: params as any });
    if (!response.success) {
      throw new Error(response.error || '获取任务列表失败');
    }
    return response.data!;
  }

  // 获取指定项目的任务列表
  static async getTasksByProject(
    projectId: number,
    params?: { page?: number; page_size?: number }
  ): Promise<TaskListResponse> {
    const response = await api.get<TaskListResponse>(
      `/project-task/project/${projectId}/tasks`, 
      { params: params as any }
    );
    if (!response.success) {
      throw new Error(response.error || '获取项目任务列表失败');
    }
    return response.data!;
  }

  // 更新任务
  static async updateTask(taskId: number, taskData: UpdateTaskRequest): Promise<ProjectTask> {
    const response = await api.put<ProjectTask>(`/project-task/${taskId}`, taskData);
    if (!response.success) {
      throw new Error(response.error || '更新任务失败');
    }
    return response.data!;
  }

  // 删除任务
  static async deleteTask(taskId: number): Promise<void> {
    const response = await api.delete(`/project-task/${taskId}`);
    if (!response.success) {
      throw new Error(response.error || '删除任务失败');
    }
  }

  // 获取任务统计信息
  static async getTaskStats(): Promise<TaskStats> {
    const response = await api.get<TaskStats>('/project-task/stats/summary');
    if (!response.success) {
      throw new Error(response.error || '获取任务统计失败');
    }
    return response.data!;
  }

  // 获取项目最新任务设置
  static async getProjectLatestSettings(projectId: number, taskType?: string): Promise<{
    has_history: boolean;
    sms_notify: boolean;
    has_agent: boolean;
    agent_phone: string | null;
    last_task_title?: string;
    last_task_created?: string;
    message: string;
  }> {
    const params = taskType ? { task_type: taskType } : undefined;
    const response = await api.get<{
      has_history: boolean;
      sms_notify: boolean;
      has_agent: boolean;
      agent_phone: string | null;
      last_task_title?: string;
      last_task_created?: string;
      message: string;
    }>(`/project-task/project/${projectId}/latest-settings`, params ? { params } : undefined);
    if (!response.success) {
      throw new Error(response.error || '获取项目最新设置失败');
    }
    return response.data!;
  }
}

export default ProjectTaskService; 