import { apiClient } from './api';

// 用户相关类型定义
export interface User {
  id: number;
  username: string;
  email?: string;
  full_name?: string;
  department?: string;
  phone?: string;
  avatar?: string;
  sso_user_id?: string;
  last_login_at?: string;
  login_count: number;
  status: boolean;
  created_at: string;
  updated_at: string;
  created_by?: string;
  updated_by?: string;
  roles: Role[];
  applications: Application[];
}

export interface Role {
  id: number;
  role_name: string;
  role_code: string;
  description?: string;
  is_system: boolean;
  status: boolean;
  created_at: string;
  updated_at: string;
  created_by?: string;
  updated_by?: string;
}

export interface Permission {
  id: number;
  permission_name: string;
  permission_code: string;
  permission_type: string;
  parent_id: number;
  app_id?: number;
  app_name?: string;
  resource_path?: string;
  description?: string;
  sort_order: number;
  status: boolean;
  created_at: string;
  updated_at: string;
  created_by?: string;
  updated_by?: string;
}

export interface Application {
  id: number;
  app_name: string;
  app_code: string;
  app_url?: string;
  description?: string;
  icon?: string;
  sort_order: number;
  status: boolean;
  created_at: string;
  updated_at: string;
  created_by?: string;
  updated_by?: string;
}

export interface ListResponse<T> {
  items: T[];
  total: number;
  page: number;
  page_size: number;
  pages: number;
}

export interface UserPermissionDetail {
  user: User;
  role_permissions: Array<{
    role: {
      id: number;
      role_name: string;
      role_code: string;
      is_system: boolean;
    };
    permissions: Array<{
      id: number;
      permission_name: string;
      permission_code: string;
      permission_type: string;
    }>;
  }>;
  special_permissions: Permission[];
  applications: Application[];
}

// 用户管理API
export const userManagementApi = {
  // 用户管理
  async getUsers(page: number = 1, pageSize: number = 20, search?: string): Promise<ListResponse<User>> {
    const params = new URLSearchParams({
      page: page.toString(),
      page_size: pageSize.toString(),
    });
    
    if (search) {
      params.append('search', search);
    }
    
    const response = await apiClient.get(`/user-management/users?${params}`);
    return response.data;
  },

  async getUserById(userId: number): Promise<User> {
    const response = await apiClient.get(`/user-management/users/${userId}`);
    return response.data;
  },

  // 角色管理
  async getRoles(page: number = 1, pageSize: number = 20, search?: string): Promise<ListResponse<Role>> {
    const params = new URLSearchParams({
      page: page.toString(),
      page_size: pageSize.toString(),
    });
    
    if (search) {
      params.append('search', search);
    }
    
    const response = await apiClient.get(`/user-management/roles?${params}`);
    return response.data;
  },

  // 权限管理
  async getPermissions(page: number = 1, pageSize: number = 20, search?: string, appId?: number): Promise<ListResponse<Permission>> {
    const params = new URLSearchParams({
      page: page.toString(),
      page_size: pageSize.toString(),
    });
    
    if (search) {
      params.append('search', search);
    }
    
    if (appId) {
      params.append('app_id', appId.toString());
    }
    
    const response = await apiClient.get(`/user-management/permissions?${params}`);
    return response.data;
  },

  // 应用管理
  async getApplications(page: number = 1, pageSize: number = 20, search?: string): Promise<ListResponse<Application>> {
    const params = new URLSearchParams({
      page: page.toString(),
      page_size: pageSize.toString(),
    });
    
    if (search) {
      params.append('search', search);
    }
    
    const response = await apiClient.get(`/user-management/applications?${params}`);
    return response.data;
  },

  // 用户权限详情
  async getUserPermissionDetails(userId: number): Promise<UserPermissionDetail> {
    const response = await apiClient.get(`/user-management/user-permissions/${userId}`);
    return response.data;
  },

  // 当前用户权限
  async getMyPermissions(): Promise<any> {
    const response = await apiClient.get('/user-management/me/permissions');
    return response.data;
  },

  async getMyApplications(): Promise<Application[]> {
    const response = await apiClient.get('/user-management/me/applications');
    return response.data;
  },
};
