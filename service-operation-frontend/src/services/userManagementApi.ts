import { api } from './system/api';

// 用户相关类型定义
export interface User {
  id: number;
  username: string;
  member_id?: string;
  work_id?: string;
  email?: string;
  full_name?: string;
  department?: string;
  phone?: string;
  avatar?: string;
  sso_user_id?: string;
  last_login_at?: string;
  login_count: number;
  status: boolean;
  created_at: string;
  updated_at: string;
  created_by?: string;
  updated_by?: string;
  roles: Role[];
  applications: Application[];
}

export interface Role {
  id: number;
  role_name: string;
  role_code: string;
  description?: string;
  is_system: boolean;
  status: boolean;
  created_at: string;
  updated_at: string;
  created_by?: string;
  updated_by?: string;
}

export interface Permission {
  id: number;
  permission_name: string;
  permission_code: string;
  permission_type: string;
  parent_id: number;
  app_id?: number;
  app_name?: string;
  resource_path?: string;
  description?: string;
  sort_order: number;
  status: boolean;
  created_at: string;
  updated_at: string;
  created_by?: string;
  updated_by?: string;
}

export interface Application {
  id: number;
  app_name: string;
  app_code: string;
  app_url?: string;
  description?: string;
  icon?: string;
  sort_order: number;
  status: boolean;
  created_at: string;
  updated_at: string;
  created_by?: string;
  updated_by?: string;
}

export interface ListResponse<T> {
  items: T[];
  total: number;
  page: number;
  page_size: number;
  pages: number;
}

export interface UserPermissionDetail {
  user: User;
  role_permissions: Array<{
    role: {
      id: number;
      role_name: string;
      role_code: string;
      is_system: boolean;
    };
    permissions: Array<{
      id: number;
      permission_name: string;
      permission_code: string;
      permission_type: string;
    }>;
  }>;
  special_permissions: Permission[];
  applications: Application[];
}

// 用户管理API
export const userManagementApi = {
  // 用户管理
  async getUsers(page: number = 1, pageSize: number = 20, search?: string): Promise<ListResponse<User>> {
    const params: Record<string, string> = {
      page: page.toString(),
      page_size: pageSize.toString(),
    };

    if (search) {
      params.search = search;
    }

    const response = await api.get<ListResponse<User>>('/user-management/users', { params });
    if (!response.success) {
      throw new Error(response.error || '获取用户列表失败');
    }
    return response.data!;
  },

  async getUserById(userId: number): Promise<User> {
    const response = await api.get<User>(`/user-management/users/${userId}`);
    if (!response.success) {
      throw new Error(response.error || '获取用户详情失败');
    }
    return response.data!;
  },

  // 角色管理
  async getRoles(page: number = 1, pageSize: number = 20, search?: string): Promise<ListResponse<Role>> {
    const params: Record<string, string> = {
      page: page.toString(),
      page_size: pageSize.toString(),
    };

    if (search) {
      params.search = search;
    }

    const response = await api.get<ListResponse<Role>>('/user-management/roles', { params });
    if (!response.success) {
      throw new Error(response.error || '获取角色列表失败');
    }
    return response.data!;
  },

  // 权限管理
  async getPermissions(page: number = 1, pageSize: number = 20, search?: string, appId?: number): Promise<ListResponse<Permission>> {
    const params: Record<string, string> = {
      page: page.toString(),
      page_size: pageSize.toString(),
    };

    if (search) {
      params.search = search;
    }

    if (appId) {
      params.app_id = appId.toString();
    }

    const response = await api.get<ListResponse<Permission>>('/user-management/permissions', { params });
    if (!response.success) {
      throw new Error(response.error || '获取权限列表失败');
    }
    return response.data!;
  },

  async createPermission(permissionData: {
    permission_name: string;
    permission_code: string;
    permission_type: string;
    parent_id: number;
    app_id: number;
    resource_path: string;
    description: string;
    sort_order: number;
  }): Promise<Permission> {
    const response = await api.post<Permission>('/user-management/permissions', permissionData);
    if (!response.success) {
      throw new Error(response.error || '创建权限失败');
    }
    return response.data!;
  },

  async updatePermission(permissionId: number, permissionData: {
    permission_name: string;
    permission_code: string;
    permission_type: string;
    parent_id: number;
    app_id: number;
    resource_path: string;
    description: string;
    sort_order: number;
  }): Promise<Permission> {
    const response = await api.put<Permission>(`/user-management/permissions/${permissionId}`, permissionData);
    if (!response.success) {
      throw new Error(response.error || '更新权限失败');
    }
    return response.data!;
  },

  async deletePermission(permissionId: number): Promise<void> {
    const response = await api.delete(`/user-management/permissions/${permissionId}`);
    if (!response.success) {
      throw new Error(response.error || '删除权限失败');
    }
  },

  // 角色权限管理
  async getRolePermissions(roleId: number): Promise<{
    role: { id: number; role_name: string };
    permissions: Array<Permission & { assigned: boolean }>;
    assigned_permission_ids: number[];
  }> {
    const response = await api.get(`/user-management/roles/${roleId}/permissions`);
    if (!response.success) {
      throw new Error(response.error || '获取角色权限失败');
    }
    return response.data!;
  },

  async assignRolePermissions(roleId: number, permissionIds: number[]): Promise<{ message: string; assigned_count: number }> {
    const response = await api.post(`/user-management/roles/${roleId}/permissions`, permissionIds);
    if (!response.success) {
      throw new Error(response.error || '分配角色权限失败');
    }
    return response.data!;
  },

  // 用户角色权限管理
  async getUserRolesPermissions(userId: number): Promise<{
    user: { id: number; username: string; full_name?: string };
    roles: Array<Role & { assigned: boolean }>;
    permissions: Array<Permission & { has_role_permission: boolean; has_user_permission: boolean; effective: boolean }>;
    assigned_role_ids: number[];
    user_permission_ids: number[];
  }> {
    const response = await api.get(`/user-management/users/${userId}/roles-permissions`);
    if (!response.success) {
      throw new Error(response.error || '获取用户角色权限失败');
    }
    return response.data!;
  },

  async assignUserRoles(userId: number, roleIds: number[]): Promise<{ message: string; assigned_count: number }> {
    const response = await api.post(`/user-management/users/${userId}/roles`, roleIds);
    if (!response.success) {
      throw new Error(response.error || '分配用户角色失败');
    }
    return response.data!;
  },

  async assignUserPermissions(userId: number, permissionIds: number[]): Promise<{ message: string; assigned_count: number }> {
    const response = await api.post(`/user-management/users/${userId}/permissions`, permissionIds);
    if (!response.success) {
      throw new Error(response.error || '分配用户权限失败');
    }
    return response.data!;
  },

  // 应用管理
  async getApplications(page: number = 1, pageSize: number = 20, search?: string): Promise<ListResponse<Application>> {
    const params: Record<string, string> = {
      page: page.toString(),
      page_size: pageSize.toString(),
    };

    if (search) {
      params.search = search;
    }

    const response = await api.get<ListResponse<Application>>('/user-management/applications', { params });
    if (!response.success) {
      throw new Error(response.error || '获取应用列表失败');
    }
    return response.data!;
  },

  // 用户权限详情
  async getUserPermissionDetails(userId: number): Promise<UserPermissionDetail> {
    const response = await api.get<UserPermissionDetail>(`/user-management/user-permissions/${userId}`);
    if (!response.success) {
      throw new Error(response.error || '获取用户权限详情失败');
    }
    return response.data!;
  },

  // 当前用户权限
  async getMyPermissions(): Promise<any> {
    const response = await api.get<any>('/user-management/me/permissions');
    if (!response.success) {
      throw new Error(response.error || '获取当前用户权限失败');
    }
    return response.data!;
  },

  async getMyApplications(): Promise<Application[]> {
    const response = await api.get<Application[]>('/user-management/me/applications');
    if (!response.success) {
      throw new Error(response.error || '获取当前用户应用失败');
    }
    return response.data!;
  },

  // 更新用户状态
  async updateUserStatus(userId: number, status: boolean): Promise<User> {
    const response = await api.put<User>(`/user-management/users/${userId}/status`, { status });
    if (!response.success) {
      throw new Error(response.error || '更新用户状态失败');
    }
    return response.data!;
  },

  // 更新用户信息
  async updateUser(userId: number, userData: Partial<User>): Promise<User> {
    const response = await api.put<User>(`/user-management/users/${userId}`, userData);
    if (!response.success) {
      throw new Error(response.error || '更新用户信息失败');
    }
    return response.data!;
  },

  // 更新角色信息
  async updateRole(roleId: number, roleData: Partial<Role>): Promise<Role> {
    const response = await api.put<Role>(`/user-management/roles/${roleId}`, roleData);
    if (!response.success) {
      throw new Error(response.error || '更新角色信息失败');
    }
    return response.data!;
  },
};
