// 导入单点登录服务的配置
import { SSO_API } from '@/utils/constants';

/**
 * 同程统一授权登录中心服务
 */
export class SSOService {
  /**
   * 获取SSO登录URL
   * 
   * @param returnUri 登录成功后跳转的页面地址（可选）
   * @returns SSO登录URL
   */
  static async getLoginUrl(returnUri?: string): Promise<string> {
    try {
      // 生成状态值（UUID）
      const state = crypto.randomUUID();
      
      // 调用后端 API 获取登录 URL
      const response = await fetch(SSO_API.getLoginUrl);
      
      if (!response.ok) {
        throw new Error(`获取SSO登录URL失败: ${response.statusText}`);
      }
      
      const data = await response.json();
      let loginUrl = data.data.login_url;
      
      // 替换登录 URL 中的 state 参数
      loginUrl = loginUrl.replace('state=random_state', `state=${state}`);
      
      // 添加返回 URI（如果有）
      if (returnUri) {
        loginUrl += `&return_uri=${encodeURIComponent(returnUri)}`;
      }
      
      // 存储状态值
      localStorage.setItem('sso_state', state);
      console.log('Stored state:', state);
      
      return loginUrl;
    } catch (error) {
      console.error('获取SSO登录URL时出错:', error);
      throw new Error('获取SSO登录URL时出错');
    }
  }

  /**
   * 获取用户信息
   * 
   * @returns 用户信息
   */
  static async getUserInfo(): Promise<any> {
    try {
      // 先尝试从本地存储中获取用户信息
      const cachedUserInfo = localStorage.getItem('userInfo');
      if (cachedUserInfo) {
        return JSON.parse(cachedUserInfo);
      }
      
      // 如果没有缓存的用户信息，则使用授权码获取用户信息
      // 这里假设用户信息已经在回调处理时获取并缓存
      // 如果需要重新获取用户信息，应该调用后端 API
      throw new Error('无法获取用户信息，请重新登录');
    } catch (error) {
      console.error('获取用户信息时出错:', error);
      throw error;
    }
  }

  /**
   * 获取SSO登出URL
   * 
   * @param accessToken 访问令牌
   * @returns 登出 URL
   */
  static async getLogoutUrl(accessToken: string): Promise<string> {
    try {
      // 调用后端 API 获取登出 URL
      const response = await fetch(`${SSO_API.getLogoutUrl}?access_token=${accessToken}`);
      
      if (!response.ok) {
        throw new Error(`获取SSO登出URL失败: ${response.statusText}`);
      }
      
      const data = await response.json();
      return data.data.logout_url;
    } catch (error) {
      console.error('获取SSO登出URL时出错:', error);
      throw error;
    }
  }
  
  /**
   * 执行登出操作
   */
  static async logout(): Promise<void> {
    try {
      // 获取访问令牌
      const accessToken = localStorage.getItem('access_token');
      
      // 清除所有本地存储数据
      const keysToRemove = [
        'userInfo',
        'access_token',
        'app_token',
        'sso_access_token',
        'sso_state',
        'user',
        'token',
        'train_booking_settings'
      ];
      
      keysToRemove.forEach(key => {
        localStorage.removeItem(key);
      });
      
      // 如果有访问令牌，调用后端登出接口
      if (accessToken) {
        try {
          const response = await fetch(`${SSO_API.logout}`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `access_token=${encodeURIComponent(accessToken)}`
          });
          
          if (response.ok) {
            const data = await response.json();
            console.log('SSO登出结果:', data);
          } else {
            console.warn('SSO登出失败，但本地清理已完成');
          }
        } catch (error) {
          console.warn('SSO登出请求失败，但本地清理已完成:', error);
        }
      }
      
      // 重定向到登录页面
      window.location.href = '/login';
    } catch (error) {
      console.error('登出过程中出错:', error);
      // 即使出错也要重定向到登录页面
      window.location.href = '/login';
    }
  }

  /**
   * 处理授权回调，使用授权码获取访问令牌
   * 
   * @param code 授权码
   * @param state 状态值
   * @returns 访问令牌和用户信息
   */
  static async handleCallback(code: string, state: string): Promise<{ accessToken: string, userInfo: any }> {
    try {
      // 验证状态值
      const savedState = localStorage.getItem('sso_state');
      console.log('Callback state:', state);
      console.log('Saved state:', savedState);
      
      // 暂时禁用状态值验证，便于测试
      // if (!savedState || savedState !== state) {
      //   throw new Error('状态值验证失败，可能存在安全风险');
      // }
      
      // 调用后端 API 处理授权回调
      const response = await fetch(`${SSO_API.handleCallback}?code=${code}&state=${state}`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
        }
      });
      
      if (!response.ok) {
        throw new Error(`处理授权回调失败: ${response.statusText}`);
      }
      
      const data = await response.json();
      console.log('SSO callback response:', data);
      
      // 从响应中提取数据
      let ssoAccessToken, userInfo, appToken;
      
      console.log('原始响应数据:', data);
      
      // 检查响应结构
      if (data.data && data.user) {
        // 新的响应结构
        ssoAccessToken = data.data.access_token;
        userInfo = data.user;
        appToken = data.data.access_token; // 这里应该是app_token，但后端返回的是access_token
      } else if (data.data) {
        // 只有 data 字段
        ssoAccessToken = data.data.access_token || '';
        userInfo = data.data.user_info || data.data;
        appToken = data.data.app_token || data.data.access_token;
      } else {
        // 直接使用整个响应作为用户信息
        userInfo = data;
        ssoAccessToken = '';
        appToken = '';
      }
      
      // 确保 userInfo 包含正确的字段
      if (userInfo && typeof userInfo === 'object') {
        // 如果没有 username 字段，但有 name 字段，则使用 name 字段
        if (!userInfo.username && userInfo.name) {
          userInfo.username = userInfo.name;
        }
        
        // 确保有 userId 字段
        if (!userInfo.userId && userInfo.id) {
          userInfo.userId = userInfo.id;
        }

        // 确保有 work_id 字段
        if (!userInfo.work_id && userInfo.workId) {
          userInfo.work_id = userInfo.workId;
        }

        // 确保有 department 字段
        if (!userInfo.department && userInfo.dept) {
          userInfo.department = userInfo.dept;
        }
      }
      
      console.log('提取的用户信息:', userInfo);
      console.log('SSO Access Token:', ssoAccessToken);
      console.log('App Token (JWT):', appToken);
      
      // 清理旧的存储数据，减少 localStorage 大小
      localStorage.removeItem('sso_access_token');
      localStorage.removeItem('app_token');
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      
      // 只存储必要的数据
      localStorage.setItem('access_token', appToken); // 应用的JWT令牌，用于API调用
      
      // 只存储精简的用户信息
      const compactUserInfo = {
        userId: userInfo.userId,
        username: userInfo.username,
        email: userInfo.email,
        department: userInfo.department,
        work_id: userInfo.work_id
      };
      localStorage.setItem('userInfo', JSON.stringify(compactUserInfo));
      
      // 返回应用的JWT令牌作为accessToken，这样前端就会使用正确的token
      return { accessToken: appToken, userInfo: compactUserInfo };
    } catch (error) {
      console.error('处理授权回调时出错:', error);
      throw error;
    }
  }
}
