/**
 * 自动认证服务
 * 使用开发者密钥自动获取access_token
 */

import { API_BASE_URL, DEVELOPER_KEY, DEVELOPER_SECRET } from '@/utils/constants';

// Token响应接口
interface TokenResponse {
  data: {
    access_token: string;
    token_type: string;
  };
  message: string;
}

// 认证错误类
class AuthError extends Error {
  constructor(message: string, public status?: number) {
    super(message);
    this.name = 'AuthError';
  }
}

// JWT Token解析
function parseJWT(token: string): any {
  try {
    const base64Url = token.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(
      atob(base64)
        .split('')
        .map(function (c) {
          return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
        })
        .join('')
    );
    return JSON.parse(jsonPayload);
  } catch (error) {
    console.error('JWT解析失败:', error);
    return null;
  }
}

// 检查Token是否过期（提前5分钟过期）
function isTokenExpired(token: string): boolean {
  const tokenData = parseJWT(token);
  if (!tokenData || !tokenData.exp) {
    return true;
  }
  
  const currentTime = Math.floor(Date.now() / 1000);
  const bufferTime = 5 * 60; // 5分钟缓冲时间
  return (tokenData.exp - bufferTime) < currentTime;
}

/**
 * 自动认证服务类
 */
class AutoAuthService {
  private refreshPromise: Promise<string> | null = null;

  /**
   * 获取有效的access_token
   * 如果token不存在或即将过期，会自动刷新
   */
  async getValidToken(): Promise<string> {
    const currentToken = localStorage.getItem('access_token');
    
    // 检查当前token是否有效
    if (currentToken && !isTokenExpired(currentToken)) {
      return currentToken;
    }

    // 如果正在刷新，等待刷新完成
    if (this.refreshPromise) {
      return await this.refreshPromise;
    }

    // 开始刷新token
    this.refreshPromise = this.refreshToken();
    
    try {
      const newToken = await this.refreshPromise;
      return newToken;
    } finally {
      this.refreshPromise = null;
    }
  }

  /**
   * 使用开发者密钥获取新的access_token
   */
  private async refreshToken(): Promise<string> {
    if (!DEVELOPER_KEY || !DEVELOPER_SECRET) {
      throw new AuthError('开发者密钥未配置，请检查环境变量 VITE_DEVELOPER_KEY 和 VITE_DEVELOPER_SECRET');
    }

    console.log('🔄 使用开发者密钥获取access_token...');

    try {
      const response = await fetch(`${API_BASE_URL}/auth/token`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          api_key: DEVELOPER_KEY,
          api_secret: DEVELOPER_SECRET,
        }),
      });

      if (!response.ok) {
        const errorData = await response.text();
        console.error('认证失败:', response.status, errorData);
        throw new AuthError(`认证失败 (${response.status}): ${errorData}`, response.status);
      }

      const result: TokenResponse = await response.json();
      const newToken = result.data.access_token;

      // 保存新token
      localStorage.setItem('access_token', newToken);
      console.log('✅ 成功获取新的access_token');

      return newToken;
    } catch (error) {
      console.error('❌ 获取access_token失败:', error);
      
      if (error instanceof AuthError) {
        throw error;
      }
      
      throw new AuthError('网络错误，无法获取access_token');
    }
  }

  /**
   * 清除本地token
   */
  clearToken(): void {
    localStorage.removeItem('access_token');
    console.log('🗑️ 已清除本地access_token');
  }

  /**
   * 检查开发者密钥是否已配置
   */
  isDeveloperKeyConfigured(): boolean {
    return !!(DEVELOPER_KEY && DEVELOPER_SECRET);
  }

  /**
   * 获取当前token信息
   */
  getTokenInfo(): {
    hasToken: boolean;
    isValid: boolean;
    userInfo: any;
    expiresIn: number;
  } {
    const token = localStorage.getItem('access_token');
    
    if (!token) {
      return {
        hasToken: false,
        isValid: false,
        userInfo: null,
        expiresIn: 0,
      };
    }

    const tokenData = parseJWT(token);
    const expired = isTokenExpired(token);
    const currentTime = Math.floor(Date.now() / 1000);
    const expiresIn = tokenData?.exp ? Math.max(0, tokenData.exp - currentTime) : 0;

    return {
      hasToken: true,
      isValid: !expired,
      userInfo: tokenData,
      expiresIn,
    };
  }
}

// 导出单例实例
export const autoAuthService = new AutoAuthService();

// 导出类型和错误
export { AuthError };
export type { TokenResponse }; 