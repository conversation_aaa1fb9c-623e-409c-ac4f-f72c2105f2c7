// API模块统一导出
export { default as api } from './request';

// 项目相关
export { 
  projectApi, 
  type Project, 
  type CreateProjectRequest, 
  type UpdateProjectRequest, 
  type ProjectListResponse 
} from './project';

// 火车票订单相关
export { 
  trainOrderApi, 
  type TrainOrder, 
  type TrainOrderListResponse, 
  type ValidationError, 
  type ExcelValidationResponse, 
  type BookingRequest, 
  type BookingResponse 
} from './trainOrder';

// 护照识别相关
export { 
  passportApi, 
  type PassportRecord, 
  type TaskSummary, 
  type PassportListResponse, 
  type TaskListResponse, 
  type UploadResponse 
} from './passport';

// 酒店订单相关
export * from './hotel';

// 导入API模块用于统一导出
import { projectApi } from './project';
import { trainOrderApi } from './trainOrder';
import { passportApi } from './passport';

// 统一的API对象，便于使用
export const APIs = {
  project: projectApi,
  trainOrder: trainOrderApi,
  passport: passportApi,
};

export default APIs; 