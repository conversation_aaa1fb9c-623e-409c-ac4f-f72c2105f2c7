import api from './request';
import { checkAndHandleAuthError } from '@/utils/global-auth-handler';

// API调用包装函数，用于统一处理认证错误
async function apiCallWithAuthHandling<T>(apiCall: () => Promise<T>): Promise<T> {
  try {
    return await apiCall();
  } catch (error) {
    // 检查并处理认证错误
    if (checkAndHandleAuthError(error)) {
      // 如果是认证错误，重新抛出错误，但已经触发了全局处理
      throw new Error('认证已过期，请重新登录');
    }
    // 如果不是认证错误，直接重新抛出
    throw error;
  }
}

// 火车票订单相关类型定义
export interface TrainOrder {
  id: number;
  project_id: number;
  sequence_number: number;
  traveler_full_name: string;
  traveler_surname?: string;
  traveler_given_name?: string;
  nationality?: string;
  gender?: string;
  birth_date?: string;
  id_type?: string;
  id_number?: string;
  id_expiry_date?: string;
  mobile_phone?: string;
  mobile_phone_country_code: string;
  travel_date?: string;
  departure_station?: string;
  arrival_station?: string;
  train_number?: string;
  seat_type?: string;
  departure_time?: string;
  arrival_time?: string;
  cost_center?: string;
  trip_submission_item?: string;
  contact_person?: string;
  contact_phone?: string;
  contact_email?: string;
  approval_reference?: string;
  company_name?: string;
  booking_agent?: string;
  ticket_sms?: string;
  amount?: number;
  order_number?: string;
  bill_number?: string;
  order_status: string;
  fail_reason?: string;
  created_at: string;
  updated_at: string;
  is_deleted: boolean;
}

export interface TrainOrderListResponse {
  total: number;
  page: number;
  page_size: number;
  items: TrainOrder[];
}

export interface ValidationError {
  row: number;
  field: string;
  error_type: string;
  message: string;
  value?: string;
}

export interface ExcelValidationResponse {
  project_id: number;
  total_rows: number;
  valid_rows: number;
  error_rows: number;
  has_errors: boolean;
  errors: ValidationError[];
  message: string;
  can_proceed: boolean;
  file_path?: string;
}

export interface ExcelUploadResponse {
  project_id: number;
  total_orders: number;
  failed_orders: number;
  skipped_duplicate_orders: number;
  file_path?: string;
  message: string;
}

export interface BookingRequest {
  booking_type: 'book_only' | 'book_and_ticket';
  orders: number[];
  sms_notify?: boolean;
  has_agent?: boolean;
  agent_phone?: string | null;
}

export interface BookingResponse {
  processed_count: number;
  success_count: number;
  failed_count: number;
  message: string;
}

export interface ClearOrdersResponse {
  deleted_count: number;
  message: string;
}

// 新增：任务订单映射相关接口
export interface TaskToTrainOrder {
  id: number;
  project_id: number;
  task_id: string;
  order_id: number;
  order_status: string;
  start_time?: string;
  end_time?: string;
  message?: string;
  created_at: string;
  updated_at: string;
}

export interface TaskToTrainOrderCreate {
  project_id: number;
  task_id: string;
  order_id: number;
  order_status?: string;
  start_time?: string;
  end_time?: string;
  message?: string;
}

export interface TaskToTrainOrderUpdate {
  order_status?: string;
  start_time?: string;
  end_time?: string;
  message?: string;
}

export interface TaskToTrainOrderListResponse {
  mappings: TaskToTrainOrder[];
  total: number;
}

// 新增预订任务相关类型和接口
export interface CreateBookingTaskRequest {
  booking_type: 'book_only' | 'book_and_ticket';
  task_title: string;
  task_description?: string;
  sms_notify: boolean;
  has_agent: boolean;
  agent_phone?: string;
  order_ids?: number[]; // 指定要预订的订单ID列表，如果为undefined则预订所有initial状态的订单
}

export interface CreateBookingTaskResponse {
  task_id: string;
  project_id: number;
  task_title: string;
  submitted_orders_count: number;
  message: string;
}

// 新增：项目订单统计响应
export interface ProjectOrderStatsResponse {
  project_id: number;
  total_orders: number;
  completed_orders: number;
  completed_amount: string; // 后端序列化为字符串，避免前导0问题
}

// 新增：项目订单详细统计响应
export interface ProjectOrderDetailStatsResponse {
  project_id: number;
  total_orders: number;
  check_failed_orders: number;
  initial_orders: number;
  submitted_orders: number;
  processing_orders: number;
  completed_orders: number;
  failed_orders: number;
  completed_amount: string; // 后端序列化为字符串，避免前导0问题
  total_amount: string; // 后端序列化为字符串，避免前导0问题
}

// 火车票预订任务API
export const trainBookingTaskApi = {
  // 创建火车票预订任务
  createBookingTask: (projectId: string | number, data: CreateBookingTaskRequest) => 
    api.post<CreateBookingTaskResponse>(`/train-order/project/${projectId}/create-booking-task`, data),
};

// 火车票订单API接口
export const trainOrderApi = {
  // 获取项目订单统计
  getProjectStats: (projectId: string | number) => 
    apiCallWithAuthHandling(() => 
      api.get<ProjectOrderStatsResponse>(`/train-order/project/${projectId}/stats`)
    ),

  // 获取项目订单详细统计
  getProjectDetailStats: (projectId: string | number) => 
    apiCallWithAuthHandling(() => 
      api.get<ProjectOrderDetailStatsResponse>(`/train-order/project/${projectId}/detail-stats`)
    ),

  // 获取项目的火车票订单列表
  getOrdersByProject: (
    projectId: string | number, 
    page: number = 1, 
    pageSize: number = 20, 
    orderStatus?: string,
    travelerName?: string,
    mobilePhone?: string,
    contactPhone?: string,
    sortByFailedFirst: boolean = true
  ) => {
    const params = new URLSearchParams({
      page: page.toString(),
      page_size: pageSize.toString(),
    });
    
    if (orderStatus) {
      params.append('order_status', orderStatus);
    }
    
    if (travelerName) {
      params.append('traveler_name', travelerName);
    }
    
    if (mobilePhone) {
      params.append('mobile_phone', mobilePhone);
    }
    
    if (contactPhone) {
      params.append('contact_phone', contactPhone);
    }
    
    if (sortByFailedFirst) {
      params.append('sort_by_failed_first', 'true');
    }
    
    return apiCallWithAuthHandling(() => 
      api.get<TrainOrderListResponse>(`/train-order/project/${projectId}?${params}`)
    );
  },

  // 获取特定任务的火车票订单列表
  getOrdersByTask: (
    taskId: string, 
    page: number = 1, 
    pageSize: number = 20, 
    status?: string,
    travelerName?: string,
    mobilePhone?: string,
    contactPhone?: string
  ) => {
    const params = new URLSearchParams({
      page: page.toString(),
      page_size: pageSize.toString(),
    });
    
    if (status) {
      params.append('status', status);
    }
    
    if (travelerName) {
      params.append('traveler_name', travelerName);
    }
    
    if (mobilePhone) {
      params.append('mobile_phone', mobilePhone);
    }
    
    if (contactPhone) {
      params.append('contact_phone', contactPhone);
    }
    
    return api.get<TrainOrderListResponse>(`/train-order/task/${taskId}?${params}`);
  },

  // 获取单个订单详情
  getOrder: (orderId: number) => 
    api.get<TrainOrder>(`/train-order/${orderId}`),

  // 更新订单信息
  updateOrder: (orderId: number, data: Partial<TrainOrder>) => 
    api.put<TrainOrder>(`/train-order/${orderId}`, data),

  // 删除单个订单
  deleteOrder: (orderId: number) => 
    api.delete(`/train-order/${orderId}`),

  // Excel文件验证
  validateExcel: (projectId: string | number, file: File, smsNotify: boolean = false) => {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('project_id', projectId.toString());
    formData.append('sms_notify', smsNotify.toString());
    
    return api.upload<ExcelValidationResponse>('/train-order/validate-excel', formData);
  },

  // Excel文件上传导入
  uploadExcel: (projectId: string | number, file: File, smsNotify: boolean = false) => {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('project_id', projectId.toString());
    formData.append('sms_notify', smsNotify.toString());
    
    return api.upload<ExcelUploadResponse>('/train-order/upload-excel', formData);
  },

  // 预订订单
  bookOrders: (projectId: string | number, data: BookingRequest) => 
    api.post<BookingResponse>(`/train-order/project/${projectId}/book`, data),

  // 清空项目的待预订订单
  clearInitialOrders: (projectId: string | number) => {
    console.log('🚀 API调用: 清空项目订单, projectId:', projectId);
    return api.delete<ClearOrdersResponse>(`/train-order/project/${projectId}/clear-initial`);
  },
};

export default trainOrderApi;

// 扩展火车票订单API接口
export const trainOrderApiExtended = {
  ...trainOrderApi,
  
  // 创建预订任务
  createBookingTask: (projectId: string | number, data: CreateBookingTaskRequest) => 
    api.post<CreateBookingTaskResponse>(`/train-order/project/${projectId}/create-booking-task`, data),
}; 