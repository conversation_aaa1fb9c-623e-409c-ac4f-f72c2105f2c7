import React, { useState, useRef } from 'react';
import { buildImageUrl } from '@/utils/image';
import { ZoomIn, X, ZoomOut, RotateCcw, Copy, Check, CreditCard, User, Calendar, MapPin, Flag, FileText, Shield, Clock } from 'lucide-react';

interface PassportData {
  id: number;
  task_id: string;
  uploaded_image_url?: string;
  // 按指定顺序的字段
  certificate_type?: string;
  certificate_number?: string;
  surname?: string;
  given_names?: string;
  sex?: string;
  date_of_birth?: string;
  nationality?: string;
  passenger_type?: string;
  country_of_issue?: string;
  date_of_issue?: string;
  date_of_expiry?: string;
  ssr_code?: string;
  mrz_line1?: string;
  mrz_line2?: string;
  viz_mrz_consistency?: string;
  // 保留的原有字段
  document_type?: string;
  passport_number?: string;
  place_of_birth?: string;
  authority?: string;
  processing_status: string;
  created_at: string;
}

interface PassportImageProps {
  imageUrl: string;
  alt?: string;
  className?: string;
  fallbackText?: string;
  passportData?: PassportData;
  allPassports?: PassportData[];
  currentIndex?: number;
}

export const PassportImage: React.FC<PassportImageProps> = ({
  imageUrl,
  alt = "护照图片",
  className = "w-16 h-12 object-cover rounded border",
  fallbackText = "图片加载失败",
  passportData,
  allPassports,
  currentIndex,
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [scale, setScale] = useState(1);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [copiedField, setCopiedField] = useState<string | null>(null);
  const [currentViewIndex, setCurrentViewIndex] = useState<number>(0);
  
  const imageRef = useRef<HTMLImageElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // 获取当前显示的护照数据
  const getCurrentPassport = () => {
    if (!allPassports || !isModalOpen) return passportData;
    return allPassports[currentViewIndex] || passportData;
  };

  // 获取当前显示的图片URL
  const getCurrentImageUrl = () => {
    const currentPassport = getCurrentPassport();
    return currentPassport?.uploaded_image_url || imageUrl;
  };

  const fullImageUrl = buildImageUrl(getCurrentImageUrl());
  const currentPassportData = getCurrentPassport();
  
  // 获取字段图标
  const getFieldIcon = (key: string) => {
    const iconMap: Record<string, React.ReactNode> = {
      certificate_type: <CreditCard className="h-4 w-4" />,
      certificate_number: <CreditCard className="h-4 w-4" />,
      passport_number: <CreditCard className="h-4 w-4" />,
      surname: <User className="h-4 w-4" />,
      given_names: <User className="h-4 w-4" />,
      sex: <User className="h-4 w-4" />,
      date_of_birth: <Calendar className="h-4 w-4" />,
      nationality: <Flag className="h-4 w-4" />,
      passenger_type: <User className="h-4 w-4" />,
      country_of_issue: <Flag className="h-4 w-4" />,
      date_of_issue: <Calendar className="h-4 w-4" />,
      date_of_expiry: <Calendar className="h-4 w-4" />,
      ssr_code: <FileText className="h-4 w-4" />,
      mrz_line1: <FileText className="h-4 w-4" />,
      mrz_line2: <FileText className="h-4 w-4" />,
      viz_mrz_consistency: <Shield className="h-4 w-4" />,
      place_of_birth: <MapPin className="h-4 w-4" />,
      authority: <Shield className="h-4 w-4" />,
      task_id: <FileText className="h-4 w-4" />,
      processing_status: <Clock className="h-4 w-4" />,
      created_at: <Calendar className="h-4 w-4" />
    };
    return iconMap[key] || <FileText className="h-4 w-4" />;
  };
  
  const passportFields = currentPassportData ? [
    { label: '证件类型', value: currentPassportData.certificate_type, key: 'certificate_type' },
    { label: '证件号码', value: currentPassportData.certificate_number || currentPassportData.passport_number, key: 'certificate_number' },
    { label: '姓氏', value: currentPassportData.surname, key: 'surname' },
    { label: '名字', value: currentPassportData.given_names, key: 'given_names' },
    { label: '性别', value: currentPassportData.sex, key: 'sex' },
    { label: '出生日期', value: currentPassportData.date_of_birth, key: 'date_of_birth' },
    { label: '国籍', value: currentPassportData.nationality, key: 'nationality' },
    { label: '旅客类型', value: currentPassportData.passenger_type, key: 'passenger_type' },
    { label: '签发国', value: currentPassportData.country_of_issue, key: 'country_of_issue' },
    { label: '签发日期', value: currentPassportData.date_of_issue, key: 'date_of_issue' },
    { label: '有效期至', value: currentPassportData.date_of_expiry, key: 'date_of_expiry' },
    { label: 'SSR DOCS码', value: currentPassportData.ssr_code, key: 'ssr_code' },
    { label: 'MRZ第一行', value: currentPassportData.mrz_line1, key: 'mrz_line1' },
    { label: 'MRZ第二行', value: currentPassportData.mrz_line2, key: 'mrz_line2' },
    { label: '一致性检查', value: currentPassportData.viz_mrz_consistency, key: 'viz_mrz_consistency' }
  ].filter(field => field.value && field.value.trim() !== '') : [];

  const handleLoad = () => {
    setIsLoading(false);
    setHasError(false);
  };

  const handleError = () => {
    setIsLoading(false);
    setHasError(true);
    console.error('图片加载失败:', fullImageUrl);
  };

  const openModal = () => {
    if (!hasError) {
      setIsModalOpen(true);
      // 设置当前查看索引
      if (allPassports && currentIndex !== undefined) {
        setCurrentViewIndex(currentIndex);
      }
      // 重置缩放和位置
      setScale(1);
      setPosition({ x: 0, y: 0 });
    }
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setCopiedField(null);
  };

  // 缩放控制
  const handleZoomIn = () => {
    setScale(prev => Math.min(prev * 1.2, 3));
  };

  const handleZoomOut = () => {
    setScale(prev => Math.max(prev / 1.2, 0.5));
  };

  const handleResetZoom = () => {
    setScale(1);
    setPosition({ x: 0, y: 0 });
  };

  // 鼠标拖拽
  const handleMouseDown = (e: React.MouseEvent) => {
    if (e.button === 0) { // 左键
      e.preventDefault();
      setIsDragging(true);
      setDragStart({
        x: e.clientX - position.x,
        y: e.clientY - position.y
      });
    }
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (isDragging) {
      setPosition({
        x: e.clientX - dragStart.x,
        y: e.clientY - dragStart.y
      });
    }
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  // 滚轮缩放
  const handleWheel = (e: React.WheelEvent) => {
    e.preventDefault();
    const delta = e.deltaY > 0 ? 0.9 : 1.1;
    setScale(prev => Math.max(0.5, Math.min(3, prev * delta)));
  };

  // 复制到剪贴板
  const copyToClipboard = async (text: string, fieldName: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedField(fieldName);
      setTimeout(() => setCopiedField(null), 2000);
      
      // 显示浮动提示
      const toast = document.createElement('div');
      toast.className = 'fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg z-[60] flex items-center gap-2';
      toast.innerHTML = `
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
        </svg>
        <span>已复制到剪贴板</span>
      `;
      document.body.appendChild(toast);
      
      setTimeout(() => {
        toast.style.transform = 'translateX(100%)';
        toast.style.opacity = '0';
        setTimeout(() => document.body.removeChild(toast), 300);
      }, 2000);
      
    } catch (err) {
      console.error('复制失败:', err);
      
      // 显示错误提示
      const toast = document.createElement('div');
      toast.className = 'fixed top-4 right-4 bg-red-500 text-white px-4 py-2 rounded-lg shadow-lg z-[60] flex items-center gap-2';
      toast.innerHTML = `
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
        <span>复制失败</span>
      `;
      document.body.appendChild(toast);
      
      setTimeout(() => {
        toast.style.transform = 'translateX(100%)';
        toast.style.opacity = '0';
        setTimeout(() => document.body.removeChild(toast), 300);
      }, 2000);
    }
  };

  // 处理ESC键关闭模态框和全局鼠标事件
  React.useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        closeModal();
      } else if (event.key === '=' || event.key === '+') {
        event.preventDefault();
        handleZoomIn();
      } else if (event.key === '-' || event.key === '_') {
        event.preventDefault();
        handleZoomOut();
      } else if (event.key === 'r' || event.key === 'R') {
        event.preventDefault();
        handleResetZoom();
      } else if (event.key === 'ArrowUp' && allPassports && allPassports.length > 1) {
        event.preventDefault();
        if (currentViewIndex > 0) {
          setCurrentViewIndex(currentViewIndex - 1);
          // 重置图片状态
          setScale(1);
          setPosition({ x: 0, y: 0 });
        }
      } else if (event.key === 'ArrowDown' && allPassports && allPassports.length > 1) {
        event.preventDefault();
        if (currentViewIndex < allPassports.length - 1) {
          setCurrentViewIndex(currentViewIndex + 1);
          // 重置图片状态
          setScale(1);
          setPosition({ x: 0, y: 0 });
        }
      }
    };

    const handleGlobalMouseMove = (e: MouseEvent) => {
      if (isDragging) {
        setPosition({
          x: e.clientX - dragStart.x,
          y: e.clientY - dragStart.y
        });
      }
    };

    const handleGlobalMouseUp = () => {
      setIsDragging(false);
    };

    if (isModalOpen) {
      document.addEventListener('keydown', handleKeyDown);
      // 防止背景滚动
      document.body.style.overflow = 'hidden';
      
      // 添加全局鼠标事件监听
      if (isDragging) {
        document.addEventListener('mousemove', handleGlobalMouseMove);
        document.addEventListener('mouseup', handleGlobalMouseUp);
      }
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('mousemove', handleGlobalMouseMove);
      document.removeEventListener('mouseup', handleGlobalMouseUp);
      document.body.style.overflow = 'unset';
    };
  }, [isModalOpen, isDragging, dragStart, currentViewIndex, allPassports]);

  if (hasError) {
    return (
      <div className={`${className} bg-gray-100 flex items-center justify-center text-xs text-gray-500`}>
        {fallbackText}
      </div>
    );
  }

  return (
    <>
      <div className="relative group cursor-pointer" onClick={openModal}>
        {isLoading && (
          <div className={`${className} bg-gray-100 flex items-center justify-center`}>
            <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-gray-400"></div>
          </div>
        )}
        <img
          src={fullImageUrl}
          alt={alt}
          className={`${className} ${isLoading ? 'hidden' : ''} transition-opacity group-hover:opacity-80`}
          onLoad={handleLoad}
          onError={handleError}
          title={`点击查看大图 - ${fullImageUrl}`}
        />
        {/* 放大镜图标 */}
        {!isLoading && !hasError && (
          <div className="absolute inset-0 w-16 h-10 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity bg-black bg-opacity-20 rounded">
            <ZoomIn className="h-4 w-4 text-white" />
          </div>
        )}
      </div>

      {/* 护照放大查看模态框 */}
      {isModalOpen && (
        <div className="fixed inset-0 z-50 flex bg-black bg-opacity-90">
          {/* 左侧：护照图片查看区域 */}
          <div className="flex-1 flex items-center justify-center p-4 relative">
            {/* 工具栏 */}
            <div className="absolute top-4 left-4 z-10 flex items-center gap-2 bg-black bg-opacity-60 rounded-lg p-2 backdrop-blur-sm">
              <button
                onClick={(e) => { e.stopPropagation(); handleZoomIn(); }}
                className="p-2 text-white hover:bg-white hover:bg-opacity-20 rounded transition-colors"
                title="放大 (+)"
              >
                <ZoomIn className="h-4 w-4" />
              </button>
              <button
                onClick={(e) => { e.stopPropagation(); handleZoomOut(); }}
                className="p-2 text-white hover:bg-white hover:bg-opacity-20 rounded transition-colors"
                title="缩小 (-)"
              >
                <ZoomOut className="h-4 w-4" />
              </button>
              <button
                onClick={(e) => { e.stopPropagation(); handleResetZoom(); }}
                className="p-2 text-white hover:bg-white hover:bg-opacity-20 rounded transition-colors"
                title="重置 (R)"
              >
                <RotateCcw className="h-4 w-4" />
              </button>
              <div className="h-4 w-px bg-white bg-opacity-30 mx-1"></div>
              <span className="text-white text-sm px-2 font-medium">{Math.round(scale * 100)}%</span>
            </div>

            {/* 关闭按钮 */}
            <button
              onClick={closeModal}
              className="absolute top-4 right-4 z-10 p-2 bg-black bg-opacity-60 text-white rounded-full hover:bg-opacity-80 transition-colors backdrop-blur-sm"
              title="关闭 (ESC)"
            >
              <X className="h-5 w-5" />
            </button>
            
            {/* 护照图片容器 - 只显示护照本身 */}
            <div 
              ref={containerRef}
              className="relative w-full h-full flex items-center justify-center overflow-hidden cursor-move"
              onMouseDown={handleMouseDown}
              onMouseMove={handleMouseMove}
              onMouseUp={handleMouseUp}
              onWheel={handleWheel}
              onClick={(e) => e.stopPropagation()}
            >
              <img
                ref={imageRef}
                src={fullImageUrl}
                alt={alt}
                className="max-w-none select-none shadow-2xl rounded-lg"
                style={{
                  transform: `translate(${position.x}px, ${position.y}px) scale(${scale})`,
                  transition: isDragging ? 'none' : 'transform 0.2s ease-out',
                  maxHeight: '90vh',
                  maxWidth: '90%',
                  objectFit: 'contain'
                }}
                draggable={false}
              />
            </div>

            {/* 操作提示 */}
            <div className="absolute bottom-4 left-4 bg-black bg-opacity-60 text-white text-xs p-2 rounded-lg backdrop-blur-sm">
              <div className="space-y-1">
                <p>拖拽移动 • 滚轮缩放 • R重置 • ESC关闭</p>
              </div>
            </div>
          </div>

          {/* 右侧：护照信息面板 */}
          {currentPassportData && (
            <div className="w-[420px] bg-white h-full flex flex-col" onClick={(e) => e.stopPropagation()}>
              {/* 标题栏 */}
              <div className="p-4 border-b bg-gray-50 flex-shrink-0">
                <h3 className="text-lg font-semibold text-gray-800">护照信息</h3>
                <p className="text-xs text-gray-600 mt-1">点击复制图标可复制字段值</p>
              </div>
              
              {/* 信息内容 - 紧凑布局 */}
              <div className="flex-1 p-4 overflow-y-auto">
                {/* 护照信息 */}
                {passportFields.length > 0 ? (
                  <div className="space-y-2">
                    {passportFields.map((field) => {
                      // 判断是否为长文本字段，需要换行显示
                      const isLongTextField = ['ssr_code', 'mrz_line1', 'mrz_line2'].includes(field.key);
                      const isMonospaceField = ['mrz_line1', 'mrz_line2'].includes(field.key);
                      
                      if (isLongTextField) {
                        // 长文本字段使用垂直布局
                        return (
                          <div key={field.key} className="p-3 bg-gray-50 rounded hover:bg-gray-100 transition-colors">
                            <div className="flex items-center justify-between mb-2">
                              <div className="flex items-center">
                                <div className="text-gray-500 mr-2 flex-shrink-0">
                                  {getFieldIcon(field.key)}
                                </div>
                                <div className="text-sm font-medium text-gray-700">
                                  {field.label}:
                                </div>
                              </div>
                              <button
                                onClick={() => copyToClipboard(field.value || '', field.key)}
                                className="flex-shrink-0 p-1 rounded hover:bg-gray-200 transition-colors"
                                title={`复制：${field.value}`}
                              >
                                {copiedField === field.key ? (
                                  <Check className="h-3 w-3 text-green-600" />
                                ) : (
                                  <Copy className="h-3 w-3 text-gray-400" />
                                )}
                              </button>
                            </div>
                            {field.key === 'ssr_code' ? (
                              // SSR码使用水平滚动条
                              <div className="bg-white rounded border p-3 overflow-x-auto max-w-full min-h-[3rem]">
                                <div className="text-sm text-gray-900 whitespace-nowrap font-mono text-xs min-w-0 leading-relaxed py-1">
                                  {field.value}
                                </div>
                              </div>
                            ) : (
                              // MRZ字段使用换行显示
                              <div className={`text-sm text-gray-900 break-all leading-relaxed ${isMonospaceField ? 'font-mono text-xs' : ''}`}>
                                {field.value}
                              </div>
                            )}
                          </div>
                        );
                      } else {
                        // 普通字段使用水平布局
                        return (
                          <div key={field.key} className="flex items-center justify-between p-2 bg-gray-50 rounded hover:bg-gray-100 transition-colors">
                            <div className="flex items-center flex-1 min-w-0">
                              <div className="text-gray-500 mr-2 flex-shrink-0">
                                {getFieldIcon(field.key)}
                              </div>
                              <div className="text-sm font-medium text-gray-700 mr-2 flex-shrink-0 w-16">
                                {field.label}:
                              </div>
                              <div className="text-sm text-gray-900 truncate flex-1" title={field.value}>
                                {field.value}
                              </div>
                            </div>
                            <button
                              onClick={() => copyToClipboard(field.value || '', field.key)}
                              className="flex-shrink-0 p-1 rounded hover:bg-gray-200 transition-colors ml-2"
                              title={`复制：${field.value}`}
                            >
                              {copiedField === field.key ? (
                                <Check className="h-3 w-3 text-green-600" />
                              ) : (
                                <Copy className="h-3 w-3 text-gray-400" />
                              )}
                            </button>
                          </div>
                        );
                      }
                    })}
                  </div>
                ) : (
                  <div className="text-center py-4 text-gray-500 bg-gray-50 rounded">
                    <p className="text-sm">暂无可显示的护照字段</p>
                  </div>
                )}
                
                {/* 系统信息 */}
                <div className="border-t border-gray-200 pt-4 mt-4">
                  <h4 className="text-sm font-semibold text-gray-700 mb-3">系统信息</h4>
                  
                  <div className="space-y-2">
                    {/* 任务ID */}
                    <div className="p-3 bg-gray-50 rounded hover:bg-gray-100 transition-colors">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center">
                          <div className="text-gray-500 mr-2 flex-shrink-0">
                            {getFieldIcon('task_id')}
                          </div>
                          <div className="text-sm font-medium text-gray-700">
                            任务ID:
                          </div>
                        </div>
                        <button
                          onClick={() => copyToClipboard(currentPassportData.task_id, 'task_id')}
                          className="flex-shrink-0 p-1 rounded hover:bg-gray-200 transition-colors"
                          title={`复制：${currentPassportData.task_id}`}
                        >
                          {copiedField === 'task_id' ? (
                            <Check className="h-3 w-3 text-green-600" />
                          ) : (
                            <Copy className="h-3 w-3 text-gray-400" />
                          )}
                        </button>
                      </div>
                      <div className="text-xs font-mono text-gray-900 break-all leading-relaxed">
                        {currentPassportData.task_id}
                      </div>
                    </div>
                    
                    {/* 处理状态 */}
                    <div className="flex items-center justify-between p-2 bg-gray-50 rounded hover:bg-gray-100 transition-colors">
                      <div className="flex items-center flex-1 min-w-0">
                        <div className="text-gray-500 mr-2 flex-shrink-0">
                          {getFieldIcon('processing_status')}
                        </div>
                        <div className="text-sm font-medium text-gray-700 mr-2 flex-shrink-0 w-16">
                          状态:
                        </div>
                        <span className={`inline-block px-2 py-1 rounded text-xs font-medium ${
                          currentPassportData.processing_status === 'completed' ? 'bg-green-100 text-green-800' :
                          currentPassportData.processing_status === 'processing' ? 'bg-blue-100 text-blue-800' :
                          currentPassportData.processing_status === 'failed' ? 'bg-red-100 text-red-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {currentPassportData.processing_status === 'pending' ? '待处理' :
                           currentPassportData.processing_status === 'processing' ? '处理中' :
                           currentPassportData.processing_status === 'completed' ? '已完成' :
                           currentPassportData.processing_status === 'failed' ? '失败' : currentPassportData.processing_status}
                        </span>
                      </div>
                      <button
                        onClick={() => copyToClipboard(
                          currentPassportData.processing_status === 'pending' ? '待处理' :
                          currentPassportData.processing_status === 'processing' ? '处理中' :
                          currentPassportData.processing_status === 'completed' ? '已完成' :
                          currentPassportData.processing_status === 'failed' ? '失败' : currentPassportData.processing_status,
                          'processing_status'
                        )}
                        className="flex-shrink-0 p-1 rounded hover:bg-gray-200 transition-colors ml-2"
                      >
                        {copiedField === 'processing_status' ? (
                          <Check className="h-3 w-3 text-green-600" />
                        ) : (
                          <Copy className="h-3 w-3 text-gray-400" />
                        )}
                      </button>
                    </div>
                    
                    {/* 上传时间 */}
                    <div className="flex items-center justify-between p-2 bg-gray-50 rounded hover:bg-gray-100 transition-colors">
                      <div className="flex items-center flex-1 min-w-0">
                        <div className="text-gray-500 mr-2 flex-shrink-0">
                          {getFieldIcon('created_at')}
                        </div>
                        <div className="text-sm font-medium text-gray-700 mr-2 flex-shrink-0 w-16">
                          时间:
                        </div>
                        <div className="text-sm text-gray-900 truncate flex-1" title={new Date(currentPassportData.created_at).toLocaleString('zh-CN')}>
                          {new Date(currentPassportData.created_at).toLocaleString('zh-CN')}
                        </div>
                      </div>
                      <button
                        onClick={() => copyToClipboard(new Date(currentPassportData.created_at).toLocaleString('zh-CN'), 'created_at')}
                        className="flex-shrink-0 p-1 rounded hover:bg-gray-200 transition-colors ml-2"
                      >
                        {copiedField === 'created_at' ? (
                          <Check className="h-3 w-3 text-green-600" />
                        ) : (
                          <Copy className="h-3 w-3 text-gray-400" />
                        )}
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      )}
    </>
  );
}; 