import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useParams } from 'react-router-dom';
import { 
  Upload, 
  FileSpreadsheet, 
  Download, 
  Building, 
  Trash2, 
  Eye, 
  FileText,
  Send,
  AlertTriangle,
  Search,
  X,
  Check,
  RotateCcw,
  PenTool,
  Save,
  Edit,
  Info
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import * as XLSX from 'xlsx-js-style';

// 使用酒店订单API模块
import { 
  hotelOrderApi, 
  type HotelOrder, 
  type ExcelValidationResponse,
  type CreateBookingTaskRequest,
  type HotelOrderUpdate
} from '@/api/hotel';
import { ProjectTaskService } from '@/services/project/projectTaskService';

// Tab类型定义
type TabType = 'input' | 'text';

interface HotelBookingContentProps {
  onNavigateToAllOrders?: () => void;
}

// 将验证函数移到组件外部，避免重复创建
  const getFieldValidationRule = (fieldName: string) => {
    const validationRules: Record<string, any> = {
      // 基础必填字段（所有情况都必填）
      guest_full_name: { required: true, maxLength: 50 },
      guest_id_type: { required: true, enumValues: ['身份证', '公务护照', '普通护照', '港澳通行证', '台胞证', '回乡证', '军人证', '海员证', '台湾通行证', '外国永久居留证', '港澳台居民居住证', '其他'] },
      guest_id_number: { required: true, pattern: /^[0-9Xx]{15,18}$/, message: '证件号码格式不正确（15-18位数字或字母X）', maxLength: 18 },
      guest_mobile_phone: { required: true, pattern: /^1[3-9]\d{9}$/, message: '手机号格式不正确' },
      check_in_time: { required: true },
      check_out_time: { required: true },
    hotel_id: { required: true, maxLength: 100 },
    room_type: { required: true, maxLength: 100 },
      cost_center: { required: true, maxLength: 100 },
      contact_person: { required: true, maxLength: 50 },
      contact_mobile_phone: { required: true, pattern: /^1[3-9]\d{9}$/, message: '联系人手机号格式不正确' },
      approver: { required: true, maxLength: 50 },

      // 动态必填字段（证件类型不为身份证时必填）
      guest_surname: { maxLength: 50 },
      guest_given_name: { maxLength: 50 },
      guest_nationality: { maxLength: 50 },
      guest_gender: { enumValues: ['男', '女'] },
      guest_birth_date: {},
      guest_id_expiry_date: {},

      // 其他字段
      guest_mobile_country_code: { maxLength: 10 },
      guest_email: { pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/, message: '邮箱格式不正确' },
      destination: { maxLength: 100 },
      hotel_name: { maxLength: 200 },
      contact_mobile_country_code: { maxLength: 10 },
      company_name: { maxLength: 200 },
      booking_agent: { maxLength: 50 },
      order_number: { maxLength: 100 },
      bill_number: { maxLength: 100 },
      amount: { type: 'number', min: 0 },
      include_breakfast: { enumValues: ['是', '否'] },
      is_half_day_room: { enumValues: ['是', '否'] },
    is_group_booking: { enumValues: ['是', '否'] },
    is_violation: { enumValues: ['是', '否'] }
    };
    
    return validationRules[fieldName] || {};
  };

  // 验证单个字段 - 参考火车票的实现
  const validateField = (fieldName: keyof HotelOrder, value: any, idType?: string): string | null => {
    const rule = getFieldValidationRule(fieldName);
    const stringValue = String(value || '').trim();
    
    // 获取字段的中文显示名称
    const fieldDisplayNames: Record<string, string> = {
      guest_full_name: '入住人姓名',
      guest_surname: '入住人姓',
      guest_given_name: '入住人名',
      guest_nationality: '入住人国籍',
      guest_gender: '入住人性别',
      guest_birth_date: '入住人出生日期',
      guest_id_type: '入住人证件类型',
      guest_id_number: '入住人证件号码',
      guest_id_expiry_date: '入住人证件有效期至',
      guest_mobile_country_code: '入住人手机号国际区号',
      guest_mobile_phone: '入住人手机号',
      guest_email: '入住人邮箱',
      check_in_time: '入住时间',
      check_out_time: '离店时间',
    hotel_id: '酒店ID',
      cost_center: '成本中心',
      contact_person: '联系人姓名',
      contact_mobile_phone: '联系人手机号',
      approver: '审批人',
      destination: '目的地',
      hotel_name: '酒店名称',
      room_type: '房间类型',
      amount: '金额'
    };
    
    const fieldDisplayName = fieldDisplayNames[fieldName] || fieldName;
    
    // 检查动态必填字段（证件类型不为身份证时必填）
    const dynamicRequiredFields = ['guest_surname', 'guest_given_name', 'guest_nationality', 'guest_gender', 'guest_birth_date', 'guest_id_expiry_date'];
    const isDynamicRequired = dynamicRequiredFields.includes(fieldName) && idType && idType !== '身份证';
    
    // 必填验证
    if ((rule.required || isDynamicRequired) && !stringValue) {
      return `${fieldDisplayName}不能为空`;
    }
    
    // 如果字段为空且不是必填的，跳过其他验证
    if (!stringValue && !rule.required && !isDynamicRequired) {
      return null;
    }
    
    // 长度验证
    if (rule.maxLength && stringValue.length > rule.maxLength) {
      return `${fieldDisplayName}长度不能超过${rule.maxLength}个字符`;
    }
    
    // 枚举值验证
    if (rule.enumValues && stringValue && !rule.enumValues.includes(stringValue)) {
      return `${fieldDisplayName}必须是以下值之一：${rule.enumValues.join('、')}`;
    }
    
    // 正则表达式验证
    if (rule.pattern && stringValue && !rule.pattern.test(stringValue)) {
      return rule.message || `${fieldDisplayName}格式不正确`;
    }
    
    // 数字验证
    if (rule.type === 'number' && stringValue) {
      const numValue = parseFloat(stringValue);
      if (isNaN(numValue)) {
        return `${fieldDisplayName}必须是有效的数字`;
      }
      if (rule.min !== undefined && numValue < rule.min) {
        return `${fieldDisplayName}不能小于${rule.min}`;
      }
    }
    
    return null;
  };

  // 验证所有字段 - 参考火车票的实现
  const validateAllFields = (order: HotelOrder): Record<string, string> => {
    const errors: Record<string, string> = {};
    
    // 基础必填字段
    const requiredFields = [
      'guest_full_name', 'guest_id_type', 'guest_id_number', 'guest_mobile_phone',
    'check_in_time', 'check_out_time', 'hotel_id', 'room_type', 'cost_center', 
    'contact_person', 'contact_mobile_phone', 'approver'
    ];
    
    // 验证基础必填字段
    for (const field of requiredFields) {
      const error = validateField(field as keyof HotelOrder, (order as any)[field], order.guest_id_type);
      if (error) {
        errors[field] = error;
      }
    }
    
    // 动态必填字段（证件类型不为身份证时必填）
    if (order.guest_id_type && order.guest_id_type !== '身份证') {
      const dynamicRequiredFields = ['guest_surname', 'guest_given_name', 'guest_nationality', 'guest_gender', 'guest_birth_date', 'guest_id_expiry_date'];
      for (const field of dynamicRequiredFields) {
        const error = validateField(field as keyof HotelOrder, (order as any)[field], order.guest_id_type);
        if (error) {
          errors[field] = error;
        }
      }
    }
    
    return errors;
  };

// 将EditableCell组件移到组件外部并优化
interface EditableCellProps {
  value: string | number | boolean | null | undefined;
  field: keyof HotelOrder;
  type?: 'text' | 'number' | 'date' | 'datetime-local' | 'email';
  isEditing: boolean;
  onFieldChange: (field: keyof HotelOrder, value: string | number | boolean) => void;
  validationErrors?: Record<string, string>;
  editingOrder?: HotelOrder | null;
}

const EditableCell = React.memo<EditableCellProps>(({ 
  value, 
  field, 
  type = 'text', 
  isEditing, 
  onFieldChange, 
  validationErrors, 
  editingOrder 
}) => {
  const [localError, setLocalError] = useState<string | null>(null);
  
  const hasError = localError || validationErrors?.[field];
  
  const handleChange = useCallback((e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    let newValue: string | number | boolean = e.target.value;
    if (type === 'number') {
      newValue = parseFloat(e.target.value) || 0;
    }
    
    // 实时验证，传递证件类型信息
    const error = validateField(field, newValue, editingOrder?.guest_id_type);
    setLocalError(error);
    
    onFieldChange(field, newValue);
  }, [field, type, onFieldChange]);

  const handleBlur = useCallback(() => {
    // 失焦时再次验证，传递证件类型信息
    const error = validateField(field, value, editingOrder?.guest_id_type);
    setLocalError(error);
  }, [field, value]);
  
  const rule = getFieldValidationRule(field);
  
  // 检查是否为选择字段
  const isSelectField = rule.enumValues && Array.isArray(rule.enumValues);

  if (!isEditing) {
    // 只读模式，直接使用传入的value
    let displayValue = value;
    
    // 特殊格式化处理
    if (field === 'amount' && value) {
      displayValue = `¥${value}`;
    } else if (field === 'include_breakfast' || field === 'is_half_day_room' || field === 'is_group_booking' || field === 'is_violation') {
      // 这些字段现在是字符串类型，但需要处理旧数据（"1"/"0"或布尔值）
      if (value === "1" || value === "是" || value === true) {
        displayValue = "是";
      } else if (value === "0" || value === "否" || value === false) {
        displayValue = "否";
      } else {
        displayValue = value || '否';
      }
    } else if (!value) {
      displayValue = '-';
    }
    
    return (
      <span className="text-sm text-gray-900">
        {String(displayValue)}
      </span>
    );
  }

  // 编辑模式
  if (isSelectField) {
    return (
      <div className="w-full">
        <select
          value={String(value || '')}
          onChange={handleChange}
          onBlur={handleBlur}
          className={`w-full px-2 py-1 border rounded text-sm focus:ring-2 focus:border-transparent ${
            hasError 
              ? 'border-red-300 focus:ring-red-200' 
              : 'border-gray-300 focus:ring-blue-200'
          }`}
        >
          <option value="">请选择</option>
          {rule.enumValues?.map((optionValue: string) => (
            <option key={optionValue} value={optionValue}>
              {optionValue}
            </option>
          ))}
        </select>
        {hasError && (
          <p className="mt-1 text-xs text-red-600">{hasError}</p>
        )}
      </div>
    );
  }

  if (field === 'order_remarks' || field === 'trip_submission_item') {
    return (
      <div className="w-full">
        <textarea
          value={String(value || '')}
          onChange={handleChange}
          onBlur={handleBlur}
          rows={2}
          className={`w-full px-2 py-1 border rounded text-sm focus:ring-2 focus:border-transparent resize-none ${
            hasError 
              ? 'border-red-300 focus:ring-red-200' 
              : 'border-gray-300 focus:ring-blue-200'
          }`}
          placeholder={rule.message}
        />
        {hasError && (
          <p className="mt-1 text-xs text-red-600">{hasError}</p>
        )}
      </div>
    );
  }

  // 普通input字段
  return (
    <div className="w-full">
      <input
        type={type}
        value={String(value || '')}
        onChange={handleChange}
        onBlur={handleBlur}
        className={`w-full px-2 py-1 border rounded text-sm focus:ring-2 focus:border-transparent ${
          hasError 
            ? 'border-red-300 focus:ring-red-200' 
            : 'border-gray-300 focus:ring-blue-200'
        }`}
        placeholder={rule.message}
      />
      {hasError && (
        <p className="mt-1 text-xs text-red-600">{hasError}</p>
      )}
    </div>
  );
});

const HotelBookingContent: React.FC<HotelBookingContentProps> = ({ onNavigateToAllOrders }) => {
  const { projectId } = useParams<{ projectId: string }>();
  const { toast } = useToast();
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 专门用于格式化日期的函数，确保显示为YYYY-MM-DD格式
  // 格式化是否字段显示（处理旧数据"1"/"0"格式）
  const formatYesNoDisplay = (value: any): string => {
    if (value === "1" || value === "是") return "是";
    if (value === "0" || value === "否") return "否";
    return value || '否';
  };

  const formatDateForDisplay = (dateValue: any): string => {
    if (!dateValue) return '-';
    
    const dateStr = String(dateValue);
    
    // 如果已经是YYYY-MM-DD格式，直接返回
    if (/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) {
      return dateStr;
    }
    
    // 如果是YYYY_MM_DD格式，转换为YYYY-MM-DD
    if (/^\d{4}_\d{2}_\d{2}$/.test(dateStr)) {
      return dateStr.replace(/_/g, '-');
    }
    
    // 尝试解析其他日期格式并转换为YYYY-MM-DD
    try {
      // 替换常见分隔符为标准分隔符
      let normalizedDate = dateStr.replace(/[/._]/g, '-');
      
      // 尝试解析日期
      const date = new Date(normalizedDate);
      if (!isNaN(date.getTime())) {
        const year = date.getFullYear();
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const day = date.getDate().toString().padStart(2, '0');
        return `${year}-${month}-${day}`;
      }
    } catch (error) {
      console.warn('日期格式解析失败:', dateStr);
    }
    
    // 如果解析失败，返回原值
    return dateStr;
  };

  const [loading, setLoading] = useState(true);
  const [orders, setOrders] = useState<HotelOrder[]>([]);
  const [activeTab, setActiveTab] = useState<TabType>('input');
  const [textInput, setTextInput] = useState('');
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);

  // 详情弹窗状态
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState<HotelOrder | null>(null);
  
  // 编辑模式状态
  const [isEditMode, setIsEditMode] = useState(false);
  const [editingOrder, setEditingOrder] = useState<HotelOrder | null>(null);
  const [saving, setSaving] = useState(false);
  
  // 添加验证错误状态
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

  // 删除操作状态管理
  const [deletingOrderId, setDeletingOrderId] = useState<number | null>(null);
  
  // 分页状态
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(20);
  const [totalCount, setTotalCount] = useState(0);
  const [pageLoading, setPageLoading] = useState(false);

  // 状态筛选
  const [selectedStatuses, setSelectedStatuses] = useState<string[]>(['check_failed', 'initial', 'failed']);

  // 搜索状态
  const [searchGuestName, setSearchGuestName] = useState('');
  const [searchMobilePhone, setSearchMobilePhone] = useState('');
  const [searchContactPhone, setSearchContactPhone] = useState('');

  // 验证弹框状态
  const [showValidationDialog, setShowValidationDialog] = useState(false);
  const [validationDialogData, setValidationDialogData] = useState<ExcelValidationResponse | null>(null);

  // 自定义确认弹框状态
  const [showGeneralConfirmDialog, setShowGeneralConfirmDialog] = useState(false);
  const [generalConfirmDialogData, setGeneralConfirmDialogData] = useState<{
    title: string;
    message: string;
    confirmText?: string;
    cancelText?: string;
    type?: 'info' | 'warning' | 'danger';
    onConfirm: () => void | Promise<void>;
  } | null>(null);

  // 添加确认对话框状态 - 用于显示验证失败和预定失败的订单
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [confirmDialogData, setConfirmDialogData] = useState<{
    title: string;
    checkFailedOrders: HotelOrder[];
    bookingFailedOrders: HotelOrder[];
    onConfirm?: () => void | Promise<void>;
  } | null>(null);

  // 文件上传相关状态
  const [validating, setValidating] = useState(false);
  const [uploading, setUploading] = useState(false);
  
  // 预订设置状态
  const [smsNotify, setSmsNotify] = useState(false);
  const [hasAgent, setHasAgent] = useState(false);
  const [agentPhone, setAgentPhone] = useState('');
  const [settingsSource, setSettingsSource] = useState<'project_history' | 'localStorage' | 'default'>('default');
  
  // 暂时让TypeScript知道这个变量会被使用（将来可能显示设置来源信息）
  void settingsSource;
  
  // 清空订单状态
  const [clearingOrders, setClearingOrders] = useState(false);

  // 导出状态
  const [exporting, setExporting] = useState(false);

  // 从localStorage读取全局设置的fallback函数
  const getLocalStorageSettings = () => {
    try {
      const data = localStorage.getItem('hotel_booking_settings');
      if (data) {
        return JSON.parse(data);
      }
    } catch {}
    return {};
  };

  // 获取项目的历史酒店任务设置，优先级：项目历史 > localStorage > 默认值
  const getProjectHotelBookingSettings = async (projectId: string) => {
    try {
      // 首先尝试从项目历史酒店任务获取设置
      const projectSettings = await ProjectTaskService.getProjectLatestSettings(parseInt(projectId), '酒店预订');
      
      if (projectSettings.has_history) {
        console.log('🔍 已获取项目酒店预订历史任务设置:', projectSettings.message);
        return {
          smsNotify: projectSettings.sms_notify,
          hasAgent: projectSettings.has_agent,
          agentPhone: projectSettings.agent_phone || '',
          source: 'project_history',
          lastTaskTitle: projectSettings.last_task_title
        };
      }
    } catch (error) {
      console.warn('获取项目酒店预订历史设置失败，将使用localStorage设置:', error);
    }
    
    // 如果项目没有酒店预订历史任务，fallback到localStorage
    const localSettings = getLocalStorageSettings();
    return {
      smsNotify: localSettings.smsNotify ?? false,
      hasAgent: localSettings.hasAgent ?? false,
      agentPhone: localSettings.agentPhone ?? '',
      source: 'localStorage'
    };
  };

  // 加载项目设置的函数
  const loadProjectSettings = async () => {
    if (!projectId) return;
    
    try {
      const settings = await getProjectHotelBookingSettings(projectId);
      setSmsNotify(settings.smsNotify);
      setHasAgent(settings.hasAgent);
      setAgentPhone(settings.agentPhone);
      setSettingsSource(settings.source as 'project_history' | 'localStorage');
      
      if (settings.source === 'project_history' && settings.lastTaskTitle) {
        console.log(`✅ 已加载项目酒店预订历史设置（来自任务：${settings.lastTaskTitle}）`);
      } else if (settings.source === 'localStorage') {
        console.log('📱 已加载localStorage设置');
      }
    } catch (error) {
      console.warn('加载项目酒店预订设置失败，使用默认值:', error);
      // 如果出错，使用默认值
      setSmsNotify(false);
      setHasAgent(false);
      setAgentPhone('');
      setSettingsSource('default');
    }
  };

  // 在项目ID可用时加载项目设置
  useEffect(() => {
    if (projectId) {
      loadProjectSettings();
    }
  }, [projectId]);

  // 初始化数据加载
  useEffect(() => {
    if (projectId) {
      loadProjectData();
      loadHotelOrders(1);
    }
  }, [projectId]);

  // 通用确认对话框函数
  const showGeneralConfirm = (data: {
    title: string;
    message: string;
    confirmText?: string;
    cancelText?: string;
    type?: 'info' | 'warning' | 'danger';
    onConfirm: () => void | Promise<void>;
  }) => {
    setGeneralConfirmDialogData(data);
    setShowGeneralConfirmDialog(true);
  };

  // 字段变更处理 - 使用useCallback和空依赖数组优化避免重新渲染
  const handleFieldChange = useCallback((field: keyof HotelOrder, value: string | number | boolean) => {
    setEditingOrder(prev => {
      if (!prev) return prev;
      
      // 更新字段值
      const updated = { ...prev, [field]: value };
      
      // 实时验证当前字段，传递证件类型用于动态必填判断
      const error = validateField(field, value, updated.guest_id_type);
      setValidationErrors(prevErrors => {
        const newErrors = { ...prevErrors };
        if (error) {
          newErrors[field] = error;
        } else {
          delete newErrors[field];
        }
        return newErrors;
      });
      
      return updated;
    });
  }, []); // 空依赖数组，使用函数式更新避免闭包问题

  // 加载项目数据
  const loadProjectData = async () => {
    if (!projectId) return;
    
    try {
      // 项目信息暂时不需要在酒店预订页面中使用
      // 如果将来需要显示项目信息，可以在这里重新添加
      console.log('项目ID:', projectId);
    } catch (error) {
      console.error('获取项目信息失败:', error);
      toast({
        title: "错误", 
        description: "获取项目信息失败",
        variant: "destructive"
      });
    }
  };

  // 加载酒店订单数据
  const loadHotelOrders = async (page: number) => {
    if (!projectId) return;

    setPageLoading(true);
    try {
      const statusFilter = selectedStatuses.length > 0 ? selectedStatuses.join(',') : undefined;
      
      const response = await hotelOrderApi.getProjectOrders(
        parseInt(projectId),
        page,
        pageSize,
        statusFilter,
        searchGuestName || undefined,
        searchMobilePhone || undefined,
        searchContactPhone || undefined,
        true // sortByFailedFirst
      );

      setOrders(response.items);
      setTotalCount(response.total);
      setCurrentPage(page);
    } catch (error) {
      console.error('获取酒店订单失败:', error);
      toast({
        title: "错误",
        description: "获取酒店订单失败",
        variant: "destructive"
      });
    } finally {
      setPageLoading(false);
      setLoading(false);
    }
  };

  // 搜索处理
  const handleSearch = () => {
    loadHotelOrders(1);
  };

  const handleClearSearch = () => {
    setSearchGuestName('');
    setSearchMobilePhone('');
    setSearchContactPhone('');
    setSelectedStatuses(['check_failed', 'initial', 'submitted', 'processing', 'completed', 'failed']);
    loadHotelOrders(1);
  };

  // 状态筛选处理
  const handleStatusChange = (status: string, checked: boolean) => {
    setSelectedStatuses(prev => 
      checked 
        ? [...prev, status]
        : prev.filter(s => s !== status)
    );
  };

  // 状态筛选变化时自动重新加载数据
  useEffect(() => {
    if (!loading) {
      loadHotelOrders(1);
    }
  }, [selectedStatuses]);

  // 处理编辑订单 - 直接打开编辑模式弹窗
  const handleEditOrder = (order: HotelOrder) => {
    setSelectedOrder(order);
    setEditingOrder({ ...order });
    setIsEditMode(true);
    setValidationErrors({}); // 清空验证错误，确保编辑模式干净状态
    setShowDetailModal(true); // 显示编辑模态框
  };

  // 删除单个订单
  const handleDeleteOrder = async (orderId: number, orderName: string) => {
    // 使用自定义确认对话框替换系统默认confirm
    showGeneralConfirm({
      title: '确认删除订单',
      message: `确定要删除 ${orderName} 的订单吗？删除后将从数据库中永久移除，无法恢复。`,
      confirmText: '确认删除',
      cancelText: '取消',
      type: 'danger',
      onConfirm: async () => {
        setDeletingOrderId(orderId);
        try {
          await hotelOrderApi.deleteOrder(orderId);
          
          toast({
            title: "删除成功",
            description: `已成功删除 ${orderName} 的订单`,
          });
          
          await loadHotelOrders(currentPage);
        } catch (error) {
          console.error('删除订单失败:', error);
          toast({
            title: "删除失败",
            description: "删除订单时发生错误，请重试",
            variant: "destructive"
          });
        } finally {
          setDeletingOrderId(null);
        }
      }
    });
  };

  // 保存编辑的订单 - 参考火车票的实现
  const saveChanges = async () => {
    if (!editingOrder || !selectedOrder || saving) return;

    // 保存前进行完整验证
    const errors = validateAllFields(editingOrder);
    setValidationErrors(errors);
    
    if (Object.keys(errors).length > 0) {
      toast({
        title: "验证失败",
        description: `请修正 ${Object.keys(errors).length} 个数据错误后再保存`,
        variant: "destructive",
      });
      return;
    }

    setSaving(true);
    try {
      // 准备更新数据，将字符串类型的布尔值转换为实际布尔值
      const updateData: HotelOrderUpdate = {
        guest_full_name: editingOrder.guest_full_name,
        guest_surname: editingOrder.guest_surname,
        guest_given_name: editingOrder.guest_given_name,
        guest_nationality: editingOrder.guest_nationality,
        guest_gender: editingOrder.guest_gender,
        guest_birth_date: editingOrder.guest_birth_date,
        guest_id_type: editingOrder.guest_id_type,
        guest_id_number: editingOrder.guest_id_number,
        guest_id_expiry_date: editingOrder.guest_id_expiry_date,
        guest_mobile_country_code: editingOrder.guest_mobile_country_code,
        guest_mobile_phone: editingOrder.guest_mobile_phone,
        guest_email: editingOrder.guest_email,
        destination: editingOrder.destination,
        hotel_name: editingOrder.hotel_name,
        room_type: editingOrder.room_type,
        room_count: editingOrder.room_count,
        include_breakfast: editingOrder.include_breakfast as string,
        is_half_day_room: editingOrder.is_half_day_room as string,
        check_in_time: editingOrder.check_in_time,
        check_out_time: editingOrder.check_out_time,
        is_group_booking: editingOrder.is_group_booking as string,
        group_booking_name: editingOrder.group_booking_name,
        room_number: editingOrder.room_number,
        cost_center: editingOrder.cost_center,
        trip_submission_item: editingOrder.trip_submission_item,
        contact_person: editingOrder.contact_person,
        contact_mobile_country_code: editingOrder.contact_mobile_country_code,
        contact_mobile_phone: editingOrder.contact_mobile_phone,
        order_remarks: editingOrder.order_remarks,
        approver: editingOrder.approver,
                 company_name: editingOrder.company_name,
         booking_agent: editingOrder.booking_agent,
         amount: editingOrder.amount || undefined,
         order_number: editingOrder.order_number,
         bill_number: editingOrder.bill_number
      };

      const response = await hotelOrderApi.updateOrder(selectedOrder.id, updateData);

      // 根据订单状态显示不同的提示信息
      if (response.order_status === 'check_failed') {
        toast({
          title: "保存完成",
          description: `${response.guest_full_name} 的订单信息已保存，但验证失败：${response.fail_reason || '请检查数据格式'}`,
          variant: "destructive",
        });
      } else {
        // 验证通过的情况，只显示保存成功，不提及状态变更
        toast({
          title: "保存成功",
          description: `${response.guest_full_name} 的订单信息已成功更新`,
          variant: "default",
        });
      }
      
      setShowDetailModal(false);
      setSelectedOrder(null);
      setEditingOrder(null);
      setValidationErrors({});
      setIsEditMode(false);
      
      // 刷新订单列表
      await loadHotelOrders(currentPage);
      
    } catch (error) {
      console.error('保存订单失败:', error);
      toast({
        title: "保存失败",
        description: "保存订单时发生错误，请重试",
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setUploadedFile(file);
    }
  };

  const removeUploadedFile = () => {
    setUploadedFile(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleValidateAndUpload = async () => {
    if (!uploadedFile) return;

    try {
      setValidating(true);
      const validationResponse = await hotelOrderApi.validateExcel(Number(projectId), uploadedFile);

      // 显示验证结果
      setValidationDialogData(validationResponse);
      setShowValidationDialog(true);

    } catch (error) {
      console.error('Excel处理失败:', error);
      toast({
        title: "处理失败",
        description: "Excel文件处理失败，请检查文件格式",
        variant: "destructive",
      });
    } finally {
      setValidating(false);
    }
  };

  const handleValidationConfirm = async () => {
    if (!validationDialogData || !uploadedFile) return;
    
    try {
      setUploading(true);
      const response = await hotelOrderApi.uploadExcel(Number(projectId), uploadedFile);
      
      // 显示导入结果，包括验证失败的记录
      toast({
        title: "导入完成",
        description: response.message,
      });

      // 重新加载订单列表
      loadHotelOrders(1);
      
      // 清理上传状态
      setUploadedFile(null);
      setShowValidationDialog(false);
      setValidationDialogData(null);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    } catch (error: any) {
      console.error('上传失败:', error);
      // 尝试从错误响应中获取详细信息
      let errorMessage = "Excel文件上传失败";
      if (error.response?.data?.detail) {
        errorMessage = error.response.data.detail;
      } else if (error.message) {
        errorMessage = error.message;
      }
      
      toast({
        title: "上传失败",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setUploading(false);
    }
  };

  const handleValidationCancel = () => {
    setShowValidationDialog(false);
    setValidationDialogData(null);
  };

  const handleClearOrders = async () => {
    // 使用自定义确认对话框替换系统默认confirm
    showGeneralConfirm({
      title: '确认清空订单',
      message: `确定要清空所有酒店订单吗？删除后将从数据库中永久移除，无法恢复。`,
      confirmText: '确认清空',
      cancelText: '取消',
      type: 'danger',
      onConfirm: async () => {
        try {
          setClearingOrders(true);
          await hotelOrderApi.clearOrders(Number(projectId));
          
          toast({
            title: "清空成功",
            description: "所有酒店订单已清空",
          });

          loadHotelOrders(1);
        } catch (error) {
          console.error('清空订单失败:', error);
          toast({
            title: "清空失败",
            description: "清空订单失败",
            variant: "destructive",
          });
        } finally {
          setClearingOrders(false);
        }
      }
    });
  };

  const handleGenerateOrder = async () => {
    // 使用当前页面显示的订单
    const currentPageOrders = orders;
    
    if (currentPageOrders.length === 0) {
      toast({
        title: "提示",
        description: "当前页面暂无订单数据",
        variant: "default",
      });
      return;
    }

    // 检查当前页面的订单中是否有验证失败或预定失败的订单
    const checkFailedOrders = currentPageOrders.filter(order => order.order_status === 'check_failed');
    const bookingFailedOrders = currentPageOrders.filter(order => order.order_status === 'failed');
    
    if (checkFailedOrders.length > 0 || bookingFailedOrders.length > 0) {
      // 使用自定义对话框提示用户处理失败订单
      setConfirmDialogData({
        title: '无法提交预订',
        checkFailedOrders,
        bookingFailedOrders
      });
      setShowConfirmDialog(true);
      return;
    }

    // 使用通用确认对话框
    showGeneralConfirm({
      title: '确认预订',
      message: `确定要预订当前页面的 ${currentPageOrders.length} 条酒店订单吗？`,
      confirmText: '确认预订',
      cancelText: '取消',
      type: 'info',
      onConfirm: async () => {
    try {
      const taskData: CreateBookingTaskRequest = {
            booking_type: 'book_only',
            task_title: `酒店预订 - ${new Date().toLocaleDateString()}`,
            task_description: `预订 ${currentPageOrders.length} 条酒店订单`,
        sms_notify: smsNotify,
        has_agent: hasAgent,
        agent_phone: hasAgent ? agentPhone : undefined,
            order_ids: currentPageOrders.map(order => order.id) // 传递当前页面订单的ID列表
      };

      const response = await hotelOrderApi.createBookingTask(Number(projectId), taskData);
      
      toast({
        title: "预订成功",
        description: `已创建预订任务，任务ID: ${response.task_id}`,
      });

      // 预订成功后跳转到所有订单tab
      if (onNavigateToAllOrders) {
        setTimeout(() => {
          onNavigateToAllOrders();
        }, 1000);
      }

      loadHotelOrders(1);
    } catch (error) {
      console.error('创建预订任务失败:', error);
      toast({
        title: "预订失败",
        description: "创建预订任务失败",
        variant: "destructive",
      });
    }
      }
    });
  };

  const handleSubmitOrder = async () => {
    // 使用当前页面显示的订单
    const currentPageOrders = orders;
    
    if (currentPageOrders.length === 0) {
      toast({
        title: "提示",
        description: "当前页面暂无订单数据",
        variant: "default",
      });
      return;
    }

    // 检查当前页面的订单中是否有验证失败或预定失败的订单
    const checkFailedOrders = currentPageOrders.filter(order => order.order_status === 'check_failed');
    const bookingFailedOrders = currentPageOrders.filter(order => order.order_status === 'failed');
    
    if (checkFailedOrders.length > 0 || bookingFailedOrders.length > 0) {
      // 使用自定义对话框提示用户处理失败订单
      setConfirmDialogData({
        title: '无法提交预订',
        checkFailedOrders,
        bookingFailedOrders
      });
      setShowConfirmDialog(true);
      return;
    }

    // 使用通用确认对话框
    showGeneralConfirm({
      title: '确认预订且提交行程',
      message: `确定要预订且提交行程当前页面的 ${currentPageOrders.length} 条酒店订单吗？`,
      confirmText: '确认预订且提交行程',
      cancelText: '取消',
      type: 'warning',
      onConfirm: async () => {
    try {
      const taskData: CreateBookingTaskRequest = {
            booking_type: 'book_and_ticket',
            task_title: `酒店预订且提交行程 - ${new Date().toLocaleDateString()}`,
            task_description: `预订且提交行程 ${currentPageOrders.length} 条酒店订单`,
        sms_notify: smsNotify,
        has_agent: hasAgent,
        agent_phone: hasAgent ? agentPhone : undefined,
            order_ids: currentPageOrders.map(order => order.id) // 传递当前页面订单的ID列表
      };

      const response = await hotelOrderApi.createBookingTask(Number(projectId), taskData);
      
      toast({
        title: "预订且提交行程成功",
        description: `已创建预订任务，任务ID: ${response.task_id}`,
      });

      // 预订成功后跳转到所有订单tab
      if (onNavigateToAllOrders) {
        setTimeout(() => {
          onNavigateToAllOrders();
        }, 1000);
      }

      loadHotelOrders(1);
    } catch (error) {
      console.error('创建预订任务失败:', error);
      toast({
        title: "预订失败",
        description: "创建预订任务失败",
        variant: "destructive",
      });
    }
      }
    });
  };

  const openDetailModal = (order: HotelOrder) => {
    setSelectedOrder(order);
    setEditingOrder({ ...order });
    setIsEditMode(false);
    setValidationErrors({});
    setShowDetailModal(true);
  };

  const getOrderStatusDisplay = (status: string) => {
    const statusConfig = {
      'check_failed': { text: '验证失败', color: 'bg-red-100 text-red-700' },
      'initial': { text: '待提交', color: 'bg-gray-100 text-gray-600' },
      'submitted': { text: '已提交', color: 'bg-blue-100 text-blue-700' },
      'processing': { text: '处理中', color: 'bg-cyan-100 text-cyan-700' },
      'completed': { text: '已完成', color: 'bg-green-100 text-green-700' },
      'failed': { text: '预定失败', color: 'bg-red-100 text-red-700' }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || { text: status, color: 'bg-gray-100 text-gray-600' };
    
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        {config.text}
      </span>
    );
  };

  const handleDownloadTemplate = () => {
    // 创建酒店订单Excel模板
    const templateData = [
      {
        '入住人姓名': '张三',
        '入住人姓': '张',
        '入住人名': '三',
        '国籍': '中国',
        '性别': '男',
        '出生日期': '1990-01-01',
        '证件类型': '身份证',
        '证件号码': '110101199001011234',
        '证件有效期': '2030-01-01',
        '手机号': '13800138000',
        '邮箱': '<EMAIL>',
        '目的地': '北京',
        '酒店ID': 'HOTEL001',
        '房间类型': '标准间',
        '房间数': '1',
        '政策名称': '标准政策',
        '含早餐': '是',
        '半日房': '否',
        '入住时间': '2024-01-01 14:00',
        '退房时间': '2024-01-02 12:00',
        '团体预订': '否',
        '团体预订名称': '',
        '房间号': '',
        '支付方式': '信用卡',
        '发票类型': '电子发票',
        '税率': '0.06',
        '协议类型': '标准协议',
        '供应商名称': '某酒店集团',
        '支付渠道': '在线支付',
        '支付交易号': '',
        '单房价格': '300',
        '隐藏服务费': '0',
        '取消政策': '免费取消',
        '违规标记': '否',
        '联系人': '李四',
        '联系人手机号': '13900139000',
        '订单备注': '',
        '成本中心': 'CC001',
        '行程提交项': '出差',
        '审批人': '王五',
        '同住人姓名': '',
        '同住人姓': '',
        '同住人名': '',
        '同住人国籍': '',
        '同住人性别': '',
        '同住人出生日期': '',
        '同住人证件类型': '',
        '同住人证件号码': '',
        '同住人证件有效期': '',
        '同住人手机号': '',
        '同住人邮箱': '',
        '公司名称': '某某公司',
        '酒店名称': '某某酒店',
        '金额': '300',
        '预订代理': '某代理',
        '订单号': 'ORDER001',
        '账单号': 'BILL001'
      }
    ];

    const wb = XLSX.utils.book_new();
    const ws = XLSX.utils.json_to_sheet(templateData);
    XLSX.utils.book_append_sheet(wb, ws, '酒店订单模板');
    XLSX.writeFile(wb, '酒店订单导入模板.xlsx');
  };

  const handleTextParse = () => {
    toast({
      title: "功能开发中",
      description: "文本解析功能正在开发中",
    });
  };



  // 智能导出（带进度和大数据优化）
  const handleExportExcel = async () => {
    setExporting(true);
    try {
      // 获取当前筛选条件下的所有订单数据
      let allOrders: any[] = [];
      let currentPage = 1;
      let hasMore = true;
      const pageSize = 100; // 每次获取100条

      while (hasMore) {
        const response = await hotelOrderApi.getProjectOrders(
          Number(projectId),
          currentPage,
          pageSize,
          selectedStatuses.join(','),
          searchGuestName || undefined,
          searchMobilePhone || undefined,
          undefined,
          false
        );

        allOrders = [...allOrders, ...response.items];
        hasMore = response.items.length === pageSize;
        currentPage++;
      }

      if (allOrders.length === 0) {
        toast({
          title: "无数据",
          description: "当前筛选条件下暂无订单数据可导出",
          variant: "destructive",
        });
        return;
      }

      // 准备导出数据 - 包含所有核心字段
      const exportData = allOrders.map((order, index) => ({
        '序号': index + 1,
        '订单状态': getOrderStatusText(order.order_status),
        '入住人姓名': order.guest_full_name || '-',
        '入住人姓': order.guest_surname || '-',
        '入住人名': order.guest_given_name || '-',
        '国籍': order.guest_nationality || '-',
        '性别': order.guest_gender || '-',
        '出生日期': formatDateForDisplay(order.guest_birth_date),
        '证件类型': order.guest_id_type || '-',
        '证件号码': order.guest_id_number || '-',
        '证件有效期至': formatDateForDisplay(order.guest_id_expiry_date),
        '手机号国际区号': order.guest_mobile_country_code || '-',
        '手机号': order.guest_mobile_phone || '-',
        '邮箱': order.guest_email || '-',
        '目的地': order.destination || '-',
        '酒店名称': order.hotel_name || '-',
        '房间类型': order.room_type || '-',
        '房间数量': order.room_count || '-',
        '含早餐': formatYesNoDisplay(order.include_breakfast),
        '入住时间': order.check_in_time || '-',
        '离店时间': order.check_out_time || '-',
        '团体预订': formatYesNoDisplay(order.is_group_booking),
        '团体预订名称': order.group_booking_name || '-',
        '联系人': order.contact_person || '-',
        '联系人手机号国际区号': order.contact_mobile_country_code || '-',
        '联系人手机号': order.contact_mobile_phone || '-',
        '订单备注': order.order_remarks || '-',
        '成本中心': order.cost_center || '-',
        '行程提交项': order.trip_submission_item || '-',
        '审批人': order.approver || '-',
        '同住人姓名': order.roommate_name || '-',
        '同住人姓': order.roommate_surname || '-',
        '同住人名': order.roommate_given_name || '-',
        '同住人国籍': order.roommate_nationality || '-',
        '同住人性别': order.roommate_gender || '-',
        '同住人出生日期': formatDateForDisplay(order.roommate_birth_date),
        '同住人证件类型': order.roommate_id_type || '-',
        '同住人证件号码': order.roommate_id_number || '-',
        '同住人证件有效期至': formatDateForDisplay(order.roommate_id_expiry_date),
        '同住人手机号国际区号': order.roommate_mobile_country_code || '-',
        '同住人手机号': order.roommate_mobile_phone || '-',
        '同住人邮箱': order.roommate_email || '-',
        '公司名称': order.company_name || '-',
        '代订人': order.booking_agent || '-',
        '订单号': order.order_number || '-',
        '账单号': order.bill_number || '-',
        '金额': order.amount || 0,
        '失败原因': order.fail_reason || '-',
        '创建时间': formatDateTime(order.created_at),
        '更新时间': formatDateTime(order.updated_at)
      }));

      // 获取列名
      const headers = Object.keys(exportData[0] || {});
      
      // 创建空工作表
      const ws = XLSX.utils.aoa_to_sheet([]);
      
      // 添加标题行
      const title = `项目${projectId} - 酒店订单明细`;
      
      // 第一行：标题
      XLSX.utils.sheet_add_aoa(ws, [[title]], { origin: 'A1' });
      
      // 合并标题行单元格
      ws['!merges'] = [{ s: { r: 0, c: 0 }, e: { r: 0, c: headers.length - 1 } }];
      
      // 第二行：列名
      XLSX.utils.sheet_add_aoa(ws, [headers], { origin: 'A2' });
      
      // 第三行开始：数据
      const dataRows = exportData.map(row => headers.map(header => (row as any)[header]));
      XLSX.utils.sheet_add_aoa(ws, dataRows, { origin: 'A3' });

      // 设置列宽
      const colWidths = headers.map(() => ({ wch: 15 })); // 统一列宽
      ws['!cols'] = colWidths;

      // 获取工作表范围
      const range = XLSX.utils.decode_range(ws['!ref'] || 'A1');
      
      // 定义完整的边框样式
      const borderAll = {
        style: 'thin',
        color: { rgb: '000000' }
      };
      
      // 为所有单元格添加边框和样式
      for (let row = range.s.r; row <= range.e.r; row++) {
        for (let col = range.s.c; col <= range.e.c; col++) {
          const cellAddress = XLSX.utils.encode_cell({ r: row, c: col });
          if (!ws[cellAddress]) ws[cellAddress] = { t: 's', v: '' };
          
          // 设置单元格样式
          ws[cellAddress].s = {
            border: {
              top: borderAll,
              bottom: borderAll,
              left: borderAll,
              right: borderAll
            },
            alignment: { 
              horizontal: 'left', 
              vertical: 'center',
              wrapText: true 
            },
            font: { 
              name: '微软雅黑', 
              sz: 10 
            }
          };
          
          // 为标题行添加特殊样式
          if (row === 0) {
            ws[cellAddress].s.fill = {
              patternType: 'solid',
              fgColor: { rgb: 'E3F2FD' }
            };
            ws[cellAddress].s.font = {
              name: '微软雅黑',
              sz: 14,
              bold: true
            };
            ws[cellAddress].s.alignment = {
              horizontal: 'center',
              vertical: 'center',
              wrapText: true
            };
          }
          
          // 为列名行添加特殊样式
          else if (row === 1) {
            ws[cellAddress].s.fill = {
              patternType: 'solid',
              fgColor: { rgb: 'F3F4F6' }
            };
            ws[cellAddress].s.font = {
              name: '微软雅黑',
              sz: 10,
              bold: true
            };
            ws[cellAddress].s.alignment = {
              horizontal: 'center',
              vertical: 'center',
              wrapText: true
            };
          }
        }
      }

      // 创建工作簿
      const wb = XLSX.utils.book_new();

      // 添加工作表
      XLSX.utils.book_append_sheet(wb, ws, '酒店订单');
      
      // 生成文件名
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-').split('T')[0];
      const fileName = `酒店订单_项目${projectId}_${timestamp}.xlsx`;

      // 导出文件
      XLSX.writeFile(wb, fileName);

      toast({
        title: "导出成功",
        description: `成功导出 ${allOrders.length} 条筛选后的订单数据`,
        variant: "default",
      });
      
    } catch (error) {
      console.error('导出Excel失败:', error);
      toast({
        title: "导出失败",
        description: "导出Excel文件时发生错误，请重试",
        variant: "destructive",
      });
    } finally {
      setExporting(false);
    }
  };



  // 获取订单状态文本
  const getOrderStatusText = (status: string) => {
    switch (status) {
      case 'initial':
        return '待提交';
      case 'submitted':
        return '已提交';
      case 'processing':
        return '处理中';
      case 'completed':
        return '已完成';
      case 'failed':
        return '预定失败';
      case 'check_failed':
        return '验证失败';
      default:
        return status;
    }
  };

  // 格式化日期时间
  const formatDateTime = (dateString: string) => {
    if (!dateString) return '-';
    try {
      const date = new Date(dateString);
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
    } catch {
      return dateString;
    }
  };

  // 渲染数据录入tab内容
  const renderInputTab = () => (
    <div className="p-6">
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 items-center">
          {/* Left side: Upload area */}
          <div className="space-y-4">
            {!uploadedFile ? (
              <div 
                className="relative flex flex-col items-center justify-center p-6 border-2 border-dashed border-gray-300 rounded-lg hover:border-blue-500 transition-colors cursor-pointer group"
                onClick={() => fileInputRef.current?.click()}
              >
                <Upload className="h-8 w-8 text-gray-400 mb-2 transition-colors group-hover:text-blue-500" />
                <p className="text-sm font-medium text-gray-700 transition-colors group-hover:text-blue-600">点击或拖拽文件到此区域</p>
                <p className="text-xs text-gray-500 mt-1">支持 .xlsx, .xls</p>
              </div>
            ) : (
              <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3 overflow-hidden">
                    <FileSpreadsheet className="h-8 w-8 text-blue-600 flex-shrink-0" />
                    <div className="overflow-hidden">
                      <p className="text-sm font-medium text-gray-800 truncate" title={uploadedFile.name}>{uploadedFile.name}</p>
                      <p className="text-xs text-gray-500">{(uploadedFile.size / 1024).toFixed(2)} KB</p>
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="text-gray-500 hover:text-red-500 flex-shrink-0"
                    onClick={removeUploadedFile}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            )}
          </div>
          
          {/* Right side: Actions and info */}
          <div className="space-y-4">
            <div>
              <h4 className="font-semibold text-gray-800">上传说明</h4>
              <ul className="list-disc list-inside text-sm text-gray-600 mt-2 space-y-1">
                <li>请确保文件格式与模板一致。</li>
                <li>系统将验证数据有效性，重复数据将被忽略。</li>
                <li>
                  <Button 
                    variant="link" 
                    size="sm" 
                    className="p-0 h-auto text-blue-600 hover:text-blue-700"
                    onClick={handleDownloadTemplate}
                  >
                    <Download className="h-3 w-3 mr-1" />
                    下载Excel模板
                  </Button>
                </li>
              </ul>
            </div>
            
            <Button
              onClick={handleValidateAndUpload}
              disabled={!uploadedFile || validating || uploading}
              className="w-full bg-blue-600 hover:bg-blue-700 text-white"
            >
              {validating ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  验证中...
                </>
              ) : uploading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  上传中...
                </>
              ) : (
                <>
                  <Upload className="h-4 w-4 mr-2" />
                  验证并上传
                </>
              )}
            </Button>
          </div>
        </div>
      </div>

      {/* 文件输入 */}
      <input
        ref={fileInputRef}
        type="file"
        accept=".xlsx,.xls"
        onChange={handleFileUpload}
        className="hidden"
      />
    </div>
  );

  // 渲染文本内容tab内容
  const renderTextTab = () => (
    <div className="p-6">
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Left side: Text area */}
          <div className="h-full flex flex-col">
            <Label htmlFor="text-input" className="mb-2 font-medium text-gray-700">粘贴文本内容</Label>
            <Textarea
              id="text-input"
              placeholder="粘贴酒店预订信息，系统将自动识别入住人姓名、联系方式、酒店信息等。"
              className="flex-1 bg-white border-gray-300 resize-none min-h-[150px]"
              value={textInput}
              onChange={(e) => setTextInput(e.target.value)}
            />
          </div>
          
          {/* Right side: Actions and info */}
          <div className="space-y-4 flex flex-col justify-between">
            <div>
              <h4 className="font-semibold text-gray-800">文本解析说明</h4>
              <ul className="list-disc list-inside text-sm text-gray-600 mt-2 space-y-1">
                <li>支持自动识别入住人姓名、联系方式、酒店信息等。</li>
                <li>请确保每条记录信息完整，并用换行分隔。</li>
                <li>解析结果将显示在下方的订单列表中。</li>
              </ul>
            </div>
            
            <Button
              onClick={handleTextParse}
              disabled={!textInput.trim()}
              className="w-full bg-green-600 hover:bg-green-700 text-white"
            >
              <FileText className="h-4 w-4 mr-2" />
              解析文本
            </Button>
          </div>
        </div>
      </div>
    </div>
  );

  // 渲染订单列表
  const renderOrderList = () => (
    <Card>
      <CardHeader>
        <div className="space-y-4">
          {/* 第一行：标题和操作按钮 */}
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2 text-lg font-medium">
              <Building className="h-5 w-5 text-blue-600" />
              <span>酒店订单列表</span>
              {totalCount > 0 && (
                <span className="bg-blue-100 text-blue-800 text-xs font-medium px-2 py-0.5 rounded-full ml-1">
                  {totalCount}
                </span>
              )}
            </CardTitle>
            
            {/* 右侧操作按钮 */}
            <div className="flex items-center gap-2">
              <Button 
                variant="outline" 
                size="sm"
                onClick={handleClearOrders}
                disabled={clearingOrders || orders.length === 0}
                className="text-red-600 border-red-200 hover:bg-red-50"
              >
                {clearingOrders ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-600 mr-2"></div>
                    清空中...
                  </>
                ) : (
                  <>
                    <Trash2 className="h-4 w-4 mr-2" />
                    清空订单
                  </>
                )}
              </Button>
              <Button 
                variant="outline" 
                size="sm"
                onClick={handleGenerateOrder}
              >
                <FileText className="h-4 w-4 mr-2" />
                预订
              </Button>
              <Button 
                size="sm"
                onClick={handleSubmitOrder}
                className="bg-blue-600 hover:bg-blue-700 text-white"
              >
                <Send className="h-4 w-4 mr-2" />
                预订且提交行程
              </Button>
            </div>
          </div>
          
          {/* 第二行：短信通知和代订人设置 */}
          <div className="flex items-center gap-6 text-sm">
            {/* 短信通知 */}
            <div className="flex items-center gap-2">
              <Checkbox 
                id="sms-notify"
                checked={smsNotify}
                onCheckedChange={(checked) => setSmsNotify(!!checked)}
              />
              <Label htmlFor="sms-notify" className="text-gray-700">短信通知</Label>
            </div>
            
            {/* 代订人设置 */}
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <Checkbox 
                  id="has-agent"
                  checked={hasAgent}
                  onCheckedChange={(checked) => setHasAgent(!!checked)}
                />
                <Label htmlFor="has-agent" className="text-gray-700">有代订人</Label>
              </div>
              
              {hasAgent && (
                <div className="flex items-center gap-2">
                  <Label className="text-gray-600 text-xs">代订人手机：</Label>
                  <input
                    type="text"
                    placeholder="代订人手机号"
                    value={agentPhone}
                    onChange={(e) => setAgentPhone(e.target.value)}
                    className="h-7 px-2 border border-gray-300 rounded text-xs w-32 focus:outline-none focus:ring-1 focus:ring-blue-500"
                  />
                </div>
              )}
            </div>
          </div>
          
          {/* 第三行：搜索和筛选 */}
          <div className="flex flex-col md:flex-row md:items-center gap-3 pt-2 border-t border-gray-100">
            <div className="flex items-center gap-2 flex-1 max-w-xs">
              <label className="text-sm text-gray-600 whitespace-nowrap min-w-fit">入住人姓名：</label>
              <input
                type="text"
                placeholder="请输入入住人姓名"
                value={searchGuestName}
                onChange={(e) => setSearchGuestName(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                className="flex-1 h-9 px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
              />
            </div>
            
            <div className="flex items-center gap-2 flex-1 max-w-xs">
              <label className="text-sm text-gray-600 whitespace-nowrap min-w-fit">手机号码：</label>
              <input
                type="text"
                placeholder="请输入手机号码"
                value={searchMobilePhone}
                onChange={(e) => setSearchMobilePhone(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                className="flex-1 h-9 px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
              />
            </div>
            
            <div className="flex items-center gap-2 ml-auto">
              <Button
                onClick={handleSearch}
                size="sm"
                className="bg-blue-600 hover:bg-blue-700 text-white"
              >
                <Search className="h-4 w-4 mr-1" />
                搜索
              </Button>
              <Button
                onClick={handleClearSearch}
                variant="outline"
                size="sm"
                className="text-gray-600"
              >
                <RotateCcw className="h-4 w-4 mr-1" />
                重置
              </Button>
                <Button
                onClick={handleExportExcel}
                  variant="outline"
                  size="sm"
                  className="text-gray-600"
                  disabled={exporting}
                >
                  {exporting ? (
                    <>
                      <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-gray-600 mr-1"></div>
                    导出中...
                    </>
                  ) : (
                    <>
                      <FileSpreadsheet className="h-4 w-4 mr-1" />
                      导出
                    </>
                  )}
                </Button>
            </div>
          </div>
          
          {/* 第四行：状态筛选 */}
          <div className="flex flex-wrap items-center gap-4 pt-2 border-t border-gray-100">
            <span className="text-sm text-gray-600 min-w-fit">状态筛选：</span>
            
            {[
              { value: 'check_failed', label: '验证失败', color: 'text-red-600' },
              { value: 'initial', label: '待提交', color: 'text-gray-600' },
              { value: 'failed', label: '预定失败', color: 'text-red-600' }
            ].map((status) => (
              <div key={status.value} className="flex items-center gap-1.5">
                <Checkbox
                  id={`status-${status.value}`}
                  checked={selectedStatuses.includes(status.value)}
                  onCheckedChange={(checked) => handleStatusChange(status.value, !!checked)}
                />
                <Label 
                  htmlFor={`status-${status.value}`} 
                  className={`text-xs font-medium cursor-pointer ${status.color}`}
                >
                  {status.label}
                </Label>
              </div>
            ))}
          </div>
        </div>
      </CardHeader>

      <CardContent className="p-0">
        {pageLoading ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">加载中...</p>
          </div>
        ) : orders.length > 0 ? (
          <>
            <div className="overflow-x-auto">
              <table className="w-full text-xs" style={{ minWidth: '3000px' }}>
                <thead className="bg-gray-50 border-b border-gray-200">
                  <tr>
                    <th className="text-center p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '60px' }}>序号</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>状态</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '150px' }}>错误信息</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>入住人姓名</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>入住人姓</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>入住人名</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>国籍</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '60px' }}>性别</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>出生日期</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>证件类型</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '150px' }}>证件号码</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>证件有效期至</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '120px' }}>手机号</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '150px' }}>邮箱</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>目的地</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>酒店ID</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '150px' }}>酒店名称</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>房间类型</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>房间数</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>政策名称</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>含早餐</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>半日房</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '120px' }}>入住时间</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '120px' }}>退房时间</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>团体预订</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '120px' }}>团体预订名称</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>房间号</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>支付方式</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>发票类型</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>税率</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>协议类型</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '120px' }}>供应商名称</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>支付渠道</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '150px' }}>支付交易号</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>单房价格</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>隐藏服务费</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '120px' }}>取消政策</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>违规标记</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>联系人</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '120px' }}>联系人手机号</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '150px' }}>订单备注</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>成本中心</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '120px' }}>行程提交项</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>审批人</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>同住人姓名</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>同住人姓</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>同住人名</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>同住人国籍</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>同住人性别</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>同住人出生日期</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>同住人证件类型</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '150px' }}>同住人证件号码</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '120px' }}>同住人证件有效期至</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '120px' }}>同住人手机号</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '150px' }}>同住人邮箱</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '150px' }}>公司名称</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>金额</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '120px' }}>创建时间</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '120px' }}>更新时间</th>
                    <th className="text-center p-2 font-medium text-gray-900 text-xs whitespace-nowrap sticky right-0 bg-gray-50 border-l border-gray-200" style={{ minWidth: '100px' }}>操作</th>
                  </tr>
                </thead>
                <tbody>
                  {orders.map((order, index) => (
                    <tr key={order.id} className={`border-b border-gray-100 ${order.order_status === 'check_failed' || order.order_status === 'failed' ? 'bg-red-50 hover:bg-red-100' : 'hover:bg-gray-50'}`}>
                      <td className="p-2 text-xs text-gray-900 whitespace-nowrap text-center">{(currentPage - 1) * pageSize + index + 1}</td>
                      <td className="p-2 text-xs whitespace-nowrap">{getOrderStatusDisplay(order.order_status)}</td>
                      <td className="p-2 text-xs whitespace-nowrap relative group cursor-help">
                        {order.fail_reason ? (
                          <>
                            <span className={`${order.fail_reason.length > 15 ? 'border-b border-dotted border-gray-400' : ''}`}>
                              {order.fail_reason.length > 15 ? order.fail_reason.substring(0, 15) + '...' : order.fail_reason}
                            </span>
                            {order.fail_reason.length > 15 && (
                              <div className="invisible group-hover:visible absolute top-full mt-1 left-0 bg-gray-800 text-white text-xs rounded px-2 py-1 whitespace-normal max-w-xs z-50">
                                {order.fail_reason}
                              </div>
                            )}
                          </>
                        ) : (
                          '-'
                        )}
                      </td>
                      <td className="p-2 text-xs font-medium text-gray-900 whitespace-nowrap">{order.guest_full_name || '-'}</td>
                      <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.guest_surname || '-'}</td>
                      <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.guest_given_name || '-'}</td>
                      <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.guest_nationality || '-'}</td>
                      <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.guest_gender || '-'}</td>
                      <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{formatDateForDisplay(order.guest_birth_date)}</td>
                      <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.guest_id_type || '-'}</td>
                      <td className="p-2 text-xs text-gray-600 font-mono whitespace-nowrap">{order.guest_id_number || '-'}</td>
                      <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{formatDateForDisplay(order.guest_id_expiry_date)}</td>
                      <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.guest_mobile_phone || '-'}</td>
                      <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.guest_email || '-'}</td>
                      <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.destination || '-'}</td>
                      <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.hotel_id || '-'}</td>
                      <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.hotel_name || '-'}</td>
                      <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.room_type || '-'}</td>
                      <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.room_count || '-'}</td>
                      <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.policy_name || '-'}</td>
                                      <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{formatYesNoDisplay(order.include_breakfast)}</td>
                <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{formatYesNoDisplay(order.is_half_day_room)}</td>
                <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.check_in_time || '-'}</td>
                <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.check_out_time || '-'}</td>
                <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{formatYesNoDisplay(order.is_group_booking)}</td>
                      <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.group_booking_name || '-'}</td>
                      <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.room_number || '-'}</td>
                      <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.payment_method || '-'}</td>
                      <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.invoice_type || '-'}</td>
                      <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.tax_rate || '-'}</td>
                      <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.agreement_type || '-'}</td>
                      <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.supplier_name || '-'}</td>
                      <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.payment_channel || '-'}</td>
                      <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.payment_transaction_id || '-'}</td>
                      <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.cost_per_room ? `¥${order.cost_per_room}` : '-'}</td>
                      <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.hidden_service_fee ? `¥${order.hidden_service_fee}` : '-'}</td>
                      <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.cancellation_policy || '-'}</td>
                      <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.is_violation ? '是' : '否'}</td>
                      <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.contact_person || '-'}</td>
                      <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.contact_mobile_phone || '-'}</td>
                      <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.order_remarks || '-'}</td>
                      <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.cost_center || '-'}</td>
                      <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.trip_submission_item || '-'}</td>
                      <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.approver || '-'}</td>
                      <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.roommate_name || '-'}</td>
                      <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.roommate_surname || '-'}</td>
                      <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.roommate_given_name || '-'}</td>
                      <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.roommate_nationality || '-'}</td>
                      <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.roommate_gender || '-'}</td>
                      <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{formatDateForDisplay(order.roommate_birth_date)}</td>
                      <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.roommate_id_type || '-'}</td>
                      <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.roommate_id_number || '-'}</td>
                      <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{formatDateForDisplay(order.roommate_id_expiry_date)}</td>
                      <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.roommate_mobile_phone || '-'}</td>
                      <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.roommate_email || '-'}</td>
                      <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.company_name || '-'}</td>
                      <td className="p-2 text-xs text-gray-900 whitespace-nowrap">{order.amount ? `¥${order.amount}` : '-'}</td>
                      <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{formatDateTime(order.created_at)}</td>
                      <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{formatDateTime(order.updated_at)}</td>
                      <td className="p-2 text-xs whitespace-nowrap text-center sticky right-0 bg-white border-l border-gray-200">
                        <div className="flex items-center justify-center gap-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => openDetailModal(order)}
                            className="h-6 w-6 p-0 text-gray-600 hover:text-blue-600 hover:bg-blue-50"
                            title="查看详情"
                          >
                            <Eye className="h-3 w-3" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEditOrder(order)}
                            className="h-6 w-6 p-0 text-gray-600 hover:text-green-600 hover:bg-green-50"
                            title="编辑订单"
                          >
                            <PenTool className="h-3 w-3" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteOrder(order.id, order.guest_full_name || '未知用户')}
                            disabled={deletingOrderId === order.id}
                            className="h-6 w-6 p-0 text-gray-600 hover:text-red-600 hover:bg-red-50"
                            title="删除订单"
                          >
                            {deletingOrderId === order.id ? (
                              <div className="animate-spin rounded-full h-3 w-3 border-b border-red-600"></div>
                            ) : (
                              <Trash2 className="h-3 w-3" />
                            )}
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* 分页 */}
            {totalCount > pageSize && (
              <div className="flex items-center justify-between p-4 border-t border-gray-200">
                <span className="text-sm text-gray-700">
                  显示 {(currentPage - 1) * pageSize + 1} 到 {Math.min(currentPage * pageSize, totalCount)} 条，共 {totalCount} 条记录
                </span>
                
                <div className="flex items-center space-x-1">
                  {(() => {
                    const totalPages = Math.ceil(totalCount / pageSize);
                    const pages = [];
                    const maxVisiblePages = 5;
                    
                    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
                    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
                    
                    if (endPage - startPage + 1 < maxVisiblePages) {
                      startPage = Math.max(1, endPage - maxVisiblePages + 1);
                    }
                    
                    for (let i = startPage; i <= endPage; i++) {
                      pages.push(
                        <button
                          key={i}
                          onClick={() => loadHotelOrders(i)}
                          disabled={pageLoading}
                          className={`px-3 py-1 text-sm border rounded ${
                            i === currentPage
                              ? 'bg-blue-600 text-white border-blue-600'
                              : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                          } ${pageLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
                        >
                          {i}
                        </button>
                      );
                    }
                    
                    return pages;
                  })()}
                </div>
              </div>
            )}
          </>
        ) : (
          <div className="text-center py-8">
            <Building className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">暂无酒店订单数据</p>
          </div>
        )}
      </CardContent>
    </Card>
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">加载中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-gray-50 min-h-screen flex flex-col">
      {/* Tab导航 */}
      <div className="bg-white border-b border-gray-200">
        <div className="grid grid-cols-2">
          <button
            onClick={() => setActiveTab('input')}
            className={`py-4 px-6 text-center font-medium text-sm transition-colors duration-200 border-b-2 ${
              activeTab === 'input'
                ? 'border-blue-500 text-blue-600 bg-blue-50'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50'
            }`}
          >
            <div className="flex items-center justify-center gap-2">
              <Upload className="h-4 w-4" />
              Excel文件上传
            </div>
          </button>
          <button
            onClick={() => setActiveTab('text')}
            className={`py-4 px-6 text-center font-medium text-sm transition-colors duration-200 border-b-2 ${
              activeTab === 'text'
                ? 'border-blue-500 text-blue-600 bg-blue-50'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50'
            }`}
          >
            <div className="flex items-center justify-center gap-2">
              <FileText className="h-4 w-4" />
              文本内容
            </div>
          </button>
        </div>
      </div>

      {/* Tab内容区域 */}
      <div className="h-auto">
        {activeTab === 'input' && renderInputTab()}
        {activeTab === 'text' && renderTextTab()}
      </div>

      {/* 独立的订单列表区域 */}
      <div className="flex-1 p-6">
        {renderOrderList()}
      </div>

      {/* 验证错误弹框 */}
      {showValidationDialog && validationDialogData && (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
          {/* 背景遮罩 */}
          <div 
            className="absolute inset-0 bg-black/50 backdrop-blur-sm" 
            onClick={handleValidationCancel}
          ></div>
          
          {/* 对话框内容 */}
          <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl mx-4 z-50">
            {/* 头部 */}
            <div className="flex justify-between items-center px-6 py-4">
              <h2 className="text-lg font-medium text-gray-900 flex items-center gap-2">
                <FileSpreadsheet className="h-5 w-5" />
                Excel数据验证结果
              </h2>
              <button 
                onClick={handleValidationCancel}
                className="text-gray-400 hover:text-gray-500"
              >
                <X className="h-5 w-5" />
              </button>
            </div>
            
            {/* 统计信息 */}
            <div className="grid grid-cols-4 gap-4 px-6 py-4">
              <div className="bg-blue-50 rounded-lg p-3 flex items-center">
                <FileSpreadsheet className="h-5 w-5 text-blue-600 mr-2" />
                <div>
                  <div className="text-sm text-gray-600">总行数</div>
                  <div className="text-xl font-bold text-gray-900">{validationDialogData.total_rows}</div>
                </div>
              </div>
              
              <div className="bg-green-50 rounded-lg p-3 flex items-center">
                <Check className="h-5 w-5 text-green-600 mr-2" />
                <div>
                  <div className="text-sm text-gray-600">有效行数</div>
                  <div className="text-xl font-bold text-green-600">{validationDialogData.valid_rows}</div>
                </div>
              </div>
              
              <div className="bg-red-50 rounded-lg p-3 flex items-center">
                <X className="h-5 w-5 text-red-600 mr-2" />
                <div>
                  <div className="text-sm text-gray-600">错误行数</div>
                  <div className="text-xl font-bold text-red-600">{validationDialogData.error_rows}</div>
                </div>
              </div>
              
              <div className="bg-amber-50 rounded-lg p-3 flex items-center">
                <AlertTriangle className="h-5 w-5 text-amber-600 mr-2" />
                <div>
                  <div className="text-sm text-gray-600">重复行数</div>
                  <div className="text-xl font-bold text-amber-600">{validationDialogData.duplicate_rows.length}</div>
                </div>
              </div>
            </div>
            
            {/* 错误信息 */}
            {validationDialogData.errors.length > 0 && (
              <div className="px-6 py-4 border-t border-gray-200">
                <h3 className="text-sm font-medium text-gray-900 mb-3">验证错误</h3>
                <div className="space-y-2 max-h-40 overflow-y-auto">
                  {validationDialogData.errors.map((error, index) => (
                    <div key={index} className="flex items-start gap-2 text-sm">
                      <X className="h-4 w-4 text-red-500 mt-0.5 flex-shrink-0" />
                      <div>
                        <span className="font-medium">第 {error.row} 行</span>
                        <span className="mx-1">-</span>
                        <span className="text-gray-600">{error.message}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* 重复数据提示 */}
            {validationDialogData.duplicate_rows.length > 0 && (
              <div className="px-6 py-4 border-t border-gray-200">
                <h3 className="text-sm font-medium text-gray-900 mb-3">重复数据</h3>
                <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
                  <div className="flex items-start gap-2">
                    <AlertTriangle className="h-5 w-5 text-amber-600 mt-0.5" />
                    <div className="flex-1">
                      <p className="text-sm text-amber-800">
                        检测到以下行存在重复数据（基于证件号码+酒店ID+入住时间）：
                      </p>
                      <p className="text-sm text-amber-700 mt-2">
                        第 {validationDialogData.duplicate_rows.join('、')} 行
                      </p>
                      <p className="text-sm text-amber-600 mt-2">
                        您可以选择：
                      </p>
                      <ul className="list-disc list-inside text-sm text-amber-700 mt-1">
                        <li>忽略重复数据并继续导入其他数据</li>
                        <li>取消导入并修改Excel文件</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            )}
            
            {/* 按钮区域 */}
            <div className="flex justify-end gap-3 px-6 py-4 bg-gray-50 border-t border-gray-200">
              <Button variant="outline" onClick={handleValidationCancel}>
                取消导入
              </Button>
              <Button 
                onClick={handleValidationConfirm}
                disabled={!validationDialogData.should_continue}
                className={validationDialogData.errors.length > 0 || validationDialogData.duplicate_rows.length > 0 ? 'bg-amber-600 hover:bg-amber-700' : ''}
              >
                {validationDialogData.errors.length > 0 || validationDialogData.duplicate_rows.length > 0 ? '继续导入' : '确认导入'}
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* 订单详情/编辑弹窗 */}
      {showDetailModal && selectedOrder && editingOrder && (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
          {/* 背景遮罩 */}
          <div className="absolute inset-0 bg-black/50 backdrop-blur-sm" onClick={() => {
                setShowDetailModal(false);
            setIsEditMode(false);
                setEditingOrder(null);
                setValidationErrors({});
          }}></div>
          
          {/* 弹窗内容 */}
          <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden z-50 mx-4">
            {/* 弹窗头部 */}
            <div className="flex justify-between items-center px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-white">
              <h2 className="text-xl font-semibold text-blue-700 flex items-center">
                <Eye className="h-5 w-5 mr-2" />
                酒店订单详情 {isEditMode && <span className="ml-2 text-sm text-orange-600">(编辑模式)</span>}
              </h2>
                <button 
                  onClick={() => {
                      setShowDetailModal(false);
                  setIsEditMode(false);
                      setEditingOrder(null);
                      setValidationErrors({});
                  }}
                  className="h-8 w-8 rounded-full text-gray-400 hover:text-gray-600 hover:bg-gray-100 flex items-center justify-center transition-colors"
                >
                  <X className="h-5 w-5" />
                </button>
            </div>

            {/* 弹窗内容 */}
            <div className="p-6 overflow-y-auto max-h-[calc(90vh-180px)]">
              <div className="space-y-8">
                {/* 订单状态区域 */}
                <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                  <div className="flex flex-col gap-3">
                    <div className="flex items-center justify-between">
                      <h3 className="text-lg font-medium text-gray-900">订单状态</h3>
                      <div className="flex items-center">
                    {getOrderStatusDisplay(selectedOrder.order_status)}
                  </div>
                    </div>
                    
                    {/* 失败原因展示 - 只在验证失败或预定失败时显示 */}
                    {(selectedOrder.order_status === 'check_failed' || selectedOrder.order_status === 'failed') && selectedOrder.fail_reason && (
                      <div className="text-red-600 text-sm">
                        {selectedOrder.fail_reason}
                </div>
                  )}
                </div>
              </div>

                {/* 入住人信息 */}
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4 pb-2 border-b border-gray-200">入住人信息</h3>
                  <div className="overflow-hidden border border-gray-300 rounded-lg">
                    <table className="w-full">
                      <tbody>
                        <tr className="border-b border-gray-200">
                          <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">入住人姓名 <span className="text-red-500">*</span></label>
                          </td>
                          <td className="py-3 px-4 w-1/4 border-r border-gray-200">
                            <EditableCell 
                              value={editingOrder.guest_full_name} 
                              field="guest_full_name" 
                              isEditing={isEditMode} 
                              onFieldChange={handleFieldChange}
                              validationErrors={validationErrors}
                              editingOrder={editingOrder}
                            />
                          </td>
                          <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">入住人姓</label>
                          </td>
                          <td className="py-3 px-4 w-1/4">
                            <EditableCell 
                              value={editingOrder.guest_surname} 
                              field="guest_surname" 
                              isEditing={isEditMode} 
                              onFieldChange={handleFieldChange}
                              validationErrors={validationErrors}
                              editingOrder={editingOrder}
                            />
                          </td>
                        </tr>
                        <tr className="border-b border-gray-200">
                          <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">入住人名</label>
                          </td>
                          <td className="py-3 px-4 border-r border-gray-200">
                            <EditableCell 
                              value={editingOrder.guest_given_name} 
                              field="guest_given_name" 
                              isEditing={isEditMode} 
                              onFieldChange={handleFieldChange}
                              validationErrors={validationErrors}
                              editingOrder={editingOrder}
                            />
                          </td>
                          <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">入住人国籍</label>
                          </td>
                          <td className="py-3 px-4">
                            <EditableCell 
                              value={editingOrder.guest_nationality} 
                              field="guest_nationality" 
                              isEditing={isEditMode} 
                              onFieldChange={handleFieldChange}
                              validationErrors={validationErrors}
                              editingOrder={editingOrder}
                            />
                          </td>
                        </tr>
                        <tr className="border-b border-gray-200">
                          <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">入住人性别</label>
                          </td>
                          <td className="py-3 px-4 border-r border-gray-200">
                            <EditableCell 
                              value={editingOrder.guest_gender} 
                              field="guest_gender" 
                              isEditing={isEditMode} 
                              onFieldChange={handleFieldChange}
                              validationErrors={validationErrors}
                              editingOrder={editingOrder}
                            />
                          </td>
                          <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">入住人出生日期</label>
                          </td>
                          <td className="py-3 px-4">
                            <EditableCell 
                              value={editingOrder.guest_birth_date} 
                              field="guest_birth_date" 
                              type="date" 
                              isEditing={isEditMode} 
                              onFieldChange={handleFieldChange}
                              validationErrors={validationErrors}
                              editingOrder={editingOrder}
                            />
                          </td>
                        </tr>
                        <tr className="border-b border-gray-200">
                          <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">入住人证件类型 <span className="text-red-500">*</span></label>
                          </td>
                          <td className="py-3 px-4 border-r border-gray-200">
                            <EditableCell 
                              value={editingOrder.guest_id_type} 
                              field="guest_id_type" 
                              isEditing={isEditMode} 
                              onFieldChange={handleFieldChange}
                              validationErrors={validationErrors}
                              editingOrder={editingOrder}
                            />
                          </td>
                          <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">入住人证件号码 <span className="text-red-500">*</span></label>
                          </td>
                          <td className="py-3 px-4">
                            <EditableCell 
                              value={editingOrder.guest_id_number} 
                              field="guest_id_number" 
                              isEditing={isEditMode} 
                              onFieldChange={handleFieldChange}
                              validationErrors={validationErrors}
                              editingOrder={editingOrder}
                            />
                          </td>
                        </tr>
                        <tr className="border-b border-gray-200">
                          <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">入住人证件有效期至</label>
                          </td>
                          <td className="py-3 px-4 border-r border-gray-200">
                            <EditableCell 
                              value={editingOrder.guest_id_expiry_date} 
                              field="guest_id_expiry_date" 
                              type="date" 
                              isEditing={isEditMode} 
                              onFieldChange={handleFieldChange}
                              validationErrors={validationErrors}
                              editingOrder={editingOrder}
                            />
                          </td>
                          <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">入住人手机号国际区号</label>
                          </td>
                          <td className="py-3 px-4">
                            <EditableCell 
                              value={editingOrder.guest_mobile_country_code} 
                              field="guest_mobile_country_code" 
                              isEditing={isEditMode} 
                              onFieldChange={handleFieldChange}
                              validationErrors={validationErrors}
                              editingOrder={editingOrder}
                            />
                          </td>
                        </tr>
                        <tr className="border-b border-gray-200">
                          <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">入住人手机号 <span className="text-red-500">*</span></label>
                          </td>
                          <td className="py-3 px-4 border-r border-gray-200">
                            <EditableCell 
                              value={editingOrder.guest_mobile_phone} 
                              field="guest_mobile_phone" 
                              isEditing={isEditMode} 
                              onFieldChange={handleFieldChange}
                              validationErrors={validationErrors}
                              editingOrder={editingOrder}
                            />
                          </td>
                          <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">入住人邮箱</label>
                          </td>
                          <td className="py-3 px-4">
                            <EditableCell 
                              value={editingOrder.guest_email} 
                              field="guest_email" 
                              type="email"
                              isEditing={isEditMode} 
                              onFieldChange={handleFieldChange}
                              validationErrors={validationErrors}
                              editingOrder={editingOrder}
                            />
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>

                {/* 入住信息 */}
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4 pb-2 border-b border-gray-200">入住信息</h3>
                  <div className="overflow-hidden border border-gray-300 rounded-lg">
                    <table className="w-full">
                      <tbody>
                        <tr className="border-b border-gray-200">
                          <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">目的地 <span className="text-red-500">*</span></label>
                          </td>
                          <td className="py-3 px-4 w-1/4 border-r border-gray-200">
                            <EditableCell 
                              value={editingOrder.destination} 
                              field="destination" 
                              isEditing={isEditMode} 
                              onFieldChange={handleFieldChange}
                              validationErrors={validationErrors}
                              editingOrder={editingOrder}
                            />
                          </td>
                          <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">酒店ID <span className="text-red-500">*</span></label>
                          </td>
                          <td className="py-3 px-4 w-1/4">
                            <EditableCell 
                              value={editingOrder.hotel_id} 
                              field="hotel_id" 
                              isEditing={isEditMode} 
                              onFieldChange={handleFieldChange}
                              validationErrors={validationErrors}
                              editingOrder={editingOrder}
                            />
                          </td>
                        </tr>
                        <tr className="border-b border-gray-200">
                          <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">预订房型 <span className="text-red-500">*</span></label>
                          </td>
                          <td className="py-3 px-4 border-r border-gray-200">
                            <EditableCell 
                              value={editingOrder.room_type} 
                              field="room_type" 
                              isEditing={isEditMode} 
                              onFieldChange={handleFieldChange}
                              validationErrors={validationErrors}
                              editingOrder={editingOrder}
                            />
                          </td>
                          <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">预订房间数量</label>
                          </td>
                          <td className="py-3 px-4">
                            <EditableCell 
                              value={editingOrder.room_count} 
                              field="room_count" 
                              type="number" 
                              isEditing={isEditMode} 
                              onFieldChange={handleFieldChange}
                              validationErrors={validationErrors}
                              editingOrder={editingOrder}
                            />
                          </td>
                        </tr>
                        <tr className="border-b border-gray-200">
                          <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">政策名称</label>
                          </td>
                          <td className="py-3 px-4 border-r border-gray-200">
                            <EditableCell 
                              value={editingOrder.policy_name} 
                              field="policy_name" 
                              isEditing={isEditMode} 
                              onFieldChange={handleFieldChange}
                              validationErrors={validationErrors}
                              editingOrder={editingOrder}
                            />
                          </td>
                          <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">是否含早</label>
                          </td>
                          <td className="py-3 px-4">
                            <EditableCell 
                              value={editingOrder.include_breakfast} 
                              field="include_breakfast" 
                              isEditing={isEditMode} 
                              onFieldChange={handleFieldChange}
                              validationErrors={validationErrors}
                              editingOrder={editingOrder}
                            />
                          </td>
                        </tr>
                        <tr className="border-b border-gray-200">
                          <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">是否为半日房</label>
                          </td>
                          <td className="py-3 px-4 border-r border-gray-200">
                            <EditableCell 
                              value={editingOrder.is_half_day_room} 
                              field="is_half_day_room" 
                              isEditing={isEditMode} 
                              onFieldChange={handleFieldChange}
                              validationErrors={validationErrors}
                              editingOrder={editingOrder}
                            />
                          </td>
                          <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">入住时间 <span className="text-red-500">*</span></label>
                          </td>
                          <td className="py-3 px-4">
                            <EditableCell 
                              value={editingOrder.check_in_time} 
                              field="check_in_time" 
                              type="datetime-local" 
                              isEditing={isEditMode} 
                              onFieldChange={handleFieldChange}
                              validationErrors={validationErrors}
                              editingOrder={editingOrder}
                            />
                          </td>
                        </tr>
                        <tr className="border-b border-gray-200">
                          <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">离店时间 <span className="text-red-500">*</span></label>
                          </td>
                          <td className="py-3 px-4 border-r border-gray-200">
                            <EditableCell 
                              value={editingOrder.check_out_time} 
                              field="check_out_time" 
                              type="datetime-local" 
                              isEditing={isEditMode} 
                              onFieldChange={handleFieldChange}
                              validationErrors={validationErrors}
                              editingOrder={editingOrder}
                            />
                          </td>
                          <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">是否为团房</label>
                          </td>
                          <td className="py-3 px-4">
                            <EditableCell 
                              value={editingOrder.is_group_booking} 
                              field="is_group_booking" 
                              isEditing={isEditMode} 
                              onFieldChange={handleFieldChange}
                              validationErrors={validationErrors}
                              editingOrder={editingOrder}
                            />
                          </td>
                        </tr>
                        <tr className="border-b border-gray-200">
                          <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">团房名称</label>
                          </td>
                          <td className="py-3 px-4 border-r border-gray-200">
                            <EditableCell 
                              value={editingOrder.group_booking_name} 
                              field="group_booking_name" 
                              isEditing={isEditMode} 
                              onFieldChange={handleFieldChange}
                              validationErrors={validationErrors}
                              editingOrder={editingOrder}
                            />
                          </td>
                          <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">房间号</label>
                          </td>
                          <td className="py-3 px-4">
                            <EditableCell 
                              value={editingOrder.room_number} 
                              field="room_number" 
                              isEditing={isEditMode} 
                              onFieldChange={handleFieldChange}
                              validationErrors={validationErrors}
                              editingOrder={editingOrder}
                            />
                          </td>
                        </tr>
                        <tr className="border-b border-gray-200">
                          <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">支付方式</label>
                          </td>
                          <td className="py-3 px-4 border-r border-gray-200">
                            <EditableCell 
                              value={editingOrder.payment_method} 
                              field="payment_method" 
                              isEditing={isEditMode} 
                              onFieldChange={handleFieldChange}
                              validationErrors={validationErrors}
                              editingOrder={editingOrder}
                            />
                          </td>
                          <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">发票类型</label>
                          </td>
                          <td className="py-3 px-4">
                            <EditableCell 
                              value={editingOrder.invoice_type} 
                              field="invoice_type" 
                              isEditing={isEditMode} 
                              onFieldChange={handleFieldChange}
                              validationErrors={validationErrors}
                              editingOrder={editingOrder}
                            />
                          </td>
                        </tr>
                        <tr className="border-b border-gray-200">
                          <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">税率</label>
                          </td>
                          <td className="py-3 px-4 border-r border-gray-200">
                            <EditableCell 
                              value={editingOrder.tax_rate} 
                              field="tax_rate" 
                              type="number"
                              isEditing={isEditMode} 
                              onFieldChange={handleFieldChange}
                              validationErrors={validationErrors}
                              editingOrder={editingOrder}
                            />
                          </td>
                          <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">协议类型</label>
                          </td>
                          <td className="py-3 px-4">
                            <EditableCell 
                              value={editingOrder.agreement_type} 
                              field="agreement_type" 
                              isEditing={isEditMode} 
                              onFieldChange={handleFieldChange}
                              validationErrors={validationErrors}
                              editingOrder={editingOrder}
                            />
                          </td>
                        </tr>
                        <tr className="border-b border-gray-200">
                          <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">供应商名称</label>
                          </td>
                          <td className="py-3 px-4 border-r border-gray-200">
                            <EditableCell 
                              value={editingOrder.supplier_name} 
                              field="supplier_name" 
                              isEditing={isEditMode} 
                              onFieldChange={handleFieldChange}
                              validationErrors={validationErrors}
                              editingOrder={editingOrder}
                            />
                          </td>
                          <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">支付渠道</label>
                          </td>
                          <td className="py-3 px-4">
                            <EditableCell 
                              value={editingOrder.payment_channel} 
                              field="payment_channel" 
                              isEditing={isEditMode} 
                              onFieldChange={handleFieldChange}
                              validationErrors={validationErrors}
                              editingOrder={editingOrder}
                            />
                          </td>
                        </tr>
                        <tr className="border-b border-gray-200">
                          <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">支付流水号</label>
                          </td>
                          <td className="py-3 px-4 border-r border-gray-200">
                            <EditableCell 
                              value={editingOrder.payment_transaction_id} 
                              field="payment_transaction_id" 
                              isEditing={isEditMode} 
                              onFieldChange={handleFieldChange}
                              validationErrors={validationErrors}
                              editingOrder={editingOrder}
                            />
                          </td>
                          <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">对供成本（每间房成本）</label>
                          </td>
                          <td className="py-3 px-4">
                            <EditableCell 
                              value={editingOrder.cost_per_room} 
                              field="cost_per_room" 
                              type="number"
                              isEditing={isEditMode} 
                              onFieldChange={handleFieldChange}
                              validationErrors={validationErrors}
                              editingOrder={editingOrder}
                            />
                          </td>
                        </tr>
                        <tr className="border-b border-gray-200">
                          <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">隐藏手续费</label>
                          </td>
                          <td className="py-3 px-4 border-r border-gray-200">
                            <EditableCell 
                              value={editingOrder.hidden_service_fee} 
                              field="hidden_service_fee" 
                              type="number"
                              isEditing={isEditMode} 
                              onFieldChange={handleFieldChange}
                              validationErrors={validationErrors}
                              editingOrder={editingOrder}
                            />
                          </td>
                          <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">取消规则</label>
                          </td>
                          <td className="py-3 px-4">
                            <EditableCell 
                              value={editingOrder.cancellation_policy} 
                              field="cancellation_policy" 
                              isEditing={isEditMode} 
                              onFieldChange={handleFieldChange}
                              validationErrors={validationErrors}
                              editingOrder={editingOrder}
                            />
                          </td>
                        </tr>
                        <tr className="border-b border-gray-200">
                          <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">是否违规</label>
                          </td>
                          <td className="py-3 px-4 border-r border-gray-200">
                            <EditableCell 
                              value={editingOrder.is_violation} 
                              field="is_violation" 
                              isEditing={isEditMode} 
                              onFieldChange={handleFieldChange}
                              validationErrors={validationErrors}
                              editingOrder={editingOrder}
                            />
                          </td>
                          <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">联系人姓名 <span className="text-red-500">*</span></label>
                          </td>
                          <td className="py-3 px-4">
                            <EditableCell 
                              value={editingOrder.contact_person} 
                              field="contact_person" 
                              isEditing={isEditMode} 
                              onFieldChange={handleFieldChange}
                              validationErrors={validationErrors}
                              editingOrder={editingOrder}
                            />
                          </td>
                        </tr>
                        <tr className="border-b border-gray-200">
                          <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">联系人手机号国际区号</label>
                          </td>
                          <td className="py-3 px-4 border-r border-gray-200">
                            <EditableCell 
                              value={editingOrder.contact_mobile_country_code} 
                              field="contact_mobile_country_code" 
                              isEditing={isEditMode} 
                              onFieldChange={handleFieldChange}
                              validationErrors={validationErrors}
                              editingOrder={editingOrder}
                            />
                          </td>
                          <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">联系人手机号 <span className="text-red-500">*</span></label>
                          </td>
                          <td className="py-3 px-4">
                            <EditableCell 
                              value={editingOrder.contact_mobile_phone} 
                              field="contact_mobile_phone" 
                              isEditing={isEditMode} 
                              onFieldChange={handleFieldChange}
                              validationErrors={validationErrors}
                              editingOrder={editingOrder}
                            />
                          </td>
                        </tr>
                        <tr className="border-b border-gray-200">
                          <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">订单备注</label>
                          </td>
                          <td className="py-3 px-4" colSpan={3}>
                            <EditableCell 
                              value={editingOrder.order_remarks} 
                              field="order_remarks" 
                              isEditing={isEditMode} 
                              onFieldChange={handleFieldChange}
                              validationErrors={validationErrors}
                              editingOrder={editingOrder}
                            />
                          </td>
                        </tr>
                        <tr className="border-b border-gray-200">
                          <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">成本中心 <span className="text-red-500">*</span></label>
                          </td>
                          <td className="py-3 px-4 border-r border-gray-200">
                            <EditableCell 
                              value={editingOrder.cost_center} 
                              field="cost_center" 
                              isEditing={isEditMode} 
                              onFieldChange={handleFieldChange}
                              validationErrors={validationErrors}
                              editingOrder={editingOrder}
                            />
                          </td>
                          <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">审批人 <span className="text-red-500">*</span></label>
                          </td>
                          <td className="py-3 px-4">
                            <EditableCell 
                              value={editingOrder.approver} 
                              field="approver" 
                              isEditing={isEditMode} 
                              onFieldChange={handleFieldChange}
                              validationErrors={validationErrors}
                              editingOrder={editingOrder}
                            />
                          </td>
                        </tr>
                        <tr>
                          <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">行程提交项</label>
                          </td>
                          <td className="py-3 px-4" colSpan={3}>
                            <EditableCell 
                              value={editingOrder.trip_submission_item} 
                              field="trip_submission_item" 
                              isEditing={isEditMode} 
                              onFieldChange={handleFieldChange}
                              validationErrors={validationErrors}
                              editingOrder={editingOrder}
                            />
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>

                {/* 同住人信息 */}
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4 pb-2 border-b border-gray-200">同住人信息</h3>
                  <div className="overflow-hidden border border-gray-300 rounded-lg">
                    <table className="w-full">
                      <tbody>
                        <tr className="border-b border-gray-200">
                          <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">同住人姓名</label>
                          </td>
                          <td className="py-3 px-4 w-1/4 border-r border-gray-200">
                            <EditableCell 
                              value={editingOrder.roommate_name} 
                              field="roommate_name" 
                              isEditing={isEditMode} 
                              onFieldChange={handleFieldChange}
                              validationErrors={validationErrors}
                              editingOrder={editingOrder}
                            />
                          </td>
                          <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">同住人姓</label>
                          </td>
                          <td className="py-3 px-4 w-1/4">
                            <EditableCell 
                              value={editingOrder.roommate_surname} 
                              field="roommate_surname" 
                              isEditing={isEditMode} 
                              onFieldChange={handleFieldChange}
                              validationErrors={validationErrors}
                              editingOrder={editingOrder}
                            />
                          </td>
                        </tr>
                        <tr className="border-b border-gray-200">
                          <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">同住人名</label>
                          </td>
                          <td className="py-3 px-4 border-r border-gray-200">
                            <EditableCell 
                              value={editingOrder.roommate_given_name} 
                              field="roommate_given_name" 
                              isEditing={isEditMode} 
                              onFieldChange={handleFieldChange}
                              validationErrors={validationErrors}
                              editingOrder={editingOrder}
                            />
                          </td>
                          <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">同住人国籍</label>
                          </td>
                          <td className="py-3 px-4">
                            <EditableCell 
                              value={editingOrder.roommate_nationality} 
                              field="roommate_nationality" 
                              isEditing={isEditMode} 
                              onFieldChange={handleFieldChange}
                              validationErrors={validationErrors}
                              editingOrder={editingOrder}
                            />
                          </td>
                        </tr>
                        <tr className="border-b border-gray-200">
                          <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">同住人性别</label>
                          </td>
                          <td className="py-3 px-4 border-r border-gray-200">
                            <EditableCell 
                              value={editingOrder.roommate_gender} 
                              field="roommate_gender" 
                              isEditing={isEditMode} 
                              onFieldChange={handleFieldChange}
                              validationErrors={validationErrors}
                              editingOrder={editingOrder}
                            />
                          </td>
                          <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">同住人出生日期</label>
                          </td>
                          <td className="py-3 px-4">
                            <EditableCell 
                              value={editingOrder.roommate_birth_date} 
                              field="roommate_birth_date" 
                              type="date" 
                              isEditing={isEditMode} 
                              onFieldChange={handleFieldChange}
                              validationErrors={validationErrors}
                              editingOrder={editingOrder}
                            />
                          </td>
                        </tr>
                        <tr className="border-b border-gray-200">
                          <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">同住人证件类型</label>
                          </td>
                          <td className="py-3 px-4 border-r border-gray-200">
                            <EditableCell 
                              value={editingOrder.roommate_id_type} 
                              field="roommate_id_type" 
                              isEditing={isEditMode} 
                              onFieldChange={handleFieldChange}
                              validationErrors={validationErrors}
                              editingOrder={editingOrder}
                            />
                          </td>
                          <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">同住人证件号码</label>
                          </td>
                          <td className="py-3 px-4">
                            <EditableCell 
                              value={editingOrder.roommate_id_number} 
                              field="roommate_id_number" 
                              isEditing={isEditMode} 
                              onFieldChange={handleFieldChange}
                              validationErrors={validationErrors}
                              editingOrder={editingOrder}
                            />
                          </td>
                        </tr>
                        <tr className="border-b border-gray-200">
                          <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">同住人证件有效期至</label>
                          </td>
                          <td className="py-3 px-4 border-r border-gray-200">
                            <EditableCell 
                              value={editingOrder.roommate_id_expiry_date} 
                              field="roommate_id_expiry_date" 
                              type="date" 
                              isEditing={isEditMode} 
                              onFieldChange={handleFieldChange}
                              validationErrors={validationErrors}
                              editingOrder={editingOrder}
                            />
                          </td>
                          <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">同住人手机号国际区号</label>
                          </td>
                          <td className="py-3 px-4">
                            <EditableCell 
                              value={editingOrder.roommate_mobile_country_code} 
                              field="roommate_mobile_country_code" 
                              isEditing={isEditMode} 
                              onFieldChange={handleFieldChange}
                              validationErrors={validationErrors}
                              editingOrder={editingOrder}
                            />
                          </td>
                        </tr>
                        <tr className="border-b border-gray-200">
                          <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">同住人手机号</label>
                          </td>
                          <td className="py-3 px-4 border-r border-gray-200">
                            <EditableCell 
                              value={editingOrder.roommate_mobile_phone} 
                              field="roommate_mobile_phone" 
                              isEditing={isEditMode} 
                              onFieldChange={handleFieldChange}
                              validationErrors={validationErrors}
                              editingOrder={editingOrder}
                            />
                          </td>
                          <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">同住人邮箱</label>
                          </td>
                          <td className="py-3 px-4">
                            <EditableCell 
                              value={editingOrder.roommate_email} 
                              field="roommate_email" 
                              type="email"
                              isEditing={isEditMode} 
                              onFieldChange={handleFieldChange}
                              validationErrors={validationErrors}
                              editingOrder={editingOrder}
                            />
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>

                {/* 对账单信息 */}
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4 pb-2 border-b border-gray-200">对账单信息</h3>
                  <div className="overflow-hidden border border-gray-300 rounded-lg">
                    <table className="w-full">
                      <tbody>
                        <tr className="border-b border-gray-200">
                          <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">公司名称</label>
                          </td>
                          <td className="py-3 px-4 w-1/4 border-r border-gray-200">
                            <EditableCell 
                              value={editingOrder.company_name} 
                              field="company_name" 
                              isEditing={isEditMode} 
                              onFieldChange={handleFieldChange}
                              validationErrors={validationErrors}
                              editingOrder={editingOrder}
                            />
                          </td>
                          <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">酒店名称</label>
                          </td>
                          <td className="py-3 px-4 w-1/4">
                            <EditableCell 
                              value={editingOrder.hotel_name} 
                              field="hotel_name" 
                              isEditing={isEditMode} 
                              onFieldChange={handleFieldChange}
                              validationErrors={validationErrors}
                              editingOrder={editingOrder}
                            />
                          </td>
                        </tr>
                        <tr className="border-b border-gray-200">
                          <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">对客金额</label>
                          </td>
                          <td className="py-3 px-4 border-r border-gray-200">
                            <EditableCell 
                              value={editingOrder.amount} 
                              field="amount" 
                              type="number" 
                              isEditing={isEditMode} 
                              onFieldChange={handleFieldChange}
                              validationErrors={validationErrors}
                              editingOrder={editingOrder}
                            />
                          </td>
                          <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">代订人</label>
                          </td>
                          <td className="py-3 px-4">
                            <EditableCell 
                              value={editingOrder.booking_agent} 
                              field="booking_agent" 
                              isEditing={isEditMode} 
                              onFieldChange={handleFieldChange}
                              validationErrors={validationErrors}
                              editingOrder={editingOrder}
                            />
                          </td>
                        </tr>
                        <tr>
                          <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">订单号</label>
                          </td>
                          <td className="py-3 px-4 border-r border-gray-200">
                            <EditableCell 
                              value={editingOrder.order_number} 
                              field="order_number" 
                              isEditing={isEditMode} 
                              onFieldChange={handleFieldChange}
                              validationErrors={validationErrors}
                              editingOrder={editingOrder}
                            />
                          </td>
                          <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">账单号</label>
                          </td>
                          <td className="py-3 px-4">
                            <EditableCell 
                              value={editingOrder.bill_number} 
                              field="bill_number" 
                              isEditing={isEditMode} 
                              onFieldChange={handleFieldChange}
                              validationErrors={validationErrors}
                              editingOrder={editingOrder}
                            />
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
                    </div>
                    </div>
            
            {/* 弹窗底部 */}
            <div className="px-6 py-6 border-t border-gray-200 bg-gray-50 flex justify-between items-center">
              <div className="text-sm text-gray-500">
                {isEditMode ? '编辑模式：修改内容后点击保存按钮' : '查看模式：点击修改按钮开始编辑'}
                  </div>
              <div className="flex items-center gap-3">
                {isEditMode ? (
                  <>
                    <Button variant="outline" onClick={() => {
                      setIsEditMode(false);
                      setEditingOrder({ ...selectedOrder });
                      setValidationErrors({});
                    }} disabled={saving}>
                      取消
                    </Button>
                    <Button onClick={saveChanges} disabled={saving}>
                      {saving ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                          保存中...
                        </>
                      ) : (
                        <>
                          <Save className="h-4 w-4 mr-2" />
                          保存
                        </>
                      )}
                    </Button>
                  </>
                ) : (
                  <>
                    <Button variant="outline" onClick={() => {
                      setIsEditMode(true);
                      setValidationErrors({});
                    }}>
                      <Edit className="h-4 w-4 mr-2" />
                      修改
                    </Button>
                    <Button onClick={() => {
                      setShowDetailModal(false);
                      setIsEditMode(false);
                      setEditingOrder(null);
                      setValidationErrors({});
                    }}>
                      关闭
                    </Button>
                  </>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 通用确认对话框 */}
      {showGeneralConfirmDialog && generalConfirmDialogData && (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
          {/* 背景遮罩 */}
          <div 
            className="absolute inset-0 bg-black/50 backdrop-blur-sm" 
            onClick={() => setShowGeneralConfirmDialog(false)}
          ></div>
          
          {/* 对话框内容 */}
          <div className="bg-white rounded-2xl shadow-2xl w-full max-w-lg mx-4 z-50 overflow-hidden">
            {/* 头部图标区域 */}
            <div className="flex justify-center pt-8 pb-4">
              <div className={`w-16 h-16 rounded-full flex items-center justify-center ${
                generalConfirmDialogData.type === 'danger' ? 'bg-red-100' :
                generalConfirmDialogData.type === 'warning' ? 'bg-orange-100' :
                'bg-blue-100'
              }`}>
                {generalConfirmDialogData.type === 'danger' ? (
                  <AlertTriangle className="h-8 w-8 text-red-500" />
                ) : generalConfirmDialogData.type === 'warning' ? (
                  <AlertTriangle className="h-8 w-8 text-orange-500" />
                ) : (
                  <Check className="h-8 w-8 text-blue-500" />
                )}
              </div>
            </div>
            
            {/* 标题 */}
            <div className="text-center px-6 pb-4">
              <h2 className="text-xl font-semibold text-gray-900 mb-2">
                {generalConfirmDialogData.title}
              </h2>
              <p className="text-sm text-gray-600">
                {generalConfirmDialogData.message}
              </p>
            </div>
            
            {/* 底部按钮 */}
            <div className="px-6 py-4 bg-gray-50 flex justify-center gap-3">
              <Button
                variant="outline"
                onClick={() => setShowGeneralConfirmDialog(false)}
                className="px-6"
              >
                {generalConfirmDialogData.cancelText || '取消'}
              </Button>
              <Button
                onClick={async () => {
                  setShowGeneralConfirmDialog(false);
                  await generalConfirmDialogData.onConfirm();
                }}
                className={`px-6 ${
                  generalConfirmDialogData.type === 'danger' ? 'bg-red-600 hover:bg-red-700' :
                  generalConfirmDialogData.type === 'warning' ? 'bg-orange-600 hover:bg-orange-700' :
                  'bg-blue-600 hover:bg-blue-700'
                } text-white`}
              >
                {generalConfirmDialogData.confirmText || '确认'}
              </Button>
            </div>
          </div>
        </div>
      )}

             {/* 通用确认对话框 */}
       {showGeneralConfirmDialog && generalConfirmDialogData && (
         <div className="fixed inset-0 z-50 flex items-center justify-center">
           {/* 背景遮罩 */}
           <div 
             className="absolute inset-0 bg-black/50 backdrop-blur-sm" 
             onClick={() => setShowGeneralConfirmDialog(false)}
           ></div>
           
           {/* 对话框内容 */}
           <div className="bg-white rounded-lg shadow-xl w-full max-w-md mx-4 z-50">
             {/* 头部 */}
             <div className="flex justify-between items-center px-6 py-4 border-b border-gray-200">
               <h2 className="text-lg font-medium text-gray-900 flex items-center gap-2">
                 {generalConfirmDialogData.type === 'info' && <Info className="h-5 w-5 text-blue-500" />}
                 {generalConfirmDialogData.type === 'warning' && <AlertTriangle className="h-5 w-5 text-yellow-500" />}
                 {generalConfirmDialogData.type === 'danger' && <AlertTriangle className="h-5 w-5 text-red-500" />}
                 {generalConfirmDialogData.title}
               </h2>
               <button 
                 onClick={() => setShowGeneralConfirmDialog(false)}
                 className="text-gray-400 hover:text-gray-500"
               >
                 <X className="h-5 w-5" />
               </button>
             </div>
             
             {/* 内容 */}
             <div className="px-6 py-4">
               <p className="text-sm text-gray-600">
                 {generalConfirmDialogData.message}
               </p>
             </div>
             
             {/* 按钮区域 */}
             <div className="flex justify-end gap-3 px-6 py-4 bg-gray-50 border-t border-gray-200">
              <Button
                 variant="outline" 
                 onClick={() => setShowGeneralConfirmDialog(false)}
               >
                 {generalConfirmDialogData.cancelText || '取消'}
               </Button>
               <Button 
                 onClick={async () => {
                   setShowGeneralConfirmDialog(false);
                   await generalConfirmDialogData.onConfirm();
                 }}
                 className={`${
                   generalConfirmDialogData.type === 'danger' 
                     ? 'bg-red-600 hover:bg-red-700' 
                     : generalConfirmDialogData.type === 'warning'
                     ? 'bg-yellow-600 hover:bg-yellow-700'
                     : 'bg-blue-600 hover:bg-blue-700'
                 } text-white`}
               >
                 {generalConfirmDialogData.confirmText || '确认'}
              </Button>
            </div>
                  </div>
                  </div>
       )}

       {/* 添加确认对话框 */}
       {showConfirmDialog && confirmDialogData && (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
          {/* 背景遮罩 */}
          <div 
            className="absolute inset-0 bg-black/50 backdrop-blur-sm" 
            onClick={() => setShowConfirmDialog(false)}
          ></div>
          
          {/* 对话框内容 */}
          <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl mx-4 z-50">
            {/* 头部 */}
            <div className="flex justify-between items-center px-6 py-4">
              <h2 className="text-lg font-medium text-gray-900 flex items-center gap-2">
                <AlertTriangle className="h-5 w-5" />
                确认操作
              </h2>
              <button 
                onClick={() => setShowConfirmDialog(false)}
                className="text-gray-400 hover:text-gray-500"
              >
                <X className="h-5 w-5" />
              </button>
                </div>
            
                         {/* 内容 */}
             <div className="px-6 pb-6 max-h-80 overflow-y-auto">
               <div className="space-y-4">
                 <div className="text-sm text-gray-600 mb-4">
                   当前页面存在需要先处理的订单，无法进行预订操作：
              </div>

                 {confirmDialogData.checkFailedOrders.length > 0 && (
              <div>
                     <div className="text-sm text-gray-600 mb-2">
                       <span className="font-medium">验证失败订单 ({confirmDialogData.checkFailedOrders.length} 条)：</span>
                     </div>
                <div className="space-y-2">
                       {confirmDialogData.checkFailedOrders.slice(0, 5).map((order) => (
                         <div key={order.id} className="flex items-start gap-3 p-3 bg-amber-50 rounded-lg border border-amber-200">
                           <div className="text-sm">
                             <div className="font-medium text-gray-900">
                               入住人：{order.guest_full_name || '未知'}
                  </div>
                             <div className="text-amber-700 mt-1">
                               {order.fail_reason || '验证失败'}
                  </div>
                </div>
                              </div>
                            ))}
                       {confirmDialogData.checkFailedOrders.length > 5 && (
                         <div className="text-sm text-gray-500 text-center py-2">
                           ... 还有 {confirmDialogData.checkFailedOrders.length - 5} 条验证失败订单
                          </div>
                       )}
                  </div>
                </div>
              )}

                 {confirmDialogData.bookingFailedOrders.length > 0 && (
              <div>
                     <div className="text-sm text-gray-600 mb-2">
                       <span className="font-medium">预定失败订单 ({confirmDialogData.bookingFailedOrders.length} 条)：</span>
                  </div>
                     <div className="space-y-2">
                       {confirmDialogData.bookingFailedOrders.slice(0, 5).map((order) => (
                         <div key={order.id} className="flex items-start gap-3 p-3 bg-red-50 rounded-lg border border-red-200">
                           <div className="text-sm">
                             <div className="font-medium text-gray-900">
                               入住人：{order.guest_full_name || '未知'}
                  </div>
                             <div className="text-red-700 mt-1">
                               {order.fail_reason || '预定失败'}
                </div>
              </div>
              </div>
                       ))}
                       {confirmDialogData.bookingFailedOrders.length > 5 && (
                         <div className="text-sm text-gray-500 text-center py-2">
                           ... 还有 {confirmDialogData.bookingFailedOrders.length - 5} 条预定失败订单
            </div>
                )}
              </div>
                   </div>
                 )}
                 
                 <div className="text-sm text-gray-600 text-center pt-4 border-t border-gray-200">
                   请先编辑修改这些订单，或者使用搜索筛选功能排除这些订单后再进行提交。
                 </div>
               </div>
             </div>
            
                         {/* 按钮区域 */}
             <div className="flex justify-end gap-3 px-6 py-4 bg-gray-50 border-t border-gray-200">
                <Button
                onClick={() => setShowConfirmDialog(false)}
                  className="bg-blue-600 hover:bg-blue-700 text-white"
                >
                确定
                </Button>
            </div>
          </div>
        </div>
      )}

    </div>
  );
};

export default HotelBookingContent;
