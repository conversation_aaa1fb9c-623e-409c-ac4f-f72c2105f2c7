import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useParams } from 'react-router-dom';
import { 
  Upload, 
  FileSpreadsheet, 
  Download, 
  Train, 
  Check, 
  Trash2, 
  Eye, 
  X, 
  Edit, 
  Save,
  FileText,
  Send,
  PenTool,
  AlertTriangle,
  Search
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import * as XLSX from 'xlsx-js-style';
import { saveAs } from 'file-saver';

// 使用新的API模块
import { 
  projectApi, 
  type Project 
} from '@/api/project';
import { 
  trainOrderApi, 
  type TrainOrder, 
  type ValidationError,
  type ExcelValidationResponse,
  trainOrderApiExtended,
  type CreateBookingTaskRequest
} from '@/api/trainOrder';
import { ProjectTaskService } from '@/services/project';

// 设置全局toast事件监听器
const setupGlobalToastListener = (toast: any) => {
  const handleToast = (event: CustomEvent) => {
    toast(event.detail);
  };
  
  window.addEventListener('showToast', handleToast as EventListener);
  return () => window.removeEventListener('showToast', handleToast as EventListener);
};

// Tab类型定义
type TabType = 'input' | 'text';

// 字段验证规则
const getFieldValidationRule = (field: keyof TrainOrder) => {
  const rules: Record<string, {
    required?: boolean;
    pattern?: RegExp;
    message?: string;
    maxLength?: number;
    minLength?: number;
    enumValues?: string[];
  }> = {
    // 基础必填字段（所有情况下都必填）
    traveler_full_name: { required: true, message: '出行人姓名不能为空', maxLength: 50 },
    id_type: { 
      required: true, 
      message: '证件类型不能为空', 
      maxLength: 20,
      enumValues: ['身份证', '公务护照', '普通护照', '港澳通行证', '台胞证', '回乡证', '军人证', '海员证', '台湾通行证', '外国永久居留证', '港澳台居民居住证', '其他']
    },
    id_number: { 
      required: true, 
      message: '证件号码不能为空',
      maxLength: 50
    },
    mobile_phone: { 
      required: true, 
      pattern: /^1[3-9]\d{9}$/, 
      message: '手机号码格式不正确',
      maxLength: 11
    },
    travel_date: { required: true, message: '出行日期不能为空' },
    departure_station: { required: true, message: '出发站名不能为空', maxLength: 50 },
    arrival_station: { required: true, message: '到达站名不能为空', maxLength: 50 },
    train_number: { required: true, message: '车次不能为空', maxLength: 20 },
    seat_type: { required: true, message: '座位类型不能为空', maxLength: 20 },
    departure_time: { required: true, message: '出发时间不能为空' },
    arrival_time: { required: true, message: '到达时间不能为空' },
    cost_center: { required: true, message: '成本中心不能为空', maxLength: 100 },
    contact_person: { required: true, message: '联系人不能为空', maxLength: 50 },
    contact_phone: { 
      required: true, 
      pattern: /^1[3-9]\d{9}$/, 
      message: '联系人手机号格式不正确',
      maxLength: 11
    },
    approval_reference: { required: true, message: '审批参考人不能为空', maxLength: 50 },
    
    // 证件类型不为身份证时的额外必填字段（动态必填）
    traveler_surname: { maxLength: 25 }, // 动态必填
    traveler_given_name: { maxLength: 25 }, // 动态必填
    nationality: { maxLength: 20 }, // 动态必填
    gender: { 
      maxLength: 10, // 动态必填
      enumValues: ['男', '女']
    }, 
    birth_date: {}, // 动态必填
    id_expiry_date: {}, // 动态必填
    
    // 手机号国际区号（自动补齐为86，非必填）
    mobile_phone_country_code: { maxLength: 10 },
    
    // 非必填字段
    contact_email: { 
      pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/, 
      message: '联系人邮箱格式不正确',
      maxLength: 100
    },
    company_name: { maxLength: 100 },
    booking_agent: { maxLength: 50 },
    order_number: { maxLength: 50 },
    bill_number: { maxLength: 50 },
    amount: { 
      pattern: /^\d+(\.\d{1,2})?$/, 
      message: '金额格式不正确（最多两位小数）'
    },
    sequence_number: { maxLength: 50 },
    trip_submission_item: { maxLength: 200 },
    ticket_sms: { maxLength: 500 }
  };
  
  return rules[field] || {};
};

// 验证单个字段
const validateField = (field: keyof TrainOrder, value: string | number | boolean | null | undefined, idType?: string): string | null => {
  const rule = getFieldValidationRule(field);
  
  // 处理boolean类型字段
  if (typeof value === 'boolean') {
    return null; // boolean字段不需要验证
  }
  
  const stringValue = String(value || '').trim();
  
  // 动态必填字段：当证件类型不为身份证时，这些字段变为必填
  const dynamicRequiredFields = ['traveler_surname', 'traveler_given_name', 'nationality', 'gender', 'birth_date', 'id_expiry_date'];
  let isRequired = rule.required;
  
  if (dynamicRequiredFields.includes(field) && idType && idType !== '身份证') {
    isRequired = true;
  }
  
  // 必填验证
  if (isRequired && !stringValue) {
    const fieldNames: Record<string, string> = {
      // 基础必填字段
      traveler_full_name: '出行人姓名',
      id_type: '证件类型',
      id_number: '证件号码',
      mobile_phone: '手机号',
      travel_date: '出行日期',
      departure_station: '出发站名',
      arrival_station: '到达站名',
      train_number: '车次',
      seat_type: '座位类型',
      departure_time: '出发时间',
      arrival_time: '到达时间',
      cost_center: '成本中心',
      contact_person: '联系人',
      contact_phone: '联系人手机号',
      approval_reference: '审批参考人',
      // 动态必填字段
      traveler_surname: '出行人姓',
      traveler_given_name: '出行人名',
      nationality: '国籍',
      gender: '性别',
      birth_date: '出生日期',
      id_expiry_date: '证件有效期'
    };
    return fieldNames[field] ? `${fieldNames[field]}不能为空` : (rule.message || `${field}不能为空`);
  }
  
  // 如果值为空且非必填，跳过其他验证
  if (!stringValue && !isRequired) {
    return null;
  }
  
  // 长度验证
  if (rule.maxLength && stringValue.length > rule.maxLength) {
    const fieldNames: Record<string, string> = {
      traveler_full_name: '出行人姓名',
      traveler_surname: '出行人姓',
      traveler_given_name: '出行人名',
      nationality: '国籍',
      gender: '性别',
      id_type: '证件类型',
      id_number: '证件号码',
      mobile_phone: '手机号',
      mobile_phone_country_code: '手机号国际区号',
      travel_date: '出行日期',
      departure_station: '出发站名',
      arrival_station: '到达站名',
      train_number: '车次',
      seat_type: '座位类型',
      departure_time: '出发时间',
      arrival_time: '到达时间',
      cost_center: '成本中心',
      contact_person: '联系人',
      contact_phone: '联系人手机号',
      contact_email: '联系人邮箱',
      approval_reference: '审批参考人',
      company_name: '公司名称',
      booking_agent: '代订人',
      order_number: '订单号',
      bill_number: '账单号',
      sequence_number: '差旅单号',
      trip_submission_item: '行程提交项',
      ticket_sms: '出票短信'
    };
    const fieldDisplayName = fieldNames[field] || field;
    return `${fieldDisplayName}长度不能超过${rule.maxLength}个字符`;
  }
  
  if (rule.minLength && stringValue.length < rule.minLength) {
    const fieldNames: Record<string, string> = {
      traveler_full_name: '出行人姓名',
      traveler_surname: '出行人姓',
      traveler_given_name: '出行人名',
      nationality: '国籍',
      gender: '性别',
      id_type: '证件类型',
      id_number: '证件号码',
      mobile_phone: '手机号',
      mobile_phone_country_code: '手机号国际区号',
      travel_date: '出行日期',
      departure_station: '出发站名',
      arrival_station: '到达站名',
      train_number: '车次',
      seat_type: '座位类型',
      departure_time: '出发时间',
      arrival_time: '到达时间',
      cost_center: '成本中心',
      contact_person: '联系人',
      contact_phone: '联系人手机号',
      contact_email: '联系人邮箱',
      approval_reference: '审批参考人',
      company_name: '公司名称',
      booking_agent: '代订人',
      order_number: '订单号',
      bill_number: '账单号',
      sequence_number: '差旅单号',
      trip_submission_item: '行程提交项',
      ticket_sms: '出票短信'
    };
    const fieldDisplayName = fieldNames[field] || field;
    return `${fieldDisplayName}长度不能少于${rule.minLength}个字符`;
  }
  
  // 格式验证
  if (rule.pattern && !rule.pattern.test(stringValue)) {
    return rule.message || `${field}格式不正确`;
  }
  
  // 身份证号码特殊验证：只在证件类型为身份证时验证格式
  if (field === 'id_number' && stringValue && idType === '身份证') {
    const idNumberPattern = /^[0-9Xx]{15}$|^[0-9Xx]{18}$/;
    if (!idNumberPattern.test(stringValue)) {
      return '身份证号码格式不正确（15位或18位数字或字母X）';
    }
  }
  
  // 枚举值验证
  if (rule.enumValues && stringValue && !rule.enumValues.includes(stringValue)) {
    const fieldNames: Record<string, string> = {
      id_type: '证件类型',
      gender: '性别'
    };
    const fieldDisplayName = fieldNames[field] || field;
    return `${fieldDisplayName}必须是以下值之一：${rule.enumValues.join('、')}`;
  }
  
  return null;
};

// 可编辑单元格组件
const EditableCell: React.FC<{
  value: string | number | null | undefined;
  field: keyof TrainOrder;
  type?: 'text' | 'number' | 'date';
  isEditing: boolean;
  onFieldChange: (field: keyof TrainOrder, value: string | number) => void;
  validationErrors?: Record<string, string>;
  editingOrder?: TrainOrder | null;
}> = ({ value, field, type = 'text', isEditing, onFieldChange, validationErrors, editingOrder }) => {
  const [localError, setLocalError] = useState<string | null>(null);
  
  const hasError = localError || validationErrors?.[field];
  
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const newValue = type === 'number' ? parseFloat(e.target.value) || 0 : e.target.value;
    
    // 实时验证，传递证件类型信息
    const error = validateField(field, newValue, editingOrder?.id_type);
    setLocalError(error);
    
    onFieldChange(field, newValue);
  };

  const handleBlur = () => {
    // 失焦时再次验证，传递证件类型信息
    const error = validateField(field, value, editingOrder?.id_type);
    setLocalError(error);
  };

  // 获取字段的验证规则
  const rule = getFieldValidationRule(field);
  const isSelectField = rule.enumValues && rule.enumValues.length > 0;

  if (!isEditing) {
    // 特殊格式化金额字段
    if (field === 'amount' && value) {
      return <span>¥{value}</span>;
    }
    return <span>{value || '-'}</span>;
  }

  return (
    <div className="w-full">
      {isSelectField ? (
        <select
          value={value || ''}
          onChange={handleChange}
          onBlur={handleBlur}
          className={`w-full px-2 py-1 border rounded text-sm focus:ring-2 focus:border-transparent ${
            hasError 
              ? 'border-red-300 focus:ring-red-500 bg-red-50' 
              : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'
          }`}
        >
          <option value="">请选择</option>
          {rule.enumValues?.map(optionValue => (
            <option key={optionValue} value={optionValue}>
              {optionValue}
            </option>
          ))}
        </select>
      ) : (
        <input
          type={type}
          value={value || ''}
          onChange={handleChange}
          onBlur={handleBlur}
          className={`w-full px-2 py-1 border rounded text-sm focus:ring-2 focus:border-transparent ${
            hasError 
              ? 'border-red-300 focus:ring-red-500 bg-red-50' 
              : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'
          }`}
        />
      )}
      {hasError && (
        <div className="mt-1">
          <div className="text-red-600 text-xs">
            {hasError}
          </div>
        </div>
      )}
    </div>
  );
};

interface TrainBookingContentProps {
  onNavigateToAllOrders?: () => void;
}

const TrainBookingContent: React.FC<TrainBookingContentProps> = ({ onNavigateToAllOrders }) => {
  const { projectId } = useParams<{ projectId: string }>();
  const { toast } = useToast();
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  const [project, setProject] = useState<Project | null>(null);
  const [loading, setLoading] = useState(true);
  const [orders, setOrders] = useState<TrainOrder[]>([]);
  const [uploading, setUploading] = useState(false);
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [activeTab, setActiveTab] = useState<TabType>('input');
  const [textInput, setTextInput] = useState('');
  
  // 验证相关状态
  const [validating, setValidating] = useState(false);
  const [validationResult, setValidationResult] = useState<ExcelValidationResponse | null>(null);
  const [showValidationModal, setShowValidationModal] = useState(false);
  
  // 详情弹窗状态
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState<TrainOrder | null>(null);
  
  // 编辑模式状态
  const [isEditMode, setIsEditMode] = useState(false);
  const [editingOrder, setEditingOrder] = useState<TrainOrder | null>(null);
  const [saving, setSaving] = useState(false);
  
  // 添加验证错误状态
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  
  // 分页状态
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  const [pageLoading, setPageLoading] = useState(false);

  // 订单状态管理
  const [taskStatus, setTaskStatus] = useState<string>('initial');
  const [submittingOrder, setSubmittingOrder] = useState(false);
  const [isOrderSubmitted, setIsOrderSubmitted] = useState(false);

  // 删除操作状态管理
  const [deletingOrderId, setDeletingOrderId] = useState<number | null>(null);
  const [clearingOrders, setClearingOrders] = useState(false);

  // 搜索相关状态
  const [searchTravelerName, setSearchTravelerName] = useState('');
  const [searchMobilePhone, setSearchMobilePhone] = useState('');
  const [searchContactPhone, setSearchContactPhone] = useState('');

  // 状态筛选相关状态 - 默认全部选中
  const [selectedStatuses, setSelectedStatuses] = useState<string[]>(['initial', 'check_failed', 'failed']);

  // 自定义确认对话框状态
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [confirmDialogData, setConfirmDialogData] = useState<{
    title: string;
    checkFailedOrders: TrainOrder[];
    bookingFailedOrders: TrainOrder[];
  } | null>(null);

  // 添加通用确认对话框状态
  const [showGeneralConfirmDialog, setShowGeneralConfirmDialog] = useState(false);
  const [generalConfirmDialogData, setGeneralConfirmDialogData] = useState<{
    title: string;
    message: string;
    confirmText: string;
    cancelText: string;
    onConfirm: () => void;
    type: 'info' | 'warning' | 'danger';
  } | null>(null);

  // 从localStorage读取全局设置的fallback函数
  const getLocalStorageSettings = () => {
    try {
      const data = localStorage.getItem('train_booking_settings');
      if (data) {
        return JSON.parse(data);
      }
    } catch {}
    return {};
  };

  // 获取项目的历史任务设置，优先级：项目历史 > localStorage > 默认值
  const getProjectBookingSettings = async (projectId: string) => {
    try {
      // 首先尝试从项目历史火车票任务获取设置
      const projectSettings = await ProjectTaskService.getProjectLatestSettings(parseInt(projectId), '火车票预订');
      
      if (projectSettings.has_history) {
        console.log('🔍 已获取项目火车票预订历史任务设置:', projectSettings.message);
        return {
          smsNotify: projectSettings.sms_notify,
          hasAgent: projectSettings.has_agent,
          agentPhone: projectSettings.agent_phone || '',
          source: 'project_history',
          lastTaskTitle: projectSettings.last_task_title
        };
      }
    } catch (error) {
      console.warn('获取项目火车票预订历史设置失败，将使用localStorage设置:', error);
    }
    
    // 如果项目没有历史任务，fallback到localStorage
    const localSettings = getLocalStorageSettings();
    return {
      smsNotify: localSettings.smsNotify ?? false,
      hasAgent: localSettings.hasAgent ?? false,
      agentPhone: localSettings.agentPhone ?? '',
      source: 'localStorage'
    };
  };

  // 初始化设置状态 - 使用默认值，稍后异步加载
  const [smsNotify, setSmsNotify] = useState(false);
  const [hasAgent, setHasAgent] = useState(false);
  const [agentPhone, setAgentPhone] = useState('');
  const [settingsSource, setSettingsSource] = useState<'project_history' | 'localStorage' | 'default'>('default');

  // 旧的localStorage读取函数保留用于fallback（在getLocalStorageSettings中使用）

  // 避免未使用变量警告
  void project;
  void taskStatus;
  void submittingOrder;
  void isOrderSubmitted;

  // 设置全局toast监听
  useEffect(() => {
    return setupGlobalToastListener(toast);
  }, [toast]);

  // 在项目ID可用时加载项目设置
  useEffect(() => {
    if (projectId) {
      loadProjectSettings();
    }
  }, [projectId]);

  // 加载项目设置的函数
  const loadProjectSettings = async () => {
    if (!projectId) return;
    
    try {
      const settings = await getProjectBookingSettings(projectId);
      setSmsNotify(settings.smsNotify);
      setHasAgent(settings.hasAgent);
      setAgentPhone(settings.agentPhone);
      setSettingsSource(settings.source as 'project_history' | 'localStorage');
      
      if (settings.source === 'project_history' && settings.lastTaskTitle) {
        console.log(`✅ 已加载项目火车票预订历史设置（来自任务：${settings.lastTaskTitle}）`);
      } else if (settings.source === 'localStorage') {
        console.log('📱 已加载localStorage设置');
      }
    } catch (error) {
      console.warn('加载项目火车票预订设置失败，使用默认值:', error);
      // 如果出错，使用默认值
      setSmsNotify(false);
      setHasAgent(false);
      setAgentPhone('');
      setSettingsSource('default');
    }
  };

  // 显示通用确认对话框
  const showGeneralConfirm = (options: {
    title: string;
    message: string;
    confirmText?: string;
    cancelText?: string;
    onConfirm: () => void;
    type?: 'info' | 'warning' | 'danger';
  }) => {
    setGeneralConfirmDialogData({
      title: options.title,
      message: options.message,
      confirmText: options.confirmText || '确定',
      cancelText: options.cancelText || '取消',
      onConfirm: options.onConfirm,
      type: options.type || 'info'
    });
    setShowGeneralConfirmDialog(true);
  };

  // 处理通用确认对话框的确认操作
  const handleGeneralConfirm = () => {
    if (generalConfirmDialogData?.onConfirm) {
      generalConfirmDialogData.onConfirm();
    }
    setShowGeneralConfirmDialog(false);
    setGeneralConfirmDialogData(null);
  };

  // 处理通用确认对话框的取消操作
  const handleGeneralCancel = () => {
    setShowGeneralConfirmDialog(false);
    setGeneralConfirmDialogData(null);
  };

  useEffect(() => {
    if (projectId) {
      loadProjectData();
      loadTrainOrders(1);
    }
  }, [projectId]);

  // 监听状态筛选变化
  useEffect(() => {
    if (projectId) {
      setCurrentPage(1);
      loadTrainOrders(1);
    }
  }, [selectedStatuses]);

  // 监听pageSize变化
  useEffect(() => {
    if (projectId) {
      setCurrentPage(1);
      loadTrainOrders(1);
    }
  }, [pageSize]);

  const loadProjectData = async () => {
    if (!projectId) return;
    
    try {
      const response = await projectApi.getProject(projectId);
      setProject(response.data);
    } catch (error) {
      console.error('加载项目失败:', error);
      // toast已经在统一请求函数中处理
    } finally {
      setLoading(false);
    }
  };

  const loadTrainOrders = async (page: number) => {
    if (!projectId) return;
    
    setPageLoading(true);
    try {
      const response = await trainOrderApi.getOrdersByProject(
        projectId, 
        page, 
        pageSize,
        selectedStatuses.join(','), // 使用动态状态筛选
        searchTravelerName || undefined,
        searchMobilePhone || undefined,
        searchContactPhone || undefined,
        true // 启用后端失败订单优先排序
      );
      
      // 直接使用后端返回的已排序数据，不需要前端再次排序
      setOrders(response.data.items);
      setCurrentPage(response.data.page);
      setTotalCount(response.data.total);
      
      // 由于后端默认只返回initial状态的订单，简化状态检查
      if (response.data.items.length > 0) {
        setTaskStatus('initial');
        setIsOrderSubmitted(false);
      }
    } catch (error) {
      console.error('加载订单失败:', error);
      // toast已经在统一请求函数中处理
    } finally {
      setPageLoading(false);
    }
  };

  // 搜索处理函数
  const handleSearch = () => {
    setCurrentPage(1); // 重置到第一页
    loadTrainOrders(1);
  };

  // 清空搜索条件
  const handleClearSearch = () => {
    setSearchTravelerName('');
    setSearchMobilePhone('');
    setSearchContactPhone('');
    setSelectedStatuses(['initial', 'check_failed', 'failed']); // 重置状态筛选为全部选中
    setCurrentPage(1);
    loadTrainOrders(1);
  };

  // 状态筛选处理函数
  const handleStatusChange = (status: string, checked: boolean) => {
    if (checked) {
      setSelectedStatuses(prev => [...prev, status]);
    } else {
      setSelectedStatuses(prev => prev.filter(s => s !== status));
    }
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setUploadedFile(file);
    }
  };

  const removeUploadedFile = () => {
    setUploadedFile(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // 删除未使用的函数

  const uploadExcelFile = async () => {
    if (!uploadedFile || !projectId) {
      toast({
        title: "错误",
        description: "请选择要上传的文件",
        variant: "destructive",
      });
      return;
    }

    setUploading(true);
    try {
      await trainOrderApi.uploadExcel(projectId, uploadedFile, smsNotify);
      
      toast({
        title: "上传成功",
        description: `成功上传火车票订单`,
      });
      
      // 设置订单状态为初始状态
      setTaskStatus('initial');
      setIsOrderSubmitted(false);
      
      // 重新加载订单列表
      await loadTrainOrders(1);
      
      // 清空上传状态
      setUploadedFile(null);
      // 重新同步localStorage到UI
      const sync = getLocalStorageSettings();
      setSmsNotify(sync.smsNotify ?? false);
      setHasAgent(sync.hasAgent ?? false);
      setAgentPhone(sync.agentPhone ?? '');
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    } catch (error: any) {
      console.error('上传失败:', error);
      // 错误提示已在统一请求函数中处理，这里不需要重复显示
    } finally {
      setUploading(false);
    }
  };

  // 合并验证和上传的处理函数
  const handleValidateAndUpload = async () => {
    if (!uploadedFile || !projectId) {
      toast({
        title: "错误",
        description: "请选择要上传的文件",
        variant: "destructive",
      });
      return;
    }

    setValidating(true);
    setUploading(true);
    try {
      // 先验证文件
      const validationResponse = await trainOrderApi.validateExcel(projectId, uploadedFile, smsNotify);
      setValidationResult(validationResponse.data);
      
      if (validationResponse.data.has_errors) {
        setShowValidationModal(true);
        toast({
          title: "验证发现问题",
          description: validationResponse.data.message,
          variant: "destructive",
        });
        return;
      }
      
      // 验证通过，直接上传
      const uploadResponse = await trainOrderApi.uploadExcel(projectId, uploadedFile, smsNotify);
      
      toast({
        title: "上传成功",
        description: uploadResponse.data.message || `成功验证并上传 ${uploadResponse.data.total_orders || '若干'} 条火车票订单`,
      });
      
      // 设置订单状态为初始状态
      setTaskStatus('initial');
      setIsOrderSubmitted(false);
      
      // 重新加载订单列表
      await loadTrainOrders(1);
      
      // 清空上传状态
      setUploadedFile(null);
      setValidationResult(null);
      // 重新同步localStorage到UI
      const sync = getLocalStorageSettings();
      setSmsNotify(sync.smsNotify ?? false);
      setHasAgent(sync.hasAgent ?? false);
      setAgentPhone(sync.agentPhone ?? '');
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
      
    } catch (error: any) {
      console.error('❌ 验证或上传失败:', error);
      // 错误提示已在统一请求函数中处理，这里不需要重复显示
    } finally {
      setValidating(false);
      setUploading(false);
    }
  };

  // 获取所有符合当前筛选条件的订单
  const getAllFilteredOrders = async () => {
    if (!projectId) return [];
    
    try {
      // 使用一个很大的页面大小来获取所有数据，但后端限制为100，所以需要分批获取
      let allOrders: TrainOrder[] = [];
      let currentPage = 1;
      const maxPageSize = 100; // 后端限制的最大页面大小
      
      while (true) {
        const response = await trainOrderApi.getOrdersByProject(
          projectId, 
          currentPage, 
          maxPageSize,
          selectedStatuses.join(','),
          searchTravelerName || undefined,
          searchMobilePhone || undefined,
          searchContactPhone || undefined,
          true // 启用失败订单优先排序
        );
        
        allOrders = allOrders.concat(response.data.items);
        
        // 如果返回的数据少于页面大小，说明已经是最后一页
        if (response.data.items.length < maxPageSize) {
          break;
        }
        
        currentPage++;
      }
      
      return allOrders;
    } catch (error) {
      console.error('获取所有订单失败:', error);
      return [];
    }
  };

  // 删除了未使用的checkForFailedOrders函数

  // 预订订单
  const handleGenerateOrder = async () => {
    // 使用当前页面显示的订单而不是所有符合筛选条件的订单
    const currentPageOrders = orders;
    
    if (currentPageOrders.length === 0) {
      toast({
        title: "提示",
        description: "当前页面暂无订单数据",
        variant: "default",
      });
      return;
    }

    // 检查当前页面的订单中是否有验证失败或预定失败的订单
    const checkFailedOrders = currentPageOrders.filter(order => order.order_status === 'check_failed');
    const bookingFailedOrders = currentPageOrders.filter(order => order.order_status === 'failed');
    
    if (checkFailedOrders.length > 0 || bookingFailedOrders.length > 0) {
      // 使用自定义对话框替代alert
      setConfirmDialogData({
        title: '无法提交预订',
        checkFailedOrders,
        bookingFailedOrders
      });
      setShowConfirmDialog(true);
      return;
    }

    // 使用自定义确认对话框替换系统默认confirm
    showGeneralConfirm({
      title: '确认预订',
      message: `确定要预订当前页面的 ${currentPageOrders.length} 条火车票订单吗？`,
      confirmText: '确认预订',
      cancelText: '取消',
      type: 'info',
      onConfirm: async () => {
        // 保存设置到localStorage
        localStorage.setItem('train_booking_settings', JSON.stringify({
          smsNotify,
          hasAgent,
          agentPhone
        }));
        setSubmittingOrder(true);
        try {
          // 使用新的创建预订任务API
          const taskData: CreateBookingTaskRequest = {
            booking_type: 'book_only',
            task_title: `火车票预订 - ${new Date().toLocaleDateString()}`,
            task_description: `预订 ${currentPageOrders.length} 条火车票订单`,
            sms_notify: smsNotify,
            has_agent: hasAgent,
            agent_phone: hasAgent ? agentPhone : undefined,
            order_ids: currentPageOrders.map(order => order.id) // 传递当前页面订单的ID列表
          };

          const response = await trainOrderApiExtended.createBookingTask(projectId!, taskData);
          
          setTaskStatus('submitted');
          setIsOrderSubmitted(true);
          
          await loadTrainOrders(currentPage);
          
          toast({
            title: "预订成功",
            description: response.data.message,
            variant: "success",
          });
          
          // 预订成功后跳转到所有订单标签页
          if (onNavigateToAllOrders) {
            setTimeout(() => {
              onNavigateToAllOrders();
            }, 1000); // 延迟1秒后跳转，让用户看到成功提示
          }
        } catch (error) {
          console.error('预订失败:', error);
          // toast已经在统一请求函数中处理
        } finally {
          setSubmittingOrder(false);
        }
      }
    });
  };

  // 预订且出票
  const handleSubmitOrder = async () => {
    // 使用当前页面显示的订单而不是所有符合筛选条件的订单
    const currentPageOrders = orders;
    
    if (currentPageOrders.length === 0) {
      toast({
        title: "提示",
        description: "当前页面暂无订单数据",
        variant: "default",
      });
      return;
    }

    // 检查当前页面的订单中是否有验证失败或预定失败的订单
    const checkFailedOrders = currentPageOrders.filter(order => order.order_status === 'check_failed');
    const bookingFailedOrders = currentPageOrders.filter(order => order.order_status === 'failed');
    
    if (checkFailedOrders.length > 0 || bookingFailedOrders.length > 0) {
      // 使用自定义对话框替代alert
      setConfirmDialogData({
        title: '无法提交预订',
        checkFailedOrders,
        bookingFailedOrders
      });
      setShowConfirmDialog(true);
      return;
    }

    // 使用自定义确认对话框替换系统默认confirm
    showGeneralConfirm({
      title: '确认预订且出票',
      message: `确定要预订且出票当前页面的 ${currentPageOrders.length} 条火车票订单吗？`,
      confirmText: '确认预订且出票',
      cancelText: '取消',
      type: 'warning',
      onConfirm: async () => {
        // 保存设置到localStorage
        localStorage.setItem('train_booking_settings', JSON.stringify({
          smsNotify,
          hasAgent,
          agentPhone
        }));
        setSubmittingOrder(true);
        try {
          // 使用新的创建预订任务API
          const taskData: CreateBookingTaskRequest = {
            booking_type: 'book_and_ticket',
            task_title: `火车票预订且出票 - ${new Date().toLocaleDateString()}`,
            task_description: `预订且出票 ${currentPageOrders.length} 条火车票订单`,
            sms_notify: smsNotify,
            has_agent: hasAgent,
            agent_phone: hasAgent ? agentPhone : undefined,
            order_ids: currentPageOrders.map(order => order.id) // 传递当前页面订单的ID列表
          };

          const response = await trainOrderApiExtended.createBookingTask(projectId!, taskData);
          
          setTaskStatus('submitted');
          setIsOrderSubmitted(true);
          
          await loadTrainOrders(currentPage);
          
          toast({
            title: "预订且出票成功",
            description: response.data.message,
            variant: "success",
          });
          
          // 预订且出票成功后跳转到所有订单标签页
          if (onNavigateToAllOrders) {
            setTimeout(() => {
              onNavigateToAllOrders();
            }, 1000); // 延迟1秒后跳转，让用户看到成功提示
          }
        } catch (error) {
          console.error('预订且出票失败:', error);
          // toast已经在统一请求函数中处理
        } finally {
          setSubmittingOrder(false);
        }
      }
    });
  };

  // 详情弹窗相关函数
  const openDetailModal = (order: TrainOrder) => {
    setSelectedOrder(order);
    setEditingOrder({ ...order });
    setIsEditMode(false);
    setValidationErrors({}); // 清空验证错误，确保详情模式干净状态
    setShowDetailModal(true);
  };

  const enterEditMode = () => {
    setIsEditMode(true);
  };

  const cancelEdit = () => {
    if (selectedOrder) {
      setEditingOrder(selectedOrder);
      setIsEditMode(false);
      setValidationErrors({}); // 清空验证错误
    }
  };

  const handleFieldChange = useCallback((field: keyof TrainOrder, value: string | number) => {
    if (editingOrder) {
      setEditingOrder({
        ...editingOrder,
        [field]: value
      });
      
      // 实时验证当前字段，传递证件类型用于动态必填判断
      const error = validateField(field, value, editingOrder.id_type);
      setValidationErrors(prev => {
        if (error) {
          return { ...prev, [field]: error };
        } else {
          const { [field]: removed, ...rest } = prev;
          return rest;
        }
      });
    }
  }, [editingOrder]);

  // 验证所有必填字段
  const validateAllFields = (order: TrainOrder): Record<string, string> => {
    const errors: Record<string, string> = {};
    
    // 基础必填字段（所有情况下都必填）
    const basicRequiredFields: (keyof TrainOrder)[] = [
      'traveler_full_name', 'id_type', 'id_number', 'mobile_phone', 'travel_date',
      'departure_station', 'arrival_station', 'train_number', 'seat_type', 
      'departure_time', 'arrival_time', 'cost_center', 'contact_person', 
      'contact_phone', 'approval_reference'
    ];
    
    // 证件类型不为身份证时的额外必填字段
    const dynamicRequiredFields: (keyof TrainOrder)[] = [
      'traveler_surname', 'traveler_given_name', 'nationality', 
      'gender', 'birth_date', 'id_expiry_date'
    ];
    
    // 验证基础必填字段
    basicRequiredFields.forEach(field => {
      const error = validateField(field, order[field], order.id_type);
      if (error) {
        errors[field] = error;
      }
    });
    
    // 验证动态必填字段（仅当证件类型不为身份证时）
    if (order.id_type && order.id_type !== '身份证') {
      dynamicRequiredFields.forEach(field => {
        const error = validateField(field, order[field], order.id_type);
        if (error) {
          errors[field] = error;
        }
      });
    }
    
    // 验证非必填字段的格式（如果有值的话）
    const optionalFields: (keyof TrainOrder)[] = ['contact_email', 'amount'];
    optionalFields.forEach(field => {
      if (order[field]) {
        const error = validateField(field, order[field], order.id_type);
        if (error) {
          errors[field] = error;
        }
      }
    });
    
    return errors;
  };

  const saveChanges = async () => {
    if (!editingOrder) return;

    // 保存前进行完整验证
    const errors = validateAllFields(editingOrder);
    setValidationErrors(errors);
    
    if (Object.keys(errors).length > 0) {
      toast({
        title: "验证失败",
        description: `请修正 ${Object.keys(errors).length} 个字段的错误后再保存`,
        variant: "destructive",
      });
      return;
    }

    setSaving(true);
    try {
      // 直接保存订单数据，后端会自动进行验证并设置状态
      const response = await trainOrderApi.updateOrder(editingOrder.id, editingOrder);
      
      // 更新本地数据
      setOrders(prevOrders => 
        prevOrders.map(order => 
          order.id === response.data.id ? response.data : order
        )
      );
      
      setSelectedOrder(response.data);
      setEditingOrder(response.data);
      setIsEditMode(false);
      setValidationErrors({}); // 清空验证错误
      
      // 根据订单状态显示不同的提示信息
      if (response.data.order_status === 'check_failed') {
        toast({
          title: "保存完成",
          description: `${response.data.traveler_full_name} 的订单信息已保存，但验证失败：${response.data.fail_reason || '请检查数据格式'}`,
          variant: "destructive",
        });
      } else if (response.data.order_status === 'initial') {
        toast({
          title: "保存成功",
          description: `${response.data.traveler_full_name} 的订单信息已成功更新，状态已改为待提交`,
          variant: "default",
        });
      } else {
        toast({
          title: "保存成功",
          description: `${response.data.traveler_full_name} 的订单信息已成功更新`,
          variant: "default",
        });
      }
    } catch (error) {
      console.error('保存失败:', error);
      // toast已经在统一请求函数中处理
    } finally {
      setSaving(false);
    }
  };

  // 删除单个订单
  // 处理编辑订单 - 直接打开编辑模式弹窗
  const handleEditOrder = (order: TrainOrder) => {
    setSelectedOrder(order);
    setEditingOrder({ ...order });
    setIsEditMode(true);
    setValidationErrors({}); // 清空验证错误，确保编辑模式干净状态
    setShowDetailModal(true); // 显示编辑模态框
  };

  const handleDeleteOrder = async (orderId: number, orderName: string) => {
    // 使用自定义确认对话框替换系统默认confirm
    showGeneralConfirm({
      title: '确认删除订单',
      message: `确定要删除 ${orderName} 的订单吗？删除后将从数据库中永久移除，无法恢复。`,
      confirmText: '确认删除',
      cancelText: '取消',
      type: 'danger',
      onConfirm: async () => {
        setDeletingOrderId(orderId);
        try {
          await trainOrderApi.deleteOrder(orderId);
          
          toast({
            title: "删除成功",
            description: `已成功删除 ${orderName} 的订单`,
          });
          
          await loadTrainOrders(currentPage);
        } catch (error) {
          console.error('删除订单失败:', error);
          // toast已经在统一请求函数中处理
        } finally {
          setDeletingOrderId(null);
        }
      }
    });
  };

  // 获取订单状态显示
  const getOrderStatusDisplay = (status: string) => {
    switch (status) {
      case 'check_failed':
        return <span className="px-2 py-1 bg-amber-100 text-amber-700 rounded-full text-xs">验证失败</span>;
      case 'initial':
        return <span className="px-2 py-1 bg-gray-100 text-gray-600 rounded-full text-xs">待提交</span>;
      case 'submitted':
        return <span className="px-2 py-1 bg-blue-100 text-blue-700 rounded-full text-xs">已提交</span>;
      case 'processing':
        return <span className="px-2 py-1 bg-cyan-100 text-cyan-700 rounded-full text-xs">处理中</span>;
      case 'completed':
        return <span className="px-2 py-1 bg-green-100 text-green-700 rounded-full text-xs">预定完成</span>;
      case 'failed':
        return <span className="px-2 py-1 bg-red-100 text-red-700 rounded-full text-xs">预定失败</span>;
      default:
        return <span className="px-2 py-1 bg-gray-100 text-gray-600 rounded-full text-xs">{status}</span>;
    }
  };

  // 获取订单状态文本（用于导出）
  const getOrderStatusText = (status: string) => {
    switch (status) {
      case 'check_failed':
        return '验证失败';
      case 'initial':
        return '待提交';
      case 'submitted':
        return '已提交';
      case 'processing':
        return '处理中';
      case 'completed':
        return '已完成';
      case 'failed':
        return '预定失败';
      default:
        return status || '未知';
    }
  };

  // 格式化日期时间（用于导出）
  const formatDateTime = (dateString: string | null | undefined) => {
    if (!dateString) return '-';
    try {
      const date = new Date(dateString);
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
    } catch {
      return dateString;
    }
  };

  // 清空所有符合筛选条件的订单
  const handleClearOrders = async () => {
    // 获取所有符合筛选条件的订单
    const allOrders = await getAllFilteredOrders();
    console.log('🗑️ 点击清空订单按钮，筛选后订单数量:', allOrders.length);
    
    if (allOrders.length === 0) {
      console.log('❌ 暂无订单，显示提示');
      toast({
        title: "提示",
        description: "当前筛选条件下暂无订单需要清空",
        variant: "default",
      });
      return;
    }

    // 使用自定义确认对话框替换系统默认confirm
    showGeneralConfirm({
      title: '确认清空订单',
      message: `确定要清空当前筛选条件下的所有 ${allOrders.length} 条订单吗？删除后将从数据库中永久移除，无法恢复。`,
      confirmText: '确认清空',
      cancelText: '取消',
      type: 'danger',
      onConfirm: async () => {
        console.log('⏳ 开始清空订单，项目ID:', projectId);
        setClearingOrders(true);
        try {
          // 注意：这里需要修改后端API支持按筛选条件删除，目前只能清空所有initial状态的订单
          // 暂时使用现有API，但提示用户实际的操作范围
          const response = await trainOrderApi.clearInitialOrders(projectId!);
          console.log('✅ 清空订单成功，响应:', response);
          
          toast({
            title: "清空成功",
            description: `已成功清空 ${response.data.deleted_count} 条待提交订单`,
          });
          
          await loadTrainOrders(1);
          
          setTaskStatus('initial');
          setIsOrderSubmitted(false);
        } catch (error) {
          console.error('❌ 清空订单失败:', error);
          // toast已经在统一请求函数中处理
        } finally {
          setClearingOrders(false);
          console.log('🏁 清空订单操作结束');
        }
      }
    });
  };

  // 下载Excel模板
  const handleDownloadTemplate = () => {
    try {
      // 创建一个隐藏的链接元素来触发下载
      const link = document.createElement('a');
      link.href = '/templates/train_template.xlsx';
      link.download = 'train_template.xlsx';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      toast({
        title: "下载成功",
        description: "Excel模板文件已开始下载",
        variant: "default",
      });
    } catch (error) {
      console.error('下载模板失败:', error);
      toast({
        title: "下载失败",
        description: "下载Excel模板时发生错误，请重试",
        variant: "destructive",
      });
    }
  };

  // 处理文本粘贴解析
  const handleTextParse = () => {
    if (!textInput.trim()) {
      toast({
        title: "提示",
        description: "请输入要解析的文本内容",
        variant: "default",
      });
      return;
    }

    // 这里可以添加文本解析逻辑
    toast({
      title: "功能开发中",
      description: "文本粘贴解析功能正在开发中...",
      variant: "default",
    });
  };

  // 导出Excel
  const handleExportExcel = async () => {
    // 获取所有符合筛选条件的订单
    const allOrders = await getAllFilteredOrders();
    
    if (allOrders.length === 0) {
      toast({
        title: "无数据",
        description: "当前筛选条件下暂无订单数据可导出",
        variant: "destructive",
      });
      return;
    }

    try {
      // 准备导出数据 - 包含所有数据库字段
      const exportData = allOrders.map((order, index) => ({
        '序号': order.sequence_number || index + 1,
        '订单状态': getOrderStatusText(order.order_status),
        '出行人姓名': order.traveler_full_name || '-',
        '出行人姓': order.traveler_surname || '-',
        '出行人名': order.traveler_given_name || '-',
        '国籍': order.nationality || '-',
        '性别': order.gender || '-',
        '出生日期': order.birth_date || '-',
        '证件类型': order.id_type || '-',
        '证件号码': order.id_number || '-',
        '证件有效期至': order.id_expiry_date || '-',
        '手机号': order.mobile_phone || '-',
        '手机号国际区号': order.mobile_phone_country_code || '-',
        '出行日期': order.travel_date || '-',
        '出发站名': order.departure_station || '-',
        '到达站名': order.arrival_station || '-',
        '车次': order.train_number || '-',
        '座位类型': order.seat_type || '-',
        '出发时间': order.departure_time || '-',
        '到达时间': order.arrival_time || '-',
        '成本中心': order.cost_center || '-',
        '行程提交项': order.trip_submission_item || '-',
        '联系人': order.contact_person || '-',
        '联系人手机号': order.contact_phone || '-',
        '联系人邮箱': order.contact_email || '-',
        '审批参考人': order.approval_reference || '-',
        '公司名称': order.company_name || '-',
        '代订人': order.booking_agent || '-',
        '出票短信': order.ticket_sms || '-',
        '金额': order.amount || 0,
        '订单号': order.order_number || '-',
        '账单号': order.bill_number || '-',
        '失败原因': order.fail_reason || '-',
        '创建时间': formatDateTime(order.created_at),
        '更新时间': formatDateTime(order.updated_at)
      }));

      // 获取列名
      const headers = Object.keys(exportData[0] || {});
      
      // 创建空工作表
      const ws = XLSX.utils.aoa_to_sheet([]);
      
      // 添加标题行
      const title = `${project?.project_name || '项目' + projectId} - 待预定订单明细`;
      
      // 第一行：标题
      XLSX.utils.sheet_add_aoa(ws, [[title]], { origin: 'A1' });
      
      // 合并标题行单元格
      ws['!merges'] = [{ s: { r: 0, c: 0 }, e: { r: 0, c: headers.length - 1 } }];
      
      // 第二行：列名
      XLSX.utils.sheet_add_aoa(ws, [headers], { origin: 'A2' });
      
      // 第三行开始：数据
      const dataRows = exportData.map(row => headers.map(header => (row as any)[header]));
      XLSX.utils.sheet_add_aoa(ws, dataRows, { origin: 'A3' });
      
      // 设置列宽
      const colWidths = [
        { wch: 6 },   // 序号
        { wch: 10 },  // 订单状态
        { wch: 12 },  // 出行人姓名
        { wch: 10 },  // 出行人姓
        { wch: 10 },  // 出行人名
        { wch: 8 },   // 国籍
        { wch: 6 },   // 性别
        { wch: 12 },  // 出生日期
        { wch: 10 },  // 证件类型
        { wch: 18 },  // 证件号码
        { wch: 12 },  // 证件有效期至
        { wch: 12 },  // 手机号
        { wch: 8 },   // 手机号国际区号
        { wch: 12 },  // 出行日期
        { wch: 12 },  // 出发站名
        { wch: 12 },  // 到达站名
        { wch: 10 },  // 车次
        { wch: 10 },  // 座位类型
        { wch: 10 },  // 出发时间
        { wch: 10 },  // 到达时间
        { wch: 15 },  // 成本中心
        { wch: 15 },  // 行程提交项
        { wch: 10 },  // 联系人
        { wch: 12 },  // 联系人手机号
        { wch: 20 },  // 联系人邮箱
        { wch: 12 },  // 审批参考人
        { wch: 15 },  // 公司名称
        { wch: 10 },  // 代订人
        { wch: 15 },  // 出票短信
        { wch: 10 },  // 金额
        { wch: 15 },  // 订单号
        { wch: 15 },  // 账单号
        { wch: 20 },  // 失败原因
        { wch: 18 },  // 创建时间
        { wch: 18 }   // 更新时间
      ];
      ws['!cols'] = colWidths;

      // 获取工作表范围
      const range = XLSX.utils.decode_range(ws['!ref'] || 'A1');
      
      // 定义完整的边框样式
      const borderAll = {
        style: 'thin',
        color: { rgb: '000000' }
      };
      
      // 为所有单元格添加边框和样式
      for (let row = range.s.r; row <= range.e.r; row++) {
        for (let col = range.s.c; col <= range.e.c; col++) {
          const cellAddress = XLSX.utils.encode_cell({ r: row, c: col });
          if (!ws[cellAddress]) ws[cellAddress] = { t: 's', v: '' };
          
          // 设置单元格样式
          ws[cellAddress].s = {
            border: {
              top: borderAll,
              bottom: borderAll,
              left: borderAll,
              right: borderAll
            },
            alignment: { 
              horizontal: 'left', 
              vertical: 'center',
              wrapText: true 
            },
            font: { 
              name: '微软雅黑', 
              sz: 10 
            }
          };
          
          // 为标题行添加特殊样式
          if (row === 0) {
            ws[cellAddress].s.fill = {
              patternType: 'solid',
              fgColor: { rgb: 'E3F2FD' }
            };
            ws[cellAddress].s.font = {
              name: '微软雅黑',
              sz: 14,
              bold: true
            };
            ws[cellAddress].s.alignment = {
              horizontal: 'center',
              vertical: 'center',
              wrapText: true
            };
          }
          
          // 为列名行添加特殊样式
          else if (row === 1) {
            ws[cellAddress].s.fill = {
              patternType: 'solid',
              fgColor: { rgb: 'F3F4F6' }
            };
            ws[cellAddress].s.font = {
              name: '微软雅黑',
              sz: 10,
              bold: true
            };
            ws[cellAddress].s.alignment = {
              horizontal: 'center',
              vertical: 'center',
              wrapText: true
            };
          }
        }
      }

      // 创建工作簿
      const wb = XLSX.utils.book_new();

      // 添加工作表
      XLSX.utils.book_append_sheet(wb, ws, '火车票订单');
      
      // 生成Excel文件
      const excelBuffer = XLSX.write(wb, { 
        bookType: 'xlsx', 
        type: 'array',
        cellStyles: true
      });
      const blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      
      // 生成包含筛选条件的文件名
      const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
      let filterInfo = '';
      
      // 添加状态筛选信息
      if (selectedStatuses.length > 0 && selectedStatuses.length < 6) {
        const statusNames = selectedStatuses.map(status => {
          switch (status) {
            case 'check_failed': return '验证失败';
            case 'initial': return '待提交';
            case 'submitted': return '已提交';
            case 'processing': return '处理中';
            case 'completed': return '预定完成';
            case 'failed': return '预定失败';
            default: return status;
          }
        });
        filterInfo += `_状态(${statusNames.join('_')})`;
      }
      
      // 添加搜索条件信息
      const searchConditions = [];
      if (searchTravelerName) searchConditions.push(`出行人(${searchTravelerName})`);
      if (searchMobilePhone) searchConditions.push(`手机(${searchMobilePhone})`);
      if (searchContactPhone) searchConditions.push(`联系人手机(${searchContactPhone})`);
      if (searchConditions.length > 0) {
        filterInfo += `_搜索(${searchConditions.join('_')})`;
      }
      
      const filename = `火车票订单_项目${projectId}${filterInfo}_${timestamp}.xlsx`;
      saveAs(blob, filename);
      
      // 显示成功提示
      toast({
        title: "导出成功",
        description: `成功导出 ${allOrders.length} 条筛选后的订单数据`,
        variant: "default",
      });
      
    } catch (error) {
      console.error('导出Excel失败:', error);
      toast({
        title: "导出失败",
        description: "导出Excel文件时发生错误，请重试",
        variant: "destructive",
      });
    }
  };

  // 渲染数据录入tab内容
  const renderInputTab = () => (
    <div className="p-6">
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 items-center">
          {/* Left side: Upload area */}
          <div className="space-y-4">
            {!uploadedFile ? (
              <div 
                className="relative flex flex-col items-center justify-center p-6 border-2 border-dashed border-gray-300 rounded-lg hover:border-blue-500 transition-colors cursor-pointer group"
                onClick={() => fileInputRef.current?.click()}
              >
                <Upload className="h-8 w-8 text-gray-400 mb-2 transition-colors group-hover:text-blue-500" />
                <p className="text-sm font-medium text-gray-700 transition-colors group-hover:text-blue-600">点击或拖拽文件到此区域</p>
                <p className="text-xs text-gray-500 mt-1">支持 .xlsx, .xls</p>
              </div>
            ) : (
              <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3 overflow-hidden">
                    <FileSpreadsheet className="h-8 w-8 text-blue-600 flex-shrink-0" />
                    <div className="overflow-hidden">
                      <p className="text-sm font-medium text-gray-800 truncate" title={uploadedFile.name}>{uploadedFile.name}</p>
                      <p className="text-xs text-gray-500">{(uploadedFile.size / 1024).toFixed(2)} KB</p>
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="text-gray-500 hover:text-red-500 flex-shrink-0"
                    onClick={removeUploadedFile}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            )}
          </div>
          
          {/* Right side: Actions and info */}
          <div className="space-y-4">
            <div>
              <h4 className="font-semibold text-gray-800">上传说明</h4>
              <ul className="list-disc list-inside text-sm text-gray-600 mt-2 space-y-1">
                <li>请确保文件格式与模板一致。</li>
                <li>系统将验证数据有效性，重复数据将被忽略。</li>
                                 <li>
                   <Button 
                     variant="link" 
                     size="sm" 
                     className="p-0 h-auto text-blue-600 hover:text-blue-700"
                     onClick={handleDownloadTemplate}
                   >
                     <Download className="h-3 w-3 mr-1" />
                     下载Excel模板
                   </Button>
                 </li>
              </ul>
            </div>
            
            <Button
              onClick={handleValidateAndUpload}
              disabled={!uploadedFile || validating || uploading}
              className="w-full bg-blue-600 hover:bg-blue-700 text-white"
            >
              {validating ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  验证中...
                </>
              ) : uploading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  上传中...
                </>
              ) : (
                <>
                  <Upload className="h-4 w-4 mr-2" />
                  验证并上传
                </>
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );

  // 渲染文本内容tab内容
  const renderTextTab = () => (
    <div className="p-6">
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Left side: Text area */}
          <div className="h-full flex flex-col">
            <Label htmlFor="text-input" className="mb-2 font-medium text-gray-700">粘贴文本内容</Label>
            <Textarea
              id="text-input"
              placeholder="粘贴成员信息，系统将自动识别姓名、身份证号、车次等信息。"
              className="flex-1 bg-white border-gray-300 resize-none min-h-[150px]"
              value={textInput}
              onChange={(e) => setTextInput(e.target.value)}
            />
          </div>
          
          {/* Right side: Actions and info */}
          <div className="space-y-4 flex flex-col justify-between">
            <div>
              <h4 className="font-semibold text-gray-800">文本解析说明</h4>
              <ul className="list-disc list-inside text-sm text-gray-600 mt-2 space-y-1">
                <li>支持自动识别姓名、身份证号、车次、行程等。</li>
                <li>请确保每条记录信息完整，并用换行分隔。</li>
                <li>解析结果将显示在下方的订单列表中。</li>
              </ul>
            </div>
            
            <Button
              onClick={handleTextParse}
              disabled={!textInput.trim()}
              className="w-full bg-green-600 hover:bg-green-700 text-white"
            >
              <FileText className="h-4 w-4 mr-2" />
              解析文本
            </Button>
          </div>
        </div>
      </div>
    </div>
  );

  // 渲染订单列表
  const renderOrderList = () => (
    <Card>
      <CardHeader>
        <div className="space-y-4">
          {/* 第一行：标题和操作按钮 */}
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2 text-lg font-medium">
              <Train className="h-5 w-5 text-blue-600" />
              <span>火车票订单列表</span>
              {totalCount > 0 && (
                <span className="bg-blue-100 text-blue-800 text-xs font-medium px-2 py-0.5 rounded-full ml-1">
                  {totalCount}
                </span>
              )}
            </CardTitle>
            
                        {/* 右侧操作按钮 */}
            <div className="flex items-center gap-2">
              <Button 
                variant="outline" 
                size="sm"
                onClick={handleClearOrders}
                disabled={clearingOrders || orders.length === 0}
                className="text-red-600 border-red-200 hover:bg-red-50"
              >
                {clearingOrders ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-600 mr-2"></div>
                    清空中...
                  </>
                ) : (
                  <>
                    <Trash2 className="h-4 w-4 mr-2" />
                    清空订单
                  </>
                )}
              </Button>
              <Button 
                variant="outline" 
                size="sm"
                onClick={handleGenerateOrder}
              >
                <FileText className="h-4 w-4 mr-2" />
                预订
              </Button>
              <Button 
                size="sm"
                onClick={handleSubmitOrder}
                className="bg-blue-600 hover:bg-blue-700 text-white"
              >
                <Send className="h-4 w-4 mr-2" />
                预订且出票
              </Button>
            </div>
          </div>
          
          {/* 第二行：短信通知和代订人设置 */}
          <div className="flex items-center gap-6 text-sm">
            {/* 短信通知 */}
            <div className="flex items-center gap-2">
              <Checkbox 
                id="sms-notify"
                checked={smsNotify}
                onCheckedChange={setSmsNotify}
              />
              <Label htmlFor="sms-notify" className="text-gray-700">
                短信通知
              </Label>
              {/* 设置来源提示 */}
              {settingsSource === 'project_history' && (
                <span className="text-xs text-green-600 bg-green-50 px-2 py-1 rounded">
                  📋 已载入项目历史设置
                </span>
              )}
              {settingsSource === 'localStorage' && (
                <span className="text-xs text-blue-600 bg-blue-50 px-2 py-1 rounded">
                  💾 已载入本地设置
                </span>
              )}
            </div>
            
            {/* 代订人设置 */}
            <div className="flex items-center gap-4">
              <span className="text-gray-700">代订人:</span>
              <div className="flex items-center gap-3">
                <div className="flex items-center gap-1">
                  <input
                    type="radio"
                    id="no-agent"
                    name="agent"
                    checked={!hasAgent}
                    onChange={() => setHasAgent(false)}
                    className="text-blue-600"
                  />
                  <Label htmlFor="no-agent" className="text-gray-700">
                    无
                  </Label>
                </div>
                <div className="flex items-center gap-1">
                  <input
                    type="radio"
                    id="has-agent"
                    name="agent"
                    checked={hasAgent}
                    onChange={() => setHasAgent(true)}
                    className="text-blue-600"
                  />
                  <Label htmlFor="has-agent" className="text-gray-700">
                    有
                  </Label>
                </div>
              </div>
              
              {/* 代订人手机号码输入框 */}
              {hasAgent && (
                <div className="flex items-center gap-2">
                  <span className="text-gray-600">手机号码:</span>
                  <input
                    type="tel"
                    value={agentPhone}
                    onChange={(e) => setAgentPhone(e.target.value)}
                    placeholder="请输入代订人手机号码"
                    className="px-3 py-1 border border-gray-300 rounded text-sm w-48 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              )}
            </div>
          </div>
          {/* 第四行：状态筛选 */}
          <div className="flex items-center gap-4 px-3 py-2 bg-gray-50 rounded-md border mt-3">
            <span className="text-sm text-gray-600 whitespace-nowrap">状态筛选:</span>
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-1">
                <Checkbox
                  id="status-initial"
                  checked={selectedStatuses.includes('initial')}
                  onCheckedChange={(checked) => handleStatusChange('initial', checked as boolean)}
                />
                <Label htmlFor="status-initial" className="text-sm text-gray-600 whitespace-nowrap">
                  待提交
                </Label>
              </div>
              <div className="flex items-center gap-1">
                <Checkbox
                  id="status-check-failed"
                  checked={selectedStatuses.includes('check_failed')}
                  onCheckedChange={(checked) => handleStatusChange('check_failed', checked as boolean)}
                />
                <Label htmlFor="status-check-failed" className="text-sm text-amber-600 whitespace-nowrap">
                  验证失败
                </Label>
              </div>
              <div className="flex items-center gap-1">
                <Checkbox
                  id="status-failed"
                  checked={selectedStatuses.includes('failed')}
                  onCheckedChange={(checked) => handleStatusChange('failed', checked as boolean)}
                />
                <Label htmlFor="status-failed" className="text-sm text-red-600 whitespace-nowrap">
                  预定失败
                </Label>
              </div>
            </div>
          </div>
          {/* 第三行：搜索区域 */}
          <div className="flex flex-col md:flex-row md:items-center gap-3 pt-2 border-t border-gray-100">
            <div className="flex items-center gap-2 flex-1 max-w-xs">
              <label className="text-sm text-gray-600 whitespace-nowrap min-w-fit">出行人姓名：</label>
              <input
                type="text"
                placeholder="请输入出行人姓名"
                value={searchTravelerName}
                onChange={(e) => setSearchTravelerName(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                className="flex-1 h-9 px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
              />
            </div>
            
            <div className="flex items-center gap-2 flex-1 max-w-xs">
              <label className="text-sm text-gray-600 whitespace-nowrap min-w-fit">手机号码：</label>
              <input
                type="text"
                placeholder="请输入手机号码"
                value={searchMobilePhone}
                onChange={(e) => setSearchMobilePhone(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                className="flex-1 h-9 px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
              />
            </div>
            
            <div className="flex items-center gap-2 flex-1 max-w-xs">
              <label className="text-sm text-gray-600 whitespace-nowrap min-w-fit">联系人手机：</label>
              <input
                type="text"
                placeholder="请输入联系人手机号"
                value={searchContactPhone}
                onChange={(e) => setSearchContactPhone(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                className="flex-1 h-9 px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
              />
            </div>

            <div className="flex items-center gap-2 ml-auto">
              <Button
                size="sm"
                onClick={handleSearch}
                disabled={pageLoading}
                className="bg-blue-600 hover:bg-blue-700 text-white h-9"
              >
                <Search className="h-4 w-4 mr-2" />
                搜索
              </Button>

              <Button
                variant="outline"
                size="sm"
                onClick={handleClearSearch}
                disabled={pageLoading}
                className="h-9"
              >
                <X className="h-4 w-4 mr-2" />
                重置
              </Button>

              <Button
                variant="outline"
                size="sm"
                onClick={handleExportExcel}
                disabled={pageLoading}
                className="h-9"
              >
                <Download className="h-4 w-4 mr-2" />
                导出
              </Button>
            </div>
          </div>
          
          
        </div>
      </CardHeader>
      <CardContent className="p-0">
        {pageLoading ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">加载中...</p>
          </div>
        ) : orders.length > 0 ? (
          <>
            <div className="overflow-x-auto custom-scrollbar" style={{ 
              scrollbarWidth: 'auto', 
              scrollbarColor: '#cbd5e1 #f1f5f9'
            }}>
              <style dangerouslySetInnerHTML={{
                __html: `
                  .custom-scrollbar::-webkit-scrollbar {
                    height: 12px;
                    background-color: #f1f5f9;
                  }
                  .custom-scrollbar::-webkit-scrollbar-track {
                    background-color: #f1f5f9;
                    border-radius: 6px;
                  }
                  .custom-scrollbar::-webkit-scrollbar-thumb {
                    background-color: #cbd5e1;
                    border-radius: 6px;
                    border: 2px solid #f1f5f9;
                  }
                  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
                    background-color: #94a3b8;
                  }
                `
              }} />
              <table className="w-full text-xs" style={{ minWidth: '2200px' }}>
                <thead className="bg-gray-50 border-b border-gray-200">
                  <tr>
                    <th className="text-center p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '60px' }}>序号</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>状态</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '150px' }}>错误信息</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>出行人姓名</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>出行人姓</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>出行人名</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>国籍</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '60px' }}>性别</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>出生日期</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>证件类型</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '150px' }}>证件号码</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>证件有效期至</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '120px' }}>手机号</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>国际区号</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>出行日期</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '120px' }}>出发站名</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '120px' }}>到达站名</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>车次</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>座位类型</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>出发时间</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '80px' }}>到达时间</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '120px' }}>成本中心</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '150px' }}>行程提交项</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>联系人</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '120px' }}>联系人手机号</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '150px' }}>联系人邮箱</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>审批参考人</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '150px' }}>公司名称</th>
                    <th className="text-left p-2 font-medium text-gray-900 text-xs whitespace-nowrap" style={{ minWidth: '100px' }}>代订人</th>
                    <th className="text-center p-2 font-medium text-gray-900 text-xs whitespace-nowrap sticky right-0 bg-gray-50 border-l border-gray-200" style={{ minWidth: '120px' }}>操作</th>
                  </tr>
                </thead>
                <tbody>
                  {orders.map((order, index) => (
                    <tr key={order.id} className={`border-b border-gray-100 ${order.order_status === 'check_failed' ? 'bg-red-50 hover:bg-red-100' : 'hover:bg-gray-50'}`}>
                      <td className="p-2 text-xs text-gray-900 whitespace-nowrap text-center">{(currentPage - 1) * pageSize + index + 1}</td>
                      <td className="p-2 text-xs whitespace-nowrap">{getOrderStatusDisplay(order.order_status)}</td>
                      <td className="p-2 text-xs text-gray-600 whitespace-nowrap relative group cursor-help">
                        {order.fail_reason ? (
                          <>
                            <span className={`${order.fail_reason.length > 15 ? 'border-b border-dotted border-gray-400' : ''}`}>
                              {order.fail_reason.length > 15 ? order.fail_reason.substring(0, 15) + '...' : order.fail_reason}
                            </span>
                            {order.fail_reason.length > 15 && (
                              <div className="invisible group-hover:visible absolute top-full mt-1 left-0 bg-gray-800 text-white text-xs rounded px-2 py-1 whitespace-normal max-w-xs z-50">
                                {order.fail_reason}
                              </div>
                            )}
                          </>
                        ) : (
                          '-'
                        )}
                      </td>
                      <td className="p-2 text-xs font-medium text-gray-900 whitespace-nowrap">{order.traveler_full_name || '-'}</td>
                      <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.traveler_surname || '-'}</td>
                      <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.traveler_given_name || '-'}</td>
                      <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.nationality || '-'}</td>
                      <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.gender || '-'}</td>
                      <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.birth_date || '-'}</td>
                      <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.id_type || '-'}</td>
                      <td className="p-2 text-xs text-gray-600 font-mono whitespace-nowrap">{order.id_number || '-'}</td>
                      <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.id_expiry_date || '-'}</td>
                      <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.mobile_phone || '-'}</td>
                      <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.mobile_phone_country_code || '-'}</td>
                      <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.travel_date || '-'}</td>
                      <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.departure_station || '-'}</td>
                      <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.arrival_station || '-'}</td>
                      <td className="p-2 text-xs font-medium text-blue-600 whitespace-nowrap">{order.train_number || '-'}</td>
                      <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.seat_type || '-'}</td>
                      <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.departure_time || '-'}</td>
                      <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.arrival_time || '-'}</td>
                      <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.cost_center || '-'}</td>
                      <td className="p-2 text-xs text-gray-600 whitespace-nowrap" title={order.trip_submission_item || '-'}>
                        {order.trip_submission_item ? (order.trip_submission_item.length > 20 ? order.trip_submission_item.substring(0, 20) + '...' : order.trip_submission_item) : '-'}
                      </td>
                      <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.contact_person || '-'}</td>
                      <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.contact_phone || '-'}</td>
                      <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.contact_email || '-'}</td>
                      <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.approval_reference || '-'}</td>
                      <td className="p-2 text-xs text-gray-600 whitespace-nowrap" title={order.company_name || '-'}>
                        {order.company_name ? (order.company_name.length > 15 ? order.company_name.substring(0, 15) + '...' : order.company_name) : '-'}
                      </td>
                      <td className="p-2 text-xs text-gray-600 whitespace-nowrap">{order.booking_agent || '-'}</td>
                      <td className="p-2 text-xs whitespace-nowrap sticky right-0 bg-white border-l border-gray-200 z-20">
                        <div className="flex items-center gap-1">
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-6 w-6 p-0"
                            onClick={() => openDetailModal(order)}
                            title="查看详情"
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-6 w-6 p-0 text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                            onClick={() => handleEditOrder(order)}
                            title="编辑订单"
                          >
                            <PenTool className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-6 w-6 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                            onClick={() => handleDeleteOrder(order.id, order.traveler_full_name)}
                            disabled={deletingOrderId === order.id}
                            title="删除订单"
                          >
                            {deletingOrderId === order.id ? (
                              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-600"></div>
                            ) : (
                              <Trash2 className="h-4 w-4" />
                            )}
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
            
            {/* 分页 - 始终显示 */}
            <div className="flex items-center justify-between p-4 border-t border-gray-200">
              <div className="text-sm text-gray-700">
                {totalCount > 0 ? (
                  <>显示第 {(currentPage - 1) * pageSize + 1} 到 {Math.min(currentPage * pageSize, totalCount)} 条，共 {totalCount} 条记录</>
                ) : (
                  <>共 0 条记录</>
                )}
              </div>
              <div className="flex items-center space-x-4">
                {/* 每页显示数量选择 */}
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-gray-700">每页显示:</span>
                  <select
                    value={pageSize}
                    onChange={(e) => {
                      const newPageSize = parseInt(e.target.value);
                      setPageSize(newPageSize);
                      // 不需要手动调用loadTrainOrders，useEffect会自动处理
                    }}
                    className="text-sm border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    disabled={pageLoading}
                  >
                    <option value={5}>5条</option>
                    <option value={10}>10条</option>
                    <option value={20}>20条</option>
                    <option value={50}>50条</option>
                  </select>
                </div>
                
                {/* 分页导航 - 只在有多页时显示 */}
                {Math.ceil(totalCount / pageSize) > 1 && (
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => loadTrainOrders(1)}
                      disabled={currentPage <= 1 || pageLoading}
                    >
                      首页
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => loadTrainOrders(currentPage - 1)}
                      disabled={currentPage <= 1 || pageLoading}
                    >
                      上一页
                    </Button>
                    
                    {/* 页码显示 */}
                    <div className="flex items-center space-x-1">
                      {(() => {
                        const totalPages = Math.ceil(totalCount / pageSize);
                        const pages = [];
                        const maxVisiblePages = 5;
                        
                        let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
                        let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
                        
                        if (endPage - startPage + 1 < maxVisiblePages) {
                          startPage = Math.max(1, endPage - maxVisiblePages + 1);
                        }
                        
                        for (let i = startPage; i <= endPage; i++) {
                          pages.push(
                            <button
                              key={i}
                              onClick={() => loadTrainOrders(i)}
                              disabled={pageLoading}
                              className={`px-3 py-1 text-sm border rounded ${
                                i === currentPage
                                  ? 'bg-blue-600 text-white border-blue-600'
                                  : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                              } ${pageLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
                            >
                              {i}
                            </button>
                          );
                        }
                        
                        return pages;
                      })()}
                    </div>
                    
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => loadTrainOrders(currentPage + 1)}
                      disabled={currentPage >= Math.ceil(totalCount / pageSize) || pageLoading}
                    >
                      下一页
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => loadTrainOrders(Math.ceil(totalCount / pageSize))}
                      disabled={currentPage >= Math.ceil(totalCount / pageSize) || pageLoading}
                    >
                      末页
                    </Button>
                  </div>
                )}
                
                <div className="text-sm text-gray-700">
                  {totalCount > 0 ? (
                    <>第 {currentPage} 页，共 {Math.ceil(totalCount / pageSize)} 页</>
                  ) : (
                    <>第 1 页，共 1 页</>
                  )}
                </div>
              </div>
            </div>
          </>
        ) : (
          <div className="text-center py-12">
            <Train className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            {/* 根据是否有搜索条件显示不同的空状态消息 */}
            {searchTravelerName || searchMobilePhone || searchContactPhone ? (
              <>
                <h3 className="text-lg font-medium text-gray-900 mb-2">暂无订单数据</h3>
                <p className="text-gray-500">没有找到符合搜索条件的订单</p>
              </>
            ) : (
              <>
                <h3 className="text-lg font-medium text-gray-900 mb-2">没有符合条件的订单</h3>
                <p className="text-gray-500">请先上传Excel文件或输入文本内容来创建火车票订单</p>
              </>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">加载中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-gray-50 min-h-screen flex flex-col">


      {/* Tab导航 */}
      <div className="bg-white border-b border-gray-200">
        <div className="grid grid-cols-2">
          <button
            onClick={() => setActiveTab('input')}
            className={`py-4 px-6 text-center font-medium text-sm transition-colors duration-200 border-b-2 ${
              activeTab === 'input'
                ? 'border-blue-500 text-blue-600 bg-blue-50'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50'
            }`}
          >
            <div className="flex items-center justify-center gap-2">
              <Upload className="h-4 w-4" />
              Excel文件上传
            </div>
          </button>
          <button
            onClick={() => setActiveTab('text')}
            className={`py-4 px-6 text-center font-medium text-sm transition-colors duration-200 border-b-2 ${
              activeTab === 'text'
                ? 'border-blue-500 text-blue-600 bg-blue-50'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50'
            }`}
          >
            <div className="flex items-center justify-center gap-2">
              <FileText className="h-4 w-4" />
              文本内容
            </div>
          </button>
        </div>
      </div>

      {/* Tab内容区域 */}
      <div className="h-auto">
        {activeTab === 'input' && renderInputTab()}
        {activeTab === 'text' && renderTextTab()}
      </div>

      {/* 独立的订单列表区域 */}
      <div className="flex-1 p-6">
        {renderOrderList()}
      </div>

      {/* 隐藏的文件输入 */}
      <input
        ref={fileInputRef}
        type="file"
        accept=".xlsx,.xls"
        onChange={handleFileUpload}
        className="hidden"
      />

      {/* 详情弹窗 */}
      {showDetailModal && selectedOrder && editingOrder && (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
          {/* 背景遮罩 */}
          <div className="absolute inset-0 bg-black/50 backdrop-blur-sm" onClick={() => setShowDetailModal(false)}></div>
          
          {/* 弹窗内容 */}
          <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden z-50 mx-4">
            {/* 弹窗头部 */}
            <div className="flex justify-between items-center px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-white">
              <h2 className="text-xl font-semibold text-blue-700 flex items-center">
                <Eye className="h-5 w-5 mr-2" />
                火车票订单详情 {isEditMode && <span className="ml-2 text-sm text-orange-600">(编辑模式)</span>}
              </h2>
              <button 
                onClick={() => setShowDetailModal(false)}
                className="h-8 w-8 rounded-full text-gray-400 hover:text-gray-600 hover:bg-gray-100 flex items-center justify-center transition-colors"
              >
                <X className="h-5 w-5" />
              </button>
            </div>
            
            {/* 弹窗内容 */}
            <div className="p-6 overflow-y-auto max-h-[calc(90vh-180px)]">
              <div className="space-y-8">
                {/* 订单状态区域 */}
                <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="text-lg font-medium text-gray-900">订单状态</h3>
                    <div className="flex items-center">
                      {getOrderStatusDisplay(selectedOrder.order_status)}
                    </div>
                  </div>
                  
                  {/* 失败原因展示 - 只在验证失败或预定失败时显示 */}
                  {(selectedOrder.order_status === 'check_failed' || selectedOrder.order_status === 'failed') && selectedOrder.fail_reason && (
                    <div className="mt-4 p-4 bg-white rounded-lg border border-gray-200">
                      <div className="flex items-start gap-3">
                        <div className="flex-shrink-0">
                          <AlertTriangle className={`h-5 w-5 ${
                            selectedOrder.order_status === 'check_failed' ? 'text-amber-500' : 'text-red-500'
                          }`} />
                        </div>
                        <div className="flex-1">
                          <h4 className={`font-medium text-sm ${
                            selectedOrder.order_status === 'check_failed' ? 'text-amber-800' : 'text-red-800'
                          }`}>
                            {selectedOrder.order_status === 'check_failed' ? '验证失败原因' : '预定失败原因'}
                          </h4>
                          <p className={`mt-1 text-sm ${
                            selectedOrder.order_status === 'check_failed' ? 'text-amber-700' : 'text-red-700'
                          }`}>
                            {selectedOrder.fail_reason}
                          </p>
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                {/* 出行人基础信息 */}
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4 pb-2 border-b border-gray-200">出行人基础信息</h3>
                  <div className="overflow-hidden border border-gray-300 rounded-lg">
                    <table className="w-full">
                      <tbody>
                        <tr className="border-b border-gray-200">
                          <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">出行人姓名</label>
                          </td>
                          <td className="py-3 px-4 w-1/4 border-r border-gray-200">
                            <EditableCell 
                              value={editingOrder.traveler_full_name} 
                              field="traveler_full_name" 
                              isEditing={isEditMode} 
                              onFieldChange={handleFieldChange}
                              validationErrors={validationErrors}
                              editingOrder={editingOrder}
                            />
                          </td>
                          <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">姓</label>
                          </td>
                          <td className="py-3 px-4 w-1/4">
                            <EditableCell 
                              value={editingOrder.traveler_surname} 
                              field="traveler_surname" 
                              isEditing={isEditMode} 
                              onFieldChange={handleFieldChange}
                              validationErrors={validationErrors}
                              editingOrder={editingOrder}
                            />
                          </td>
                        </tr>
                        <tr className="border-b border-gray-200">
                          <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">名</label>
                          </td>
                          <td className="py-3 px-4 border-r border-gray-200">
                            <EditableCell 
                              value={editingOrder.traveler_given_name} 
                              field="traveler_given_name" 
                              isEditing={isEditMode} 
                              onFieldChange={handleFieldChange}
                              validationErrors={validationErrors}
                              editingOrder={editingOrder}
                            />
                          </td>
                          <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">国籍</label>
                          </td>
                          <td className="py-3 px-4">
                            <EditableCell 
                              value={editingOrder.nationality} 
                              field="nationality" 
                              isEditing={isEditMode} 
                              onFieldChange={handleFieldChange}
                              validationErrors={validationErrors}
                              editingOrder={editingOrder}
                            />
                          </td>
                        </tr>
                        <tr className="border-b border-gray-200">
                          <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">性别</label>
                          </td>
                          <td className="py-3 px-4 border-r border-gray-200">
                            <EditableCell 
                              value={editingOrder.gender} 
                              field="gender" 
                              isEditing={isEditMode} 
                              onFieldChange={handleFieldChange}
                              validationErrors={validationErrors}
                              editingOrder={editingOrder}
                            />
                          </td>
                          <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">出生日期</label>
                          </td>
                          <td className="py-3 px-4">
                            <EditableCell 
                              value={editingOrder.birth_date} 
                              field="birth_date" 
                              type="date" 
                              isEditing={isEditMode} 
                              onFieldChange={handleFieldChange}
                              validationErrors={validationErrors}
                              editingOrder={editingOrder}
                            />
                          </td>
                        </tr>
                        <tr className="border-b border-gray-200">
                          <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">证件类型</label>
                          </td>
                          <td className="py-3 px-4 border-r border-gray-200">
                            <EditableCell 
                              value={editingOrder.id_type} 
                              field="id_type" 
                              isEditing={isEditMode} 
                              onFieldChange={handleFieldChange}
                              validationErrors={validationErrors}
                              editingOrder={editingOrder}
                            />
                          </td>
                          <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">证件号码</label>
                          </td>
                          <td className="py-3 px-4">
                            <EditableCell 
                              value={editingOrder.id_number} 
                              field="id_number" 
                              isEditing={isEditMode} 
                              onFieldChange={handleFieldChange}
                              validationErrors={validationErrors}
                              editingOrder={editingOrder}
                            />
                          </td>
                        </tr>
                        <tr className="border-b border-gray-200">
                          <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">证件有效期</label>
                          </td>
                          <td className="py-3 px-4 border-r border-gray-200">
                            <EditableCell 
                              value={editingOrder.id_expiry_date} 
                              field="id_expiry_date" 
                              type="date" 
                              isEditing={isEditMode} 
                              onFieldChange={handleFieldChange}
                              validationErrors={validationErrors}
                              editingOrder={editingOrder}
                            />
                          </td>
                          <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">手机国际区号</label>
                          </td>
                          <td className="py-3 px-4">
                            <EditableCell 
                              value={editingOrder.mobile_phone_country_code} 
                              field="mobile_phone_country_code" 
                              isEditing={isEditMode} 
                              onFieldChange={handleFieldChange}
                              validationErrors={validationErrors}
                              editingOrder={editingOrder}
                            />
                          </td>
                        </tr>
                        <tr>
                          <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">手机号</label>
                          </td>
                          <td className="py-3 px-4">
                            <EditableCell 
                              value={editingOrder.mobile_phone} 
                              field="mobile_phone" 
                              isEditing={isEditMode} 
                              onFieldChange={handleFieldChange}
                              validationErrors={validationErrors}
                              editingOrder={editingOrder}
                            />
                          </td>
                          <td className="py-3 px-4 bg-gray-50 border-r border-gray-200"></td>
                          <td className="py-3 px-4"></td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>

                {/* 出行信息 */}
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4 pb-2 border-b border-gray-200">出行信息</h3>
                  <div className="overflow-hidden border border-gray-300 rounded-lg">
                    <table className="w-full">
                      <tbody>
                        <tr className="border-b border-gray-200">
                          <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">差旅单号</label>
                          </td>
                          <td className="py-3 px-4 w-1/4 border-r border-gray-200">
                            <EditableCell 
                              value={editingOrder.sequence_number} 
                              field="sequence_number" 
                              isEditing={isEditMode} 
                              onFieldChange={handleFieldChange}
                              validationErrors={validationErrors}
                              editingOrder={editingOrder}
                            />
                          </td>
                          <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">出行日期</label>
                          </td>
                          <td className="py-3 px-4 w-1/4">
                            <EditableCell 
                              value={editingOrder.travel_date} 
                              field="travel_date" 
                              type="date" 
                              isEditing={isEditMode} 
                              onFieldChange={handleFieldChange}
                              validationErrors={validationErrors}
                              editingOrder={editingOrder}
                            />
                          </td>
                        </tr>
                        <tr className="border-b border-gray-200">
                          <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">出发站名</label>
                          </td>
                          <td className="py-3 px-4 border-r border-gray-200">
                            <EditableCell 
                              value={editingOrder.departure_station} 
                              field="departure_station" 
                              isEditing={isEditMode} 
                              onFieldChange={handleFieldChange}
                              validationErrors={validationErrors}
                              editingOrder={editingOrder}
                            />
                          </td>
                          <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">到达站名</label>
                          </td>
                          <td className="py-3 px-4">
                            <EditableCell 
                              value={editingOrder.arrival_station} 
                              field="arrival_station" 
                              isEditing={isEditMode} 
                              onFieldChange={handleFieldChange}
                              validationErrors={validationErrors}
                              editingOrder={editingOrder}
                            />
                          </td>
                        </tr>
                        <tr className="border-b border-gray-200">
                          <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">车次</label>
                          </td>
                          <td className="py-3 px-4 border-r border-gray-200">
                            <EditableCell 
                              value={editingOrder.train_number} 
                              field="train_number" 
                              isEditing={isEditMode} 
                              onFieldChange={handleFieldChange}
                              validationErrors={validationErrors}
                              editingOrder={editingOrder}
                            />
                          </td>
                          <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">座位类型</label>
                          </td>
                          <td className="py-3 px-4">
                            <EditableCell 
                              value={editingOrder.seat_type} 
                              field="seat_type" 
                              isEditing={isEditMode} 
                              onFieldChange={handleFieldChange}
                              validationErrors={validationErrors}
                              editingOrder={editingOrder}
                            />
                          </td>
                        </tr>
                        <tr className="border-b border-gray-200">
                          <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">出发时间</label>
                          </td>
                          <td className="py-3 px-4 border-r border-gray-200">
                            <EditableCell 
                              value={editingOrder.departure_time} 
                              field="departure_time" 
                              isEditing={isEditMode} 
                              onFieldChange={handleFieldChange}
                              validationErrors={validationErrors}
                              editingOrder={editingOrder}
                            />
                          </td>
                          <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">到达时间</label>
                          </td>
                          <td className="py-3 px-4">
                            <EditableCell 
                              value={editingOrder.arrival_time} 
                              field="arrival_time" 
                              isEditing={isEditMode} 
                              onFieldChange={handleFieldChange}
                              validationErrors={validationErrors}
                              editingOrder={editingOrder}
                            />
                          </td>
                        </tr>
                        <tr>
                          <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">成本中心</label>
                          </td>
                          <td className="py-3 px-4" colSpan={3}>
                            <EditableCell 
                              value={editingOrder.cost_center} 
                              field="cost_center" 
                              isEditing={isEditMode} 
                              onFieldChange={handleFieldChange}
                              validationErrors={validationErrors}
                              editingOrder={editingOrder}
                            />
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>

                {/* 对账单信息 */}
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4 pb-2 border-b border-gray-200">对账单信息</h3>
                  <div className="overflow-hidden border border-gray-300 rounded-lg">
                    <table className="w-full">
                      <tbody>
                        <tr className="border-b border-gray-200">
                          <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">公司名称</label>
                          </td>
                          <td className="py-3 px-4 w-1/4 border-r border-gray-200">
                            <EditableCell 
                              value={editingOrder.company_name} 
                              field="company_name" 
                              isEditing={isEditMode} 
                              onFieldChange={handleFieldChange}
                              validationErrors={validationErrors}
                              editingOrder={editingOrder}
                            />
                          </td>
                          <td className="py-3 px-4 w-1/4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">代订人</label>
                          </td>
                          <td className="py-3 px-4 w-1/4">
                            <EditableCell 
                              value={editingOrder.booking_agent} 
                              field="booking_agent" 
                              isEditing={isEditMode} 
                              onFieldChange={handleFieldChange}
                              validationErrors={validationErrors}
                              editingOrder={editingOrder}
                            />
                          </td>
                        </tr>
                        <tr className="border-b border-gray-200">
                          <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">金额 (元)</label>
                          </td>
                          <td className="py-3 px-4 border-r border-gray-200">
                            <EditableCell 
                              value={editingOrder.amount} 
                              field="amount" 
                              type="number" 
                              isEditing={isEditMode} 
                              onFieldChange={handleFieldChange}
                              validationErrors={validationErrors}
                              editingOrder={editingOrder}
                            />
                          </td>
                          <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">订单号</label>
                          </td>
                          <td className="py-3 px-4">
                            <EditableCell 
                              value={editingOrder.order_number} 
                              field="order_number" 
                              isEditing={isEditMode} 
                              onFieldChange={handleFieldChange}
                              validationErrors={validationErrors}
                              editingOrder={editingOrder}
                            />
                          </td>
                        </tr>
                        <tr className="border-b border-gray-200">
                          <td className="py-3 px-4 bg-gray-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-gray-700">账单号</label>
                          </td>
                          <td className="py-3 px-4 border-r border-gray-200">
                            <EditableCell 
                              value={editingOrder.bill_number} 
                              field="bill_number" 
                              isEditing={isEditMode} 
                              onFieldChange={handleFieldChange}
                              validationErrors={validationErrors}
                              editingOrder={editingOrder}
                            />
                          </td>
                          <td className="py-3 px-4 bg-gray-50 border-r border-gray-200"></td>
                          <td className="py-3 px-4"></td>
                        </tr>
                        <tr>
                          <td className="py-3 px-4 bg-blue-50 border-r border-gray-200">
                            <label className="text-sm font-medium text-blue-800">出票短信</label>
                          </td>
                          <td className="py-3 px-4" colSpan={3}>
                            <EditableCell 
                              value={editingOrder.ticket_sms} 
                              field="ticket_sms" 
                              isEditing={isEditMode} 
                              onFieldChange={handleFieldChange}
                              validationErrors={validationErrors}
                              editingOrder={editingOrder}
                            />
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
            
            {/* 弹窗底部 */}
            <div className="px-6 py-6 border-t border-gray-200 bg-gray-50 flex justify-between items-center">
              <div className="text-sm text-gray-500">
                {isEditMode ? '编辑模式：修改内容后点击保存按钮' : '查看模式：点击修改按钮开始编辑'}
              </div>
              <div className="flex items-center gap-3">
                {isEditMode ? (
                  <>
                    <Button variant="outline" onClick={cancelEdit} disabled={saving}>
                      取消
                    </Button>
                    <Button onClick={saveChanges} disabled={saving}>
                      {saving ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                          保存中...
                        </>
                      ) : (
                        <>
                          <Save className="h-4 w-4 mr-2" />
                          保存
                        </>
                      )}
                    </Button>
                  </>
                ) : (
                  <>
                    <Button variant="outline" onClick={enterEditMode}>
                      <Edit className="h-4 w-4 mr-2" />
                      修改
                    </Button>
                    <Button onClick={() => setShowDetailModal(false)}>
                      关闭
                    </Button>
                  </>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 验证结果弹窗 */}
      {showValidationModal && validationResult && (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
          {/* 背景遮罩 */}
          <div 
            className="absolute inset-0 bg-black/50 backdrop-blur-sm" 
            onClick={() => setShowValidationModal(false)}
          ></div>
          
          {/* 弹窗内容 */}
          <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden z-50 mx-4">
            {/* 弹窗头部 */}
            <div className="flex justify-between items-center px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-white">
              <h2 className="text-xl font-semibold text-blue-700 flex items-center">
                <FileSpreadsheet className="h-5 w-5 mr-2" />
                Excel数据验证结果
              </h2>
              <button 
                onClick={() => setShowValidationModal(false)}
                className="h-8 w-8 rounded-full text-gray-400 hover:text-gray-600 hover:bg-gray-100 flex items-center justify-center transition-colors"
              >
                <X className="h-5 w-5" />
              </button>
            </div>
            
            {/* 弹窗内容 */}
            <div className="p-6 overflow-y-auto max-h-[calc(90vh-180px)]">
              {/* 统计信息卡片 */}
              <div className="grid grid-cols-4 gap-4 mb-6">
                {/* 总行数 */}
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <div className="flex items-center">
                    <div className="p-2 bg-blue-100 rounded-full mr-3">
                      <FileSpreadsheet className="h-4 w-4 text-blue-600" />
                    </div>
                    <div>
                      <p className="text-sm text-blue-600 font-medium">总行数</p>
                      <p className="text-xl font-bold text-blue-700">{validationResult.total_rows}</p>
                    </div>
                  </div>
                </div>
                
                {/* 有效行数 */}
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <div className="flex items-center">
                    <div className="p-2 bg-green-100 rounded-full mr-3">
                      <Check className="h-4 w-4 text-green-600" />
                    </div>
                    <div>
                      <p className="text-sm text-green-600 font-medium">有效行数</p>
                      <p className="text-xl font-bold text-green-700">{validationResult.valid_rows}</p>
                    </div>
                  </div>
                </div>
                
                {/* 错误行数 */}
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <div className="flex items-center">
                    <div className="p-2 bg-red-100 rounded-full mr-3">
                      <X className="h-4 w-4 text-red-600" />
                    </div>
                    <div>
                      <p className="text-sm text-red-600 font-medium">错误行数</p>
                      <p className="text-xl font-bold text-red-700">{validationResult.error_rows}</p>
                    </div>
                  </div>
                </div>
                
                {/* 错误数量 */}
                <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
                  <div className="flex items-center">
                    <div className="p-2 bg-orange-100 rounded-full mr-3">
                      <Trash2 className="h-4 w-4 text-orange-600" />
                    </div>
                    <div>
                      <p className="text-sm text-orange-600 font-medium">错误数量</p>
                      <p className="text-xl font-bold text-orange-700">{validationResult.errors.length}</p>
                    </div>
                  </div>
                </div>
              </div>
              
              {/* 验证结果消息 */}
              <div className={`p-4 rounded-lg mb-6 ${
                !validationResult.can_proceed 
                  ? 'bg-red-50 border border-red-200' 
                  : validationResult.has_errors
                  ? 'bg-orange-50 border border-orange-200'
                  : 'bg-green-50 border border-green-200'
              }`}>
                <div className="flex items-center">
                  {!validationResult.can_proceed ? (
                    <X className="h-5 w-5 text-red-600 mr-2" />
                  ) : validationResult.has_errors ? (
                    <AlertTriangle className="h-5 w-5 text-orange-600 mr-2" />
                  ) : (
                    <Check className="h-5 w-5 text-green-600 mr-2" />
                  )}
                  <p className={`font-medium ${
                    !validationResult.can_proceed ? 'text-red-700' 
                    : validationResult.has_errors ? 'text-orange-700' 
                    : 'text-green-700'
                  }`}>
                    {validationResult.message}
                  </p>
                </div>
              </div>
              
              {/* 错误详情列表 */}
              {validationResult.errors.length > 0 && (
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4 pb-2 border-b border-gray-200">
                    错误详情 ({validationResult.errors.length} 个错误)
                  </h3>
                  
                  <div className="space-y-3 max-h-80 overflow-y-auto">
                    {validationResult.errors.map((error: ValidationError, index: number) => (
                      <div 
                        key={index}
                        className="bg-red-50 border border-red-200 rounded-lg p-4"
                      >
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center mb-2">
                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 mr-2">
                                第 {error.row} 行
                              </span>
                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 mr-2">
                                {error.field}
                              </span>
                              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                error.error_type === 'required' ? 'bg-orange-100 text-orange-800' :
                                error.error_type === 'format' ? 'bg-blue-100 text-blue-800' :
                                error.error_type === 'expired' ? 'bg-yellow-100 text-yellow-800' :
                                error.error_type === 'duplicate_in_file' ? 'bg-purple-100 text-purple-800' :
                                error.error_type === 'duplicate_in_db' ? 'bg-pink-100 text-pink-800' :
                                'bg-gray-100 text-gray-800'
                              }`}>
                                {error.error_type === 'required' ? '必填' :
                                 error.error_type === 'format' ? '格式错误' :
                                 error.error_type === 'expired' ? '已过期' :
                                 error.error_type === 'duplicate_in_file' ? '文件内重复' :
                                 error.error_type === 'duplicate_in_db' ? '数据库重复' :
                                 error.error_type}
                              </span>
                            </div>
                            <p className="text-sm text-red-700 mb-1">{error.message}</p>
                            {error.value && (
                              <p className="text-xs text-gray-600">
                                当前值: <span className="font-mono bg-gray-100 px-1 rounded">{error.value}</span>
                              </p>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
            
            {/* 弹窗底部 */}
            <div className="px-6 py-4 border-t border-gray-200 bg-gray-50 flex justify-between items-center">
              <div className="text-sm text-gray-500">
                {validationResult.can_proceed 
                  ? (validationResult.has_errors 
                      ? '存在重复数据，导入时将自动忽略重复项' 
                      : '数据验证通过，可以进行导入操作')
                  : '请修正错误后重新上传Excel文件'
                }
              </div>
              <div className="flex items-center gap-3">
                <Button variant="outline" onClick={() => setShowValidationModal(false)}>
                  关闭
                </Button>
                {validationResult.can_proceed && (
                  <Button 
                    onClick={() => {
                      setShowValidationModal(false);
                      uploadExcelFile();
                    }}
                    disabled={uploading}
                    className={validationResult.has_errors ? 'bg-orange-600 hover:bg-orange-700' : ''}
                  >
                    {uploading ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        上传中...
                      </>
                    ) : (
                      validationResult.has_errors ? '继续导入' : '确认导入'
                    )}
                  </Button>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 自定义确认对话框 */}
      {showConfirmDialog && confirmDialogData && (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
          {/* 背景遮罩 */}
          <div 
            className="absolute inset-0 bg-black/50 backdrop-blur-sm" 
            onClick={() => setShowConfirmDialog(false)}
          ></div>
          
          {/* 对话框内容 */}
          <div className="bg-white rounded-2xl shadow-2xl w-full max-w-lg mx-4 z-50 overflow-hidden">
            {/* 头部图标区域 */}
            <div className="flex justify-center pt-8 pb-4">
              <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
                <AlertTriangle className="h-8 w-8 text-red-500" />
              </div>
            </div>
            
            {/* 标题 */}
            <div className="text-center px-6 pb-4">
              <h2 className="text-xl font-semibold text-gray-900 mb-2">
                无法提交预订
              </h2>
              <p className="text-sm text-gray-600">
                当前搜索结果中有问题订单需要先处理
              </p>
            </div>
            
            {/* 内容区域 */}
            <div className="px-6 pb-6 max-h-80 overflow-y-auto">
              <div className="space-y-4">
                {confirmDialogData.checkFailedOrders.length > 0 && (
                  <div>
                    <div className="text-sm text-gray-600 mb-2">
                      <span className="font-medium">验证失败订单 ({confirmDialogData.checkFailedOrders.length} 条)：</span>
                    </div>
                    <div className="space-y-2">
                      {confirmDialogData.checkFailedOrders.slice(0, 5).map((order) => (
                        <div key={order.id} className="flex items-start gap-3 p-3 bg-amber-50 rounded-lg border border-amber-200">
                          <div className="text-sm">
                            <div className="font-medium text-gray-900">
                              出行人：{order.traveler_full_name || '未知'}
                            </div>
                            <div className="text-amber-700 mt-1">
                              {order.fail_reason || '验证失败'}
                            </div>
                          </div>
                        </div>
                      ))}
                      {confirmDialogData.checkFailedOrders.length > 5 && (
                        <div className="text-sm text-gray-500 text-center py-2">
                          ... 还有 {confirmDialogData.checkFailedOrders.length - 5} 条验证失败订单
                        </div>
                      )}
                    </div>
                  </div>
                )}
                
                {confirmDialogData.bookingFailedOrders.length > 0 && (
                  <div>
                    <div className="text-sm text-gray-600 mb-2">
                      <span className="font-medium">预定失败订单 ({confirmDialogData.bookingFailedOrders.length} 条)：</span>
                    </div>
                    <div className="space-y-2">
                      {confirmDialogData.bookingFailedOrders.slice(0, 5).map((order) => (
                        <div key={order.id} className="flex items-start gap-3 p-3 bg-red-50 rounded-lg border border-red-200">
                          <div className="text-sm">
                            <div className="font-medium text-gray-900">
                              出行人：{order.traveler_full_name || '未知'}
                            </div>
                            <div className="text-red-700 mt-1">
                              {order.fail_reason || '预定失败'}
                            </div>
                          </div>
                        </div>
                      ))}
                      {confirmDialogData.bookingFailedOrders.length > 5 && (
                        <div className="text-sm text-gray-500 text-center py-2">
                          ... 还有 {confirmDialogData.bookingFailedOrders.length - 5} 条预定失败订单
                        </div>
                      )}
                    </div>
                  </div>
                )}
                
                <div className="text-sm text-gray-600 text-center pt-4 border-t border-gray-200">
                  请先编辑修改这些订单，或者使用搜索筛选功能排除这些订单后再进行提交。
                </div>
              </div>
            </div>
            
            {/* 底部按钮 */}
            <div className="px-6 py-4 bg-gray-50 flex justify-center">
              <Button
                onClick={() => setShowConfirmDialog(false)}
                className="px-8 bg-red-600 hover:bg-red-700 text-white"
              >
                确定
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* 通用确认对话框 */}
      {showGeneralConfirmDialog && generalConfirmDialogData && (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
          {/* 背景遮罩 */}
          <div 
            className="absolute inset-0 bg-black/50 backdrop-blur-sm" 
            onClick={handleGeneralCancel}
          ></div>
          
          {/* 对话框内容 */}
          <div className="bg-white rounded-2xl shadow-2xl w-full max-w-lg mx-4 z-50 overflow-hidden">
            {/* 头部图标区域 */}
            <div className="flex justify-center pt-8 pb-4">
              <div className={`w-16 h-16 rounded-full flex items-center justify-center ${
                generalConfirmDialogData.type === 'danger' ? 'bg-red-100' :
                generalConfirmDialogData.type === 'warning' ? 'bg-orange-100' :
                'bg-blue-100'
              }`}>
                {generalConfirmDialogData.type === 'danger' ? (
                  <AlertTriangle className="h-8 w-8 text-red-500" />
                ) : generalConfirmDialogData.type === 'warning' ? (
                  <AlertTriangle className="h-8 w-8 text-orange-500" />
                ) : (
                  <Check className="h-8 w-8 text-blue-500" />
                )}
              </div>
            </div>
            
            {/* 标题 */}
            <div className="text-center px-6 pb-4">
              <h2 className="text-xl font-semibold text-gray-900 mb-2">
                {generalConfirmDialogData.title}
              </h2>
              <p className="text-sm text-gray-600">
                {generalConfirmDialogData.message}
              </p>
            </div>
            
            {/* 底部按钮 */}
            <div className="px-6 py-4 bg-gray-50 flex justify-center gap-3">
              <Button
                variant="outline"
                onClick={handleGeneralCancel}
                className="px-6"
              >
                {generalConfirmDialogData.cancelText}
              </Button>
              <Button
                onClick={handleGeneralConfirm}
                className={`px-6 ${
                  generalConfirmDialogData.type === 'danger' ? 'bg-red-600 hover:bg-red-700' :
                  generalConfirmDialogData.type === 'warning' ? 'bg-orange-600 hover:bg-orange-700' :
                  'bg-blue-600 hover:bg-blue-700'
                } text-white`}
              >
                {generalConfirmDialogData.confirmText}
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TrainBookingContent;
