import React from 'react';
import { Calendar, User, User<PERSON><PERSON><PERSON>, Edit, Trash2 } from 'lucide-react';
import { Project } from '@/types/project';
import { ProjectOrderStatsResponse } from '@/api/trainOrder';

interface ProjectCardProps {
  project: Project;
  projectStats?: ProjectOrderStatsResponse;
  onEdit?: (project: Project) => void;
  onDelete?: (project: Project) => void;
  onView?: (project: Project) => void;
}

const ProjectCard: React.FC<ProjectCardProps> = ({
  project,
  projectStats,
  onEdit,
  onDelete,
  onView
}) => {
  // 格式化日期
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', { 
      year: 'numeric',
      month: '2-digit', 
      day: '2-digit' 
    }).replace(/\//g, '-');
  };



  // 获取真实的预订金额
  const getBookingAmount = () => {
    if (projectStats) {
      return parseFloat(projectStats.completed_amount) || 0;
    }
    return 0;
  };

  // 生成渐变背景色
  const getGradientBackground = () => {
    const gradients = [
      'from-blue-50 via-white to-indigo-50',
      'from-green-50 via-white to-emerald-50',
      'from-purple-50 via-white to-violet-50',
      'from-orange-50 via-white to-yellow-50'
    ];
    
    const index = project.id % gradients.length;
    return gradients[index];
  };

  const bookingAmount = getBookingAmount();
  const gradientBg = getGradientBackground();

  return (
    <div className={`bg-gradient-to-br ${gradientBg} rounded-xl border border-gray-200 hover:border-blue-300 hover:shadow-lg transition-all duration-200 overflow-hidden group relative`}>
      {/* 顶部装饰线 */}
      <div className="h-1 bg-gradient-to-r from-blue-400 to-purple-500"></div>
      
      {/* 边角渐变装饰 */}
      <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-bl from-blue-100/50 to-transparent rounded-bl-full opacity-60"></div>
      <div className="absolute bottom-0 left-0 w-16 h-16 bg-gradient-to-tr from-purple-100/40 to-transparent rounded-tr-full opacity-60"></div>
      
      {/* 编辑和删除按钮 */}
      <div className="absolute top-3 right-3 flex gap-1 z-20">
        <button
          onClick={(e) => {
            e.stopPropagation();
            onEdit?.(project);
          }}
          className="p-1.5 bg-white/90 hover:bg-white text-gray-600 hover:text-blue-600 rounded-full shadow-sm hover:shadow transition-all duration-200"
          title="编辑项目"
        >
          <Edit className="w-4 h-4" />
        </button>
        
        <button
          onClick={(e) => {
            e.stopPropagation();
            onDelete?.(project);
          }}
          className="p-1.5 bg-white/90 hover:bg-white text-gray-600 hover:text-red-600 rounded-full shadow-sm hover:shadow transition-all duration-200"
          title="删除项目"
        >
          <Trash2 className="w-4 h-4" />
        </button>
      </div>
      
      <div className="p-4 relative z-10">
        {/* 标题行 */}
        <div className="flex items-start mb-3">
          <h3 className="text-base font-semibold text-gray-900 truncate max-w-[calc(100%-4rem)]" title={project.project_name}>
            {project.project_name}
          </h3>
        </div>

        {/* 核心信息 */}
        <div className="space-y-2 mb-3">
          <div className="flex items-center text-xs">
            <User className="w-3 h-3 text-gray-400 mr-2 flex-shrink-0" />
            <span className="text-gray-500">客户名称：</span>
            <span className="text-gray-900 font-medium ml-1">{project.client_name}</span>
          </div>

          <div className="flex items-center text-xs">
            <Calendar className="w-3 h-3 text-gray-400 mr-2 flex-shrink-0" />
            <span className="text-gray-500">创建日期：</span>
            <span className="text-gray-700 ml-1">{formatDate(project.project_date)}</span>
          </div>

          <div className="flex items-center text-xs">
            <UserCheck className="w-3 h-3 text-gray-400 mr-2 flex-shrink-0" />
            <span className="text-gray-500">创建人：</span>
            <span className="text-gray-700 ml-1">
              {project.creator_department ? `${project.creator_department}-${project.creator_name}` : project.creator_name}
            </span>
          </div>
        </div>



        {/* 分割线 */}
        <div className="border-t border-gray-200 my-3"></div>

        {/* 底部区域 */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-xs text-gray-500">预订金额</span>
            <span className="text-sm font-semibold text-blue-600">
              ¥{bookingAmount.toLocaleString()}
            </span>
          </div>
          
          <button
            onClick={() => onView?.(project)}
            className="w-full py-2 bg-blue-600 text-white text-xs font-medium rounded-md hover:bg-blue-700 transition-colors"
          >
            查看详情
          </button>
        </div>
      </div>
    </div>
  );
};

export default ProjectCard; 