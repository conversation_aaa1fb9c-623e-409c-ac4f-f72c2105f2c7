import React from 'react';
import { Plus } from 'lucide-react';

interface CreateProjectCardProps {
  onClick: () => void;
}

const CreateProjectCard: React.FC<CreateProjectCardProps> = ({ onClick }) => {
  return (
    <div 
      onClick={onClick}
      className="bg-gradient-to-br from-gray-50 via-white to-blue-50 rounded-xl border-2 border-dashed border-gray-300 hover:border-blue-400 hover:from-blue-50 hover:to-indigo-50 transition-all duration-200 cursor-pointer flex flex-col items-center justify-center min-h-[240px] group relative overflow-hidden"
    >
      {/* 顶部装饰线 */}
      <div className="h-1 w-full bg-gradient-to-r from-gray-300 to-blue-400 group-hover:from-blue-400 group-hover:to-purple-500 transition-all duration-200"></div>
      
      {/* 边角渐变装饰 */}
      <div className="absolute top-0 right-0 w-16 h-16 bg-gradient-to-bl from-blue-100/30 group-hover:from-blue-200/50 to-transparent rounded-bl-full opacity-60 transition-all duration-200"></div>
      <div className="absolute bottom-0 left-0 w-12 h-12 bg-gradient-to-tr from-gray-100/40 group-hover:from-purple-100/60 to-transparent rounded-tr-full opacity-60 transition-all duration-200"></div>
      
      <div className="flex flex-col items-center justify-center p-6 flex-1 relative z-10">
        <div className="w-12 h-12 bg-gray-100 group-hover:bg-blue-100 rounded-full flex items-center justify-center mb-3 transition-colors duration-200">
          <Plus className="w-6 h-6 text-gray-400 group-hover:text-blue-600 transition-colors duration-200" />
        </div>
        
        <div className="text-center">
          <h3 className="text-base font-semibold text-gray-900 mb-2">
            创建新项目
          </h3>
          <p className="text-xs text-gray-500 mb-4 max-w-xs leading-relaxed">
            快速创建团队项目，管理预订服务
          </p>
          <div className="inline-flex items-center px-3 py-2 bg-blue-600 group-hover:bg-blue-700 text-white rounded-md font-medium text-xs transition-colors duration-200">
            <Plus className="w-3 h-3 mr-1" />
            新建
          </div>
        </div>
      </div>
    </div>
  );
};

export default CreateProjectCard; 