import React from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { useAuth } from '@/store/auth-context';
import { Button } from '@/components/ui/button';
import {  Bell } from 'lucide-react';

const TopNav: React.FC = () => {
  const { user, logout } = useAuth();
  const navigate = useNavigate();
  // const location = useLocation();

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  return (
    <div className="h-full flex items-center px-6 bg-gradient-to-r from-blue-700/90 via-blue-500/80 to-purple-600/90 backdrop-blur">
      {/* Logo */}
      <div className="flex items-center mr-8">
        <img src="/img_home_logo_white.png" alt="Logo" className="h-10 w-auto mt-[3px]" />
        <span className="ml-2 text-lg font-semibold text-white"><Link
          to="/service"
          className={`text-base text-white hover:text-white`}
        >
          服务运营自动化平台
        </Link></span>
      </div>

      {/* 平台切换 */}
      <div className="flex items-center space-x-4">
       
        
      </div>

      {/* 右侧功能区 */}
      <div className="flex items-center space-x-4 ml-auto">
        

        {/* 通知铃铛 */}
        <button className="relative p-1.5 text-white hover:text-white">
          <Bell className="h-5 w-5" />
          <span className="absolute top-1 right-1 block h-2 w-2 rounded-full bg-red-500" />
        </button>

        {/* 用户信息和退出按钮 */}
        {user && (
          <div className="flex items-center space-x-3">
            <span className="text-sm text-white">
              {user.department ? `${user.department} - ` : ''}{user.username}（{user.work_id || user.id}）
            </span>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleLogout}
              className="text-white hover:text-white"
            >
              退出
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default TopNav; 