import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { LayoutDashboard, Settings, GripVertical, FileText, FolderOpen } from 'lucide-react';

interface SideNavProps {
  isCollapsed: boolean;
  onCollapse: (collapsed: boolean) => void;
}

const SideNav: React.FC<SideNavProps> = ({ isCollapsed, onCollapse }) => {
  const navigate = useNavigate();
  const location = useLocation();

  // 菜单项配置
  const menuItems = [
    {
      path: '/dashboard',
      label: '应用中心',
      icon: LayoutDashboard,
    },
    {
      path: '/projects',
      label: '团房团票',
      icon: FolderOpen,
    },
    {
      path: '/passport-recognition',
      label: '护照识别',
      icon: FileText,
    },
    {
      path: '/settings',
      label: '系统设置',
      icon: Settings,
    },
  ];

  // 判断当前路由是否激活
  const isActiveRoute = (path: string) => {
    return location.pathname === path;
  };

  // 处理菜单点击
  const handleMenuClick = (path: string) => {
    navigate(path);
  };

  return (
    <div className="h-full flex flex-col">
  

      {/* 导航菜单 */}
      <nav className="flex-1 py-4 px-2 space-y-1 overflow-y-auto">
        {menuItems.map((item) => {
          const Icon = item.icon;
          const isActive = isActiveRoute(item.path);
          
          return (
            <button
              key={item.path}
              onClick={() => handleMenuClick(item.path)}
              className={`w-full flex items-center ${
                isCollapsed ? 'justify-center px-2' : 'px-3'
              } py-2 rounded-md text-sm font-medium transition-colors ${
                isActive
                  ? 'text-blue-600 bg-blue-50 border-r-2 border-blue-600'
                  : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
              }`}
              title={isCollapsed ? item.label : undefined}
            >
              <Icon size={20} />
              {!isCollapsed && <span className="ml-3">{item.label}</span>}
            </button>
          );
        })}
      </nav>

      {/* 折叠按钮 */}
      <button 
        onClick={() => onCollapse(!isCollapsed)}
        className="flex-shrink-0 w-full h-10 flex items-center justify-center border-t border-gray-200 text-gray-500 hover:bg-gray-50"
      >
        <GripVertical size={16} />
      </button>
    </div>
  );
};

export default SideNav; 