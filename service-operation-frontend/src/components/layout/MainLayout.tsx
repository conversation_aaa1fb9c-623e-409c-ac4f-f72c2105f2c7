import React, { useState, useEffect } from 'react';
import { useAuth } from '@/store/auth-context';
import SideNav from './SideNav';
import TopNav from './TopNav';

interface MainLayoutProps {
  children: React.ReactNode;
}

const MainLayout: React.FC<MainLayoutProps> = ({ children }) => {
  useAuth();
  const [isCollapsed, setIsCollapsed] = useState(() => {
    const saved = localStorage.getItem('sidebarCollapsed');
    return saved ? JSON.parse(saved) : false;
  });

  useEffect(() => {
    localStorage.setItem('sidebarCollapsed', JSON.stringify(isCollapsed));
  }, [isCollapsed]);

  const handleCollapse = (collapsed: boolean) => {
    setIsCollapsed(collapsed);
  };

  // 菜单项配置

  // 判断当前路由是否激活

  // 处理菜单点击

  return (
    <div className="flex flex-col h-screen overflow-hidden">
      {/* 顶部导航栏 - 贯穿整个顶部 */}
      <header className="h-16 flex-shrink-0 bg-white border-b border-gray-200">
        <TopNav />
      </header>

      {/* 主体内容区域 */}
      <div className="flex-1 flex overflow-hidden">
        {/* 左侧菜单 - 可折叠宽度 */}
        <div className={`${isCollapsed ? 'w-12' : 'w-48'} flex-shrink-0 bg-white border-r border-gray-200 transition-all duration-300`}>
          <SideNav isCollapsed={isCollapsed} onCollapse={handleCollapse} />
        </div>
        
        {/* 右侧内容区域 - 自适应宽度且可滚动 */}
        <main className="flex-1 overflow-auto p-6">
          {children}
        </main>
      </div>
    </div>
  );
};

export default MainLayout;
