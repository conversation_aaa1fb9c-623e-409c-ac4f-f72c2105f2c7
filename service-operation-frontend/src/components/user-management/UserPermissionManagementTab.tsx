import React, { useState, useEffect } from 'react';
import { 
  UserCheck, 
  Search, 
  Settings, 
  Eye,
  RefreshCw,
  Shield,
  Key,
  Building
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { toast } from '@/hooks/use-toast';
import { Badge } from '@/components/ui/badge';

// 用户权限详情类型
interface UserPermissionDetail {
  user: {
    id: number;
    username: string;
    full_name?: string;
    email?: string;
    department?: string;
    status: boolean;
  };
  roles: Array<{
    id: number;
    role_name: string;
    role_code: string;
    is_system: boolean;
    permissions: Array<{
      id: number;
      permission_name: string;
      permission_code: string;
      permission_type: string;
    }>;
  }>;
  applications: Array<{
    id: number;
    app_name: string;
    app_code: string;
    app_url?: string;
  }>;
  special_permissions: Array<{
    id: number;
    permission_name: string;
    permission_code: string;
    permission_type: string;
  }>;
}

const UserPermissionManagementTab: React.FC = () => {
  const [users, setUsers] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(1);
  const [pageSize] = useState(20);
  const [total, setTotal] = useState(0);
  const [search, setSearch] = useState('');
  const [selectedUser, setSelectedUser] = useState<UserPermissionDetail | null>(null);
  const [isPermissionModalOpen, setIsPermissionModalOpen] = useState(false);
  const [isAssignModalOpen, setIsAssignModalOpen] = useState(false);

  // 加载用户列表
  const loadUsers = async (currentPage: number = 1) => {
    setLoading(true);
    try {
      // TODO: 调用API获取用户列表
      // const response = await userApi.getUsers(currentPage, pageSize, search);
      
      // 模拟数据
      const mockUsers = [
        {
          id: 1,
          username: 'admin',
          full_name: '系统管理员',
          email: '<EMAIL>',
          department: 'IT部门',
          status: true,
          roles_count: 1,
          apps_count: 2,
          permissions_count: 45
        },
        {
          id: 2,
          username: 'user001',
          full_name: '张三',
          email: '<EMAIL>',
          department: '业务部门',
          status: true,
          roles_count: 1,
          apps_count: 1,
          permissions_count: 12
        }
      ];
      
      setUsers(mockUsers);
      setTotal(mockUsers.length);
      setPage(currentPage);
      
    } catch (error) {
      console.error('加载用户列表失败:', error);
      toast({
        title: "加载失败",
        description: "无法加载用户列表",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // 初始化加载
  useEffect(() => {
    loadUsers(1);
  }, []);

  // 搜索处理
  const handleSearch = () => {
    setPage(1);
    loadUsers(1);
  };

  // 清空搜索
  const handleClearSearch = () => {
    setSearch('');
    setPage(1);
    loadUsers(1);
  };

  // 查看用户权限详情
  const handleViewUserPermissions = async (userId: number) => {
    try {
      // TODO: 调用API获取用户权限详情
      // const response = await userApi.getUserPermissionDetails(userId);
      
      // 模拟数据
      const mockUserDetail: UserPermissionDetail = {
        user: {
          id: userId,
          username: userId === 1 ? 'admin' : 'user001',
          full_name: userId === 1 ? '系统管理员' : '张三',
          email: userId === 1 ? '<EMAIL>' : '<EMAIL>',
          department: userId === 1 ? 'IT部门' : '业务部门',
          status: true
        },
        roles: userId === 1 ? [
          {
            id: 1,
            role_name: '超级管理员',
            role_code: 'super_admin',
            is_system: true,
            permissions: [
              { id: 1, permission_name: 'DTTrip应用访问', permission_code: 'dttrip:access', permission_type: 'menu' },
              { id: 2, permission_name: '项目管理', permission_code: 'dttrip:project', permission_type: 'menu' },
              { id: 24, permission_name: '用户管理应用访问', permission_code: 'user_mgmt:access', permission_type: 'menu' }
            ]
          }
        ] : [
          {
            id: 3,
            role_name: '普通用户',
            role_code: 'user',
            is_system: true,
            permissions: [
              { id: 1, permission_name: 'DTTrip应用访问', permission_code: 'dttrip:access', permission_type: 'menu' },
              { id: 3, permission_name: '项目查看', permission_code: 'dttrip:project:view', permission_type: 'button' }
            ]
          }
        ],
        applications: userId === 1 ? [
          { id: 1, app_name: 'DTTrip服务运营平台', app_code: 'dttrip', app_url: 'http://localhost:5173/dashboard' },
          { id: 2, app_name: '用户管理系统', app_code: 'user_management', app_url: 'http://localhost:5173/user-management' }
        ] : [
          { id: 1, app_name: 'DTTrip服务运营平台', app_code: 'dttrip', app_url: 'http://localhost:5173/dashboard' }
        ],
        special_permissions: []
      };
      
      setSelectedUser(mockUserDetail);
      setIsPermissionModalOpen(true);
      
    } catch (error) {
      console.error('获取用户权限详情失败:', error);
      toast({
        title: "获取失败",
        description: "无法获取用户权限详情",
        variant: "destructive",
      });
    }
  };

  // 分配用户权限
  const handleAssignPermissions = (userId: number) => {
    // TODO: 实现权限分配逻辑
    setIsAssignModalOpen(true);
  };

  // 获取状态徽章
  const getStatusBadge = (status: boolean) => {
    return status ? (
      <Badge variant="default" className="bg-green-100 text-green-800">启用</Badge>
    ) : (
      <Badge variant="secondary" className="bg-red-100 text-red-800">禁用</Badge>
    );
  };

  return (
    <div className="space-y-4">
      {/* 操作栏 */}
      <Card className="p-4">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <UserCheck className="h-5 w-5 text-blue-600" />
            <h3 className="text-lg font-medium text-gray-900">用户权限管理</h3>
            {total > 0 && (
              <span className="ml-2 bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded-full">
                {total}
              </span>
            )}
          </div>
          <div className="flex items-center gap-2">
            <Button
              onClick={() => loadUsers(page)}
              variant="outline"
              size="sm"
              disabled={loading}
              className="flex items-center gap-2"
            >
              <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
              刷新
            </Button>
          </div>
        </div>
        
        {/* 搜索区域 */}
        <div className="flex items-center gap-3">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="搜索用户名、姓名或邮箱..."
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>
          <Button
            onClick={handleSearch}
            size="sm"
            className="bg-blue-600 hover:bg-blue-700 text-white"
          >
            搜索
          </Button>
          <Button
            onClick={handleClearSearch}
            variant="outline"
            size="sm"
          >
            清空
          </Button>
        </div>
      </Card>

      {/* 用户权限列表 */}
      <Card className="p-0">
        {loading ? (
          <div className="flex items-center justify-center py-8">
            <RefreshCw className="h-6 w-6 animate-spin text-blue-500 mr-2" />
            <span className="text-gray-600">加载用户数据中...</span>
          </div>
        ) : users.length > 0 ? (
          <>
            <div className="overflow-x-auto">
              <table className="w-full text-sm">
                <thead className="bg-gray-50 border-b border-gray-200">
                  <tr>
                    <th className="text-left p-4 font-medium text-gray-900">用户信息</th>
                    <th className="text-left p-4 font-medium text-gray-900">角色数量</th>
                    <th className="text-left p-4 font-medium text-gray-900">应用权限</th>
                    <th className="text-left p-4 font-medium text-gray-900">权限数量</th>
                    <th className="text-left p-4 font-medium text-gray-900">状态</th>
                    <th className="text-center p-4 font-medium text-gray-900">操作</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {users.map((user) => (
                    <tr key={user.id} className="hover:bg-gray-50">
                      <td className="p-4">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-10 w-10">
                            <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                              <span className="text-sm font-medium text-blue-600">
                                {user.full_name ? user.full_name.charAt(0) : user.username.charAt(0).toUpperCase()}
                              </span>
                            </div>
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900">{user.full_name || user.username}</div>
                            <div className="text-sm text-gray-500">{user.username}</div>
                            {user.email && <div className="text-sm text-gray-500">{user.email}</div>}
                            {user.department && <div className="text-xs text-gray-400">{user.department}</div>}
                          </div>
                        </div>
                      </td>
                      <td className="p-4">
                        <div className="flex items-center gap-1">
                          <Shield className="h-4 w-4 text-blue-500" />
                          <span className="text-sm font-medium">{user.roles_count}</span>
                        </div>
                      </td>
                      <td className="p-4">
                        <div className="flex items-center gap-1">
                          <Building className="h-4 w-4 text-green-500" />
                          <span className="text-sm font-medium">{user.apps_count}</span>
                        </div>
                      </td>
                      <td className="p-4">
                        <div className="flex items-center gap-1">
                          <Key className="h-4 w-4 text-purple-500" />
                          <span className="text-sm font-medium">{user.permissions_count}</span>
                        </div>
                      </td>
                      <td className="p-4">
                        {getStatusBadge(user.status)}
                      </td>
                      <td className="text-center p-4">
                        <div className="flex items-center justify-center gap-1">
                          <button
                            onClick={() => handleViewUserPermissions(user.id)}
                            className="p-1 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded transition-colors"
                            title="查看权限详情"
                          >
                            <Eye className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => handleAssignPermissions(user.id)}
                            className="p-1 text-gray-600 hover:text-green-600 hover:bg-green-50 rounded transition-colors"
                            title="分配权限"
                          >
                            <Settings className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </>
        ) : (
          <div className="text-center py-8">
            <UserCheck className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">暂无用户数据</h3>
            <p className="text-gray-500">
              {search ? "没有找到符合搜索条件的用户" : "系统中暂无用户"}
            </p>
          </div>
        )}
      </Card>

      {/* 用户权限详情模态框 */}
      {isPermissionModalOpen && selectedUser && (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
          <div className="absolute inset-0 bg-black/50 backdrop-blur-sm" onClick={() => setIsPermissionModalOpen(false)}></div>
          <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden z-50 mx-4">
            <div className="flex justify-between items-center px-6 py-4 border-b border-gray-200">
              <h2 className="text-xl font-semibold text-gray-900">
                {selectedUser.user.full_name || selectedUser.user.username} 的权限详情
              </h2>
              <button 
                onClick={() => setIsPermissionModalOpen(false)}
                className="h-8 w-8 rounded-full text-gray-400 hover:text-gray-600 hover:bg-gray-100 flex items-center justify-center transition-colors"
              >
                ×
              </button>
            </div>
            
            <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
              <div className="space-y-6">
                {/* 用户基本信息 */}
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-3">用户信息</h3>
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <span className="text-sm text-gray-500">用户名:</span>
                        <span className="ml-2 text-sm font-medium">{selectedUser.user.username}</span>
                      </div>
                      <div>
                        <span className="text-sm text-gray-500">姓名:</span>
                        <span className="ml-2 text-sm font-medium">{selectedUser.user.full_name || '-'}</span>
                      </div>
                      <div>
                        <span className="text-sm text-gray-500">邮箱:</span>
                        <span className="ml-2 text-sm font-medium">{selectedUser.user.email || '-'}</span>
                      </div>
                      <div>
                        <span className="text-sm text-gray-500">部门:</span>
                        <span className="ml-2 text-sm font-medium">{selectedUser.user.department || '-'}</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* 角色权限 */}
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-3">角色权限</h3>
                  <div className="space-y-4">
                    {selectedUser.roles.map((role) => (
                      <div key={role.id} className="border border-gray-200 rounded-lg p-4">
                        <div className="flex items-center gap-2 mb-3">
                          <Shield className="h-5 w-5 text-blue-500" />
                          <span className="font-medium">{role.role_name}</span>
                          <Badge variant={role.is_system ? "default" : "secondary"}>
                            {role.is_system ? '系统角色' : '自定义角色'}
                          </Badge>
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                          {role.permissions.map((permission) => (
                            <div key={permission.id} className="text-sm bg-gray-50 px-3 py-2 rounded">
                              {permission.permission_name}
                            </div>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* 应用权限 */}
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-3">应用权限</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {selectedUser.applications.map((app) => (
                      <div key={app.id} className="border border-gray-200 rounded-lg p-4">
                        <div className="flex items-center gap-2 mb-2">
                          <Building className="h-5 w-5 text-green-500" />
                          <span className="font-medium">{app.app_name}</span>
                        </div>
                        <div className="text-sm text-gray-500">
                          <div>编码: {app.app_code}</div>
                          {app.app_url && <div>地址: {app.app_url}</div>}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* 特殊权限 */}
                {selectedUser.special_permissions.length > 0 && (
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-3">特殊权限</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                      {selectedUser.special_permissions.map((permission) => (
                        <div key={permission.id} className="text-sm bg-yellow-50 border border-yellow-200 px-3 py-2 rounded">
                          {permission.permission_name}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default UserPermissionManagementTab;
