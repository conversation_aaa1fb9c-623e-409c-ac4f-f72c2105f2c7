import React from 'react';
import { X, User, Shield, Key, Building, Eye } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { UserPermissionDetail } from '@/services/userManagementApi';

interface UserPermissionDetailModalProps {
  userDetail: UserPermissionDetail | null;
  isOpen: boolean;
  onClose: () => void;
}

const UserPermissionDetailModal: React.FC<UserPermissionDetailModalProps> = ({
  userDetail,
  isOpen,
  onClose
}) => {
  if (!isOpen || !userDetail) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <Card className="w-full max-w-4xl max-h-[90vh] overflow-y-auto bg-white">
        <div className="p-6">
          {/* 标题栏 */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Eye className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <h2 className="text-xl font-semibold text-gray-900">用户权限详情</h2>
                <p className="text-sm text-gray-600">查看用户的详细权限信息</p>
              </div>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={onClose}
              className="flex items-center gap-2"
            >
              <X className="h-4 w-4" />
              关闭
            </Button>
          </div>

          {/* 用户基本信息 */}
          <div className="mb-6">
            <div className="flex items-center gap-4 p-4 bg-gray-50 rounded-lg">
              <div className="flex-shrink-0 h-12 w-12">
                <div className="h-12 w-12 rounded-full bg-blue-100 flex items-center justify-center">
                  <span className="text-lg font-medium text-blue-600">
                    {userDetail.user.full_name ? userDetail.user.full_name.charAt(0) : userDetail.user.username.charAt(0).toUpperCase()}
                  </span>
                </div>
              </div>
              <div className="flex-1">
                <h3 className="text-lg font-medium text-gray-900">
                  {userDetail.user.full_name || userDetail.user.username}
                </h3>
                <p className="text-sm text-gray-600">{userDetail.user.username}</p>
                {userDetail.user.email && (
                  <p className="text-sm text-gray-500">{userDetail.user.email}</p>
                )}
                {userDetail.user.department && (
                  <p className="text-xs text-gray-400">{userDetail.user.department}</p>
                )}
              </div>
              <div className="text-right">
                <Badge variant={userDetail.user.status ? "default" : "secondary"} 
                       className={userDetail.user.status ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"}>
                  {userDetail.user.status ? "启用" : "禁用"}
                </Badge>
              </div>
            </div>
          </div>

          {/* 权限统计 */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="p-4 bg-blue-50 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <Shield className="h-5 w-5 text-blue-600" />
                <span className="font-medium text-blue-900">分配角色</span>
              </div>
              <div className="text-2xl font-bold text-blue-600">
                {userDetail.role_permissions.length}
              </div>
              <div className="text-sm text-blue-700">个角色</div>
            </div>
            
            <div className="p-4 bg-green-50 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <Building className="h-5 w-5 text-green-600" />
                <span className="font-medium text-green-900">应用权限</span>
              </div>
              <div className="text-2xl font-bold text-green-600">
                {userDetail.applications.length}
              </div>
              <div className="text-sm text-green-700">个应用</div>
            </div>
            
            <div className="p-4 bg-purple-50 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <Key className="h-5 w-5 text-purple-600" />
                <span className="font-medium text-purple-900">有效权限</span>
              </div>
              <div className="text-2xl font-bold text-purple-600">
                {userDetail.effective_permissions.length}
              </div>
              <div className="text-sm text-purple-700">个权限</div>
            </div>
          </div>

          {/* 详细信息 */}
          <div className="space-y-6">
            {/* 角色信息 */}
            <div>
              <h4 className="text-lg font-medium text-gray-900 mb-3 flex items-center gap-2">
                <Shield className="h-5 w-5 text-blue-600" />
                分配的角色
              </h4>
              {userDetail.role_permissions.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {userDetail.role_permissions.map((rolePermission) => (
                    <div key={rolePermission.role.id} className="p-3 border border-gray-200 rounded-lg">
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="font-medium text-gray-900">{rolePermission.role.role_name}</div>
                          <div className="text-sm text-gray-500">{rolePermission.role.role_code}</div>
                          <div className="text-xs text-gray-400 mt-1">
                            {rolePermission.permissions.length} 个权限
                          </div>
                        </div>
                        {rolePermission.role.is_system && (
                          <Badge variant="secondary" className="text-xs">系统角色</Badge>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-4 text-gray-500">
                  <Shield className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                  <p>未分配任何角色</p>
                </div>
              )}
            </div>

            {/* 应用权限 */}
            <div>
              <h4 className="text-lg font-medium text-gray-900 mb-3 flex items-center gap-2">
                <Building className="h-5 w-5 text-green-600" />
                应用权限
              </h4>
              {userDetail.applications.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {userDetail.applications.map((app) => (
                    <div key={app.id} className="p-3 border border-gray-200 rounded-lg">
                      <div className="flex items-center gap-3">
                        {app.icon && (
                          <img src={app.icon} alt={app.app_name} className="h-8 w-8" />
                        )}
                        <div>
                          <div className="font-medium text-gray-900">{app.app_name}</div>
                          <div className="text-sm text-gray-500">{app.app_code}</div>
                          {app.description && (
                            <div className="text-xs text-gray-400 mt-1">{app.description}</div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-4 text-gray-500">
                  <Building className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                  <p>未分配任何应用权限</p>
                </div>
              )}
            </div>

            {/* 有效权限 */}
            <div>
              <h4 className="text-lg font-medium text-gray-900 mb-3 flex items-center gap-2">
                <Key className="h-5 w-5 text-purple-600" />
                有效权限
              </h4>
              {userDetail.effective_permissions.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {userDetail.effective_permissions.map((permission) => (
                    <div key={permission.id} className="p-3 border border-gray-200 rounded-lg">
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="font-medium text-gray-900">{permission.permission_name}</div>
                          <div className="text-sm text-gray-500">{permission.permission_code}</div>
                          {permission.description && (
                            <div className="text-xs text-gray-400 mt-1">{permission.description}</div>
                          )}
                        </div>
                        <div className="flex flex-col gap-1">
                          {permission.from_role && (
                            <Badge variant="outline" className="text-xs">角色权限</Badge>
                          )}
                          {permission.from_user && (
                            <Badge variant="secondary" className="text-xs">特殊权限</Badge>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-4 text-gray-500">
                  <Key className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                  <p>无有效权限</p>
                </div>
              )}
            </div>
          </div>

          {/* 底部按钮 */}
          <div className="flex justify-end mt-6 pt-4 border-t border-gray-200">
            <Button
              variant="outline"
              onClick={onClose}
            >
              关闭
            </Button>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default UserPermissionDetailModal;
