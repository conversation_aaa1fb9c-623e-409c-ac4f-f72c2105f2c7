import React, { useState, useEffect } from 'react';
import {
  Shield,
  Plus,
  Search,
  Edit,
  RefreshCw
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { toast } from '@/hooks/use-toast';
import { Badge } from '@/components/ui/badge';
import { userManagementApi, Role } from '@/services/userManagementApi';
import RoleEditModal from './RoleEditModal';

// 类型定义已从 userManagementApi 导入

const RoleManagementTab: React.FC = () => {
  const [roles, setRoles] = useState<Role[]>([]);
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(1);
  const [pageSize] = useState(20);
  const [total, setTotal] = useState(0);
  const [search, setSearch] = useState('');
  const [selectedRole, setSelectedRole] = useState<Role | null>(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);

  // 加载角色列表
  const loadRoles = async (currentPage: number = 1) => {
    setLoading(true);
    try {
      const response = await userManagementApi.getRoles(currentPage, pageSize, search);
      setRoles(response.items);
      setTotal(response.total);
      setPage(currentPage);
    } catch (error) {
      console.error('加载角色列表失败:', error);
      toast({
        title: "加载失败",
        description: "无法加载角色列表",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // 初始化加载
  useEffect(() => {
    loadRoles(1);
  }, []);

  // 搜索处理
  const handleSearch = () => {
    setPage(1);
    loadRoles(1);
  };

  // 清空搜索
  const handleClearSearch = () => {
    setSearch('');
    setPage(1);
    loadRoles(1);
  };

  // 处理编辑角色
  const handleEditRole = (role: Role) => {
    setSelectedRole(role);
    setIsEditModalOpen(true);
  };

  // 编辑成功后的回调
  const handleEditSuccess = () => {
    loadRoles(page);
  };

  // 格式化日期
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN');
  };

  // 获取状态徽章
  const getStatusBadge = (status: boolean) => {
    return status ? (
      <Badge variant="default" className="bg-green-100 text-green-800">启用</Badge>
    ) : (
      <Badge variant="secondary" className="bg-red-100 text-red-800">禁用</Badge>
    );
  };

  // 获取系统角色徽章
  const getSystemBadge = (isSystem: boolean) => {
    return isSystem ? (
      <Badge variant="default" className="bg-blue-100 text-blue-800">系统角色</Badge>
    ) : (
      <Badge variant="outline">自定义角色</Badge>
    );
  };

  return (
    <div className="space-y-4">
      {/* 操作栏 */}
      <Card className="p-4">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <Shield className="h-5 w-5 text-blue-600" />
            <h3 className="text-lg font-medium text-gray-900">角色管理</h3>
            {total > 0 && (
              <span className="ml-2 bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded-full">
                {total}
              </span>
            )}
          </div>
          <div className="flex items-center gap-2">
            {/* 删除了刷新和新建角色按钮 */}
          </div>
        </div>
        
        {/* 搜索区域 */}
        <div className="flex items-center gap-3">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="搜索角色名称..."
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>
          <Button
            onClick={handleSearch}
            size="sm"
            className="bg-blue-600 hover:bg-blue-700 text-white"
          >
            搜索
          </Button>
          <Button
            onClick={handleClearSearch}
            variant="outline"
            size="sm"
          >
            重置
          </Button>
        </div>
      </Card>

      {/* 角色列表 */}
      <Card className="p-0">
        {loading ? (
          <div className="flex items-center justify-center py-8">
            <RefreshCw className="h-6 w-6 animate-spin text-blue-500 mr-2" />
            <span className="text-gray-600">加载角色数据中...</span>
          </div>
        ) : roles.length > 0 ? (
          <>
            <div className="overflow-x-auto">
              <table className="w-full text-sm">
                <thead className="bg-gray-50 border-b border-gray-200">
                  <tr>
                    <th className="text-left p-4 font-medium text-gray-900">角色信息</th>
                    <th className="text-left p-4 font-medium text-gray-900">角色编码</th>
                    <th className="text-left p-4 font-medium text-gray-900">类型</th>
                    <th className="text-left p-4 font-medium text-gray-900">状态</th>
                    <th className="text-left p-4 font-medium text-gray-900">创建时间</th>
                    <th className="text-center p-4 font-medium text-gray-900">操作</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {roles.map((role) => (
                    <tr key={role.id} className="hover:bg-gray-50">
                      <td className="p-4">
                        <div>
                          <div className="text-sm font-medium text-gray-900">{role.role_name}</div>
                          {role.description && (
                            <div className="text-sm text-gray-500 mt-1">{role.description}</div>
                          )}
                        </div>
                      </td>
                      <td className="p-4">
                        <code className="text-sm bg-gray-100 px-2 py-1 rounded">{role.role_code}</code>
                      </td>
                      <td className="p-4">
                        {getSystemBadge(role.is_system)}
                      </td>
                      <td className="p-4">
                        {getStatusBadge(role.status)}
                      </td>
                      <td className="p-4">
                        <div className="text-sm text-gray-900">{formatDate(role.created_at)}</div>
                        {role.created_by && (
                          <div className="text-xs text-gray-500">创建人: {role.created_by}</div>
                        )}
                      </td>
                      <td className="text-center p-4">
                        <div className="flex items-center justify-center gap-1">
                          <button
                            onClick={() => handleEditRole(role)}
                            className="p-1 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded transition-colors"
                            title="编辑角色"
                            disabled={role.is_system}
                          >
                            <Edit className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </>
        ) : (
          <div className="text-center py-8">
            <Shield className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">暂无角色数据</h3>
            <p className="text-gray-500">
              {search ? "没有找到符合搜索条件的角色" : "系统中暂无角色"}
            </p>
          </div>
        )}
      </Card>

      {/* 角色编辑模态框 */}
      <RoleEditModal
        role={selectedRole}
        isOpen={isEditModalOpen}
        onClose={() => {
          setIsEditModalOpen(false);
          setSelectedRole(null);
        }}
        onSuccess={handleEditSuccess}
      />
    </div>
  );
};

export default RoleManagementTab;
