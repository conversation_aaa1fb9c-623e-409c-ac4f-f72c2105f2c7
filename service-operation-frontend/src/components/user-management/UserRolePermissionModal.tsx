import React, { useState, useEffect } from 'react';
import { X, Save, User, Shield, Key, Users } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { toast } from '@/hooks/use-toast';
import { Checkbox } from '@/components/ui/checkbox';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { userManagementApi, User as UserType, Role, Permission } from '@/services/userManagementApi';

interface UserRolePermissionModalProps {
  user: UserType | null;
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

interface RoleWithAssigned extends Role {
  assigned: boolean;
}

interface PermissionWithStatus extends Permission {
  has_role_permission: boolean;
  has_user_permission: boolean;
  effective: boolean;
}

const UserRolePermissionModal: React.FC<UserRolePermissionModalProps> = ({
  user,
  isOpen,
  onClose,
  onSuccess
}) => {
  const [roles, setRoles] = useState<RoleWithAssigned[]>([]);
  const [permissions, setPermissions] = useState<PermissionWithStatus[]>([]);
  const [selectedRoleIds, setSelectedRoleIds] = useState<number[]>([]);
  const [selectedPermissionIds, setSelectedPermissionIds] = useState<number[]>([]);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [activeTab, setActiveTab] = useState('roles');

  // 加载用户角色权限数据
  const loadUserRolesPermissions = async () => {
    if (!user) return;
    
    setLoading(true);
    try {
      const response = await userManagementApi.getUserRolesPermissions(user.id);
      setRoles(response.roles);
      setPermissions(response.permissions);
      setSelectedRoleIds(response.assigned_role_ids);
      setSelectedPermissionIds(response.user_permission_ids);
    } catch (error) {
      console.error('加载用户角色权限失败:', error);
      toast({
        title: "加载失败",
        description: "无法加载用户角色权限数据",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // 当用户变化时重新加载数据
  useEffect(() => {
    if (isOpen && user) {
      loadUserRolesPermissions();
    }
  }, [isOpen, user]);

  // 处理角色选择
  const handleRoleChange = (roleId: number, checked: boolean) => {
    setSelectedRoleIds(prev => {
      if (checked) {
        return [...prev, roleId];
      } else {
        return prev.filter(id => id !== roleId);
      }
    });
  };

  // 处理权限选择
  const handlePermissionChange = (permissionId: number, checked: boolean) => {
    setSelectedPermissionIds(prev => {
      if (checked) {
        return [...prev, permissionId];
      } else {
        return prev.filter(id => id !== permissionId);
      }
    });
  };

  // 全选角色
  const handleSelectAllRoles = (checked: boolean) => {
    if (checked) {
      setSelectedRoleIds(roles.map(r => r.id));
    } else {
      setSelectedRoleIds([]);
    }
  };

  // 全选权限
  const handleSelectAllPermissions = (checked: boolean) => {
    if (checked) {
      setSelectedPermissionIds(permissions.map(p => p.id));
    } else {
      setSelectedPermissionIds([]);
    }
  };

  // 保存角色分配
  const handleSaveRoles = async () => {
    if (!user) return;

    setSaving(true);
    try {
      const result = await userManagementApi.assignUserRoles(user.id, selectedRoleIds);
      
      toast({
        title: "保存成功",
        description: `已为用户 ${user.username} 分配 ${result.assigned_count} 个角色`,
      });
      
      // 重新加载数据以更新权限状态
      await loadUserRolesPermissions();
    } catch (error) {
      console.error('保存用户角色失败:', error);
      toast({
        title: "保存失败",
        description: error instanceof Error ? error.message : "保存用户角色时发生错误",
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  // 保存权限分配
  const handleSavePermissions = async () => {
    if (!user) return;

    setSaving(true);
    try {
      const result = await userManagementApi.assignUserPermissions(user.id, selectedPermissionIds);
      
      toast({
        title: "保存成功",
        description: `已为用户 ${user.username} 分配 ${result.assigned_count} 个特殊权限`,
      });
      
      // 重新加载数据以更新权限状态
      await loadUserRolesPermissions();
    } catch (error) {
      console.error('保存用户权限失败:', error);
      toast({
        title: "保存失败",
        description: error instanceof Error ? error.message : "保存用户权限时发生错误",
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  // 保存所有更改
  const handleSaveAll = async () => {
    if (!user) return;

    setSaving(true);
    try {
      // 先保存角色
      await userManagementApi.assignUserRoles(user.id, selectedRoleIds);
      // 再保存权限
      await userManagementApi.assignUserPermissions(user.id, selectedPermissionIds);
      
      toast({
        title: "保存成功",
        description: `用户 ${user.username} 的角色和权限已更新`,
      });
      
      onSuccess();
      onClose();
    } catch (error) {
      console.error('保存用户角色权限失败:', error);
      toast({
        title: "保存失败",
        description: error instanceof Error ? error.message : "保存用户角色权限时发生错误",
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  if (!isOpen || !user) return null;

  const allRolesSelected = roles.length > 0 && selectedRoleIds.length === roles.length;
  const allPermissionsSelected = permissions.length > 0 && selectedPermissionIds.length === permissions.length;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <Card className="w-full max-w-6xl max-h-[90vh] overflow-y-auto bg-white">
        <div className="p-6">
          {/* 标题栏 */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Users className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <h2 className="text-xl font-semibold text-gray-900">用户权限管理</h2>
                <p className="text-sm text-gray-600">
                  为用户 "{user.username}" ({user.full_name || user.username}) 分配角色和权限
                </p>
              </div>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={onClose}
              className="flex items-center gap-2"
            >
              <X className="h-4 w-4" />
              关闭
            </Button>
          </div>

          {/* 标签页 */}
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="roles" className="flex items-center gap-2">
                <Shield className="h-4 w-4" />
                角色分配
              </TabsTrigger>
              <TabsTrigger value="permissions" className="flex items-center gap-2">
                <Key className="h-4 w-4" />
                权限分配
              </TabsTrigger>
            </TabsList>

            {/* 角色分配标签页 */}
            <TabsContent value="roles" className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                  <Checkbox
                    checked={allRolesSelected}
                    onCheckedChange={handleSelectAllRoles}
                    className="data-[state=checked]:bg-blue-600 data-[state=checked]:border-blue-600"
                  />
                  <span className="font-medium text-gray-900">
                    全选角色 ({selectedRoleIds.length}/{roles.length})
                  </span>
                </div>
                <Button
                  onClick={handleSaveRoles}
                  disabled={saving || loading}
                  className="bg-blue-600 hover:bg-blue-700 text-white flex items-center gap-2"
                >
                  <Save className="h-4 w-4" />
                  保存角色
                </Button>
              </div>

              {loading ? (
                <div className="flex items-center justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                  <span className="ml-2 text-gray-600">加载角色数据中...</span>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {roles.map((role) => (
                    <div
                      key={role.id}
                      className="flex items-center gap-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50"
                    >
                      <Checkbox
                        checked={selectedRoleIds.includes(role.id)}
                        onCheckedChange={(checked) => handleRoleChange(role.id, checked as boolean)}
                        className="data-[state=checked]:bg-blue-600 data-[state=checked]:border-blue-600"
                      />
                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <Shield className="h-4 w-4 text-blue-600" />
                          <span className="font-medium text-gray-900">{role.role_name}</span>
                          {role.is_system && (
                            <span className="text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded">系统</span>
                          )}
                        </div>
                        <div className="text-sm text-gray-500 mt-1">
                          <code className="bg-gray-100 px-1 py-0.5 rounded text-xs">
                            {role.role_code}
                          </code>
                        </div>
                        {role.description && (
                          <div className="text-xs text-gray-400 mt-1">
                            {role.description}
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </TabsContent>

            {/* 权限分配标签页 */}
            <TabsContent value="permissions" className="space-y-4">
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                <div className="flex items-center gap-2 text-blue-800 mb-2">
                  <Key className="h-4 w-4" />
                  <span className="font-medium">权限说明</span>
                </div>
                <div className="text-sm text-blue-700 space-y-1">
                  <p>• <strong>角色权限</strong>：通过分配的角色自动获得的权限（灰色背景）</p>
                  <p>• <strong>特殊权限</strong>：直接分配给用户的额外权限（可编辑）</p>
                  <p>• 用户最终拥有的权限 = 角色权限 + 特殊权限</p>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                  <Checkbox
                    checked={allPermissionsSelected}
                    onCheckedChange={handleSelectAllPermissions}
                    className="data-[state=checked]:bg-blue-600 data-[state=checked]:border-blue-600"
                  />
                  <span className="font-medium text-gray-900">
                    全选特殊权限 ({selectedPermissionIds.length}/{permissions.length})
                  </span>
                </div>
                <Button
                  onClick={handleSavePermissions}
                  disabled={saving || loading}
                  className="bg-green-600 hover:bg-green-700 text-white flex items-center gap-2"
                >
                  <Save className="h-4 w-4" />
                  保存权限
                </Button>
              </div>

              {loading ? (
                <div className="flex items-center justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                  <span className="ml-2 text-gray-600">加载权限数据中...</span>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {permissions.map((permission) => (
                    <div
                      key={permission.id}
                      className={`flex items-center gap-3 p-3 border rounded-lg ${
                        permission.has_role_permission 
                          ? 'bg-gray-50 border-gray-300' 
                          : 'border-gray-200 hover:bg-gray-50'
                      }`}
                    >
                      <Checkbox
                        checked={selectedPermissionIds.includes(permission.id)}
                        onCheckedChange={(checked) => handlePermissionChange(permission.id, checked as boolean)}
                        className="data-[state=checked]:bg-green-600 data-[state=checked]:border-green-600"
                      />
                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <Key className={`h-4 w-4 ${permission.effective ? 'text-green-600' : 'text-gray-400'}`} />
                          <span className="font-medium text-gray-900">{permission.permission_name}</span>
                          {permission.has_role_permission && (
                            <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">角色权限</span>
                          )}
                          {permission.has_user_permission && (
                            <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">特殊权限</span>
                          )}
                        </div>
                        <div className="text-sm text-gray-500 mt-1">
                          <code className="bg-gray-100 px-1 py-0.5 rounded text-xs">
                            {permission.permission_code}
                          </code>
                        </div>
                        {permission.description && (
                          <div className="text-xs text-gray-400 mt-1">
                            {permission.description}
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </TabsContent>
          </Tabs>

          {/* 操作按钮 */}
          <div className="flex items-center justify-end gap-3 mt-6 pt-4 border-t border-gray-200">
            <Button
              variant="outline"
              onClick={onClose}
              disabled={saving}
            >
              取消
            </Button>
            <Button
              onClick={handleSaveAll}
              disabled={saving || loading}
              className="bg-blue-600 hover:bg-blue-700 text-white flex items-center gap-2"
            >
              {saving ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  保存中...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4" />
                  保存所有更改
                </>
              )}
            </Button>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default UserRolePermissionModal;
