import React, { useState, useEffect } from 'react';
import {
  Users,
  Plus,
  Search,
  Edit,
  Trash2,
  Eye,
  UserPlus,
  RefreshCw,
  Download,
  Filter,
  UserCheck,
  UserX
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { toast } from '@/hooks/use-toast';
import { Badge } from '@/components/ui/badge';
import { userManagementApi, User, Role, Application } from '@/services/userManagementApi';
import UserEditModal from './UserEditModal';

// 类型定义已从 userManagementApi 导入

const UserManagementTab: React.FC = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(1);
  const [pageSize] = useState(20);
  const [total, setTotal] = useState(0);
  const [search, setSearch] = useState('');
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);

  // 加载用户列表
  const loadUsers = async (currentPage: number = 1) => {
    setLoading(true);
    try {
      const response = await userManagementApi.getUsers(currentPage, pageSize, search);
      setUsers(response.items);
      setTotal(response.total);
      setPage(currentPage);
    } catch (error) {
      console.error('加载用户列表失败:', error);
      toast({
        title: "加载失败",
        description: "无法加载用户列表",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // 初始化加载
  useEffect(() => {
    loadUsers(1);
  }, []);

  // 搜索处理
  const handleSearch = () => {
    setPage(1);
    loadUsers(1);
  };

  // 清空搜索
  const handleClearSearch = () => {
    setSearch('');
    setPage(1);
    loadUsers(1);
  };

  // 格式化日期
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN');
  };

  // 获取状态徽章
  const getStatusBadge = (status: boolean) => {
    return status ? (
      <Badge variant="default" className="bg-green-100 text-green-800">启用</Badge>
    ) : (
      <Badge variant="secondary" className="bg-red-100 text-red-800">禁用</Badge>
    );
  };

  // 处理创建用户
  const handleCreateUser = () => {
    setIsCreateModalOpen(true);
  };

  // 处理编辑用户
  const handleEditUser = (user: User) => {
    setSelectedUser(user);
    setIsEditModalOpen(true);
  };

  // 编辑成功后的回调
  const handleEditSuccess = () => {
    loadUsers(page);
  };

  // 处理查看用户
  const handleViewUser = (user: User) => {
    setSelectedUser(user);
    setIsViewModalOpen(true);
  };

  // 处理用户状态切换（启用/停用）
  const handleToggleUserStatus = async (user: User) => {
    try {
      const newStatus = !user.status;
      await userManagementApi.updateUserStatus(user.id, newStatus);

      const action = user.status ? "停用" : "启用";
      toast({
        title: `${action}成功`,
        description: `用户 ${user.username} 已被${action}`,
      });

      // 重新加载用户列表
      loadUsers(page);
    } catch (error) {
      console.error('切换用户状态失败:', error);
      const action = user.status ? "停用" : "启用";
      toast({
        title: `${action}失败`,
        description: `${action}用户时发生错误`,
        variant: "destructive",
      });
    }
  };

  return (
    <div className="space-y-4">
      {/* 操作栏 */}
      <Card className="p-4">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <Users className="h-5 w-5 text-blue-600" />
            <h3 className="text-lg font-medium text-gray-900">用户管理</h3>
            {total > 0 && (
              <span className="ml-2 bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded-full">
                {total}
              </span>
            )}
          </div>
          <div className="flex items-center gap-2">
            {/* 删除了刷新和新建用户按钮 */}
          </div>
        </div>
        
        {/* 搜索区域 */}
        <div className="flex items-center gap-3">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="搜索用户名、姓名或邮箱..."
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>
          <Button
            onClick={handleSearch}
            size="sm"
            className="bg-blue-600 hover:bg-blue-700 text-white"
          >
            搜索
          </Button>
          <Button
            onClick={handleClearSearch}
            variant="outline"
            size="sm"
          >
            重置
          </Button>
        </div>
      </Card>

      {/* 用户列表 */}
      <Card className="p-0">
        {loading ? (
          <div className="flex items-center justify-center py-8">
            <RefreshCw className="h-6 w-6 animate-spin text-blue-500 mr-2" />
            <span className="text-gray-600">加载用户数据中...</span>
          </div>
        ) : users.length > 0 ? (
          <>
            <div className="overflow-x-auto">
              <table className="w-full text-sm">
                <thead className="bg-gray-50 border-b border-gray-200">
                  <tr>
                    <th className="text-left p-4 font-medium text-gray-900">用户名</th>
                    <th className="text-left p-4 font-medium text-gray-900">姓名</th>
                    <th className="text-left p-4 font-medium text-gray-900">工号</th>
                    <th className="text-left p-4 font-medium text-gray-900">邮箱</th>
                    <th className="text-left p-4 font-medium text-gray-900">部门</th>
                    <th className="text-left p-4 font-medium text-gray-900">角色</th>
                    <th className="text-left p-4 font-medium text-gray-900">应用权限</th>
                    <th className="text-left p-4 font-medium text-gray-900">状态</th>
                    <th className="text-left p-4 font-medium text-gray-900">创建时间</th>
                    <th className="text-center p-4 font-medium text-gray-900">操作</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {users.map((user) => (
                    <tr key={user.id} className="hover:bg-gray-50">
                      <td className="p-4">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-8 w-8">
                            <div className="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center">
                              <span className="text-xs font-medium text-blue-600">
                                {user.username ? user.username.charAt(0).toUpperCase() : (user as any).member_id?.charAt(0).toUpperCase() || 'U'}
                              </span>
                            </div>
                          </div>
                          <div className="ml-3">
                            <div className="text-sm font-medium text-gray-900">{(user as any).member_id || user.username || '-'}</div>
                          </div>
                        </div>
                      </td>
                      <td className="p-4">
                        <div className="text-sm text-gray-900">{user.username || '-'}</div>
                      </td>
                      <td className="p-4">
                        <div className="text-sm text-gray-900">{(user as any).work_id || '-'}</div>
                      </td>
                      <td className="p-4">
                        <div className="text-sm text-gray-900">{user.email || '-'}</div>
                      </td>
                      <td className="p-4">
                        <div className="text-sm text-gray-900">{user.department || '-'}</div>
                      </td>
                      <td className="p-4">
                        <div className="flex flex-wrap gap-1">
                          {user.roles.map((role) => (
                            <Badge
                              key={role.id}
                              variant={role.is_system ? "default" : "secondary"}
                              className="text-xs"
                            >
                              {role.role_name}
                            </Badge>
                          ))}
                        </div>
                      </td>
                      <td className="p-4">
                        <div className="flex flex-wrap gap-1">
                          {user.applications.map((app) => (
                            <Badge key={app.id} variant="outline" className="text-xs">
                              {app.app_name}
                            </Badge>
                          ))}
                        </div>
                      </td>
                      <td className="p-4">
                        {getStatusBadge(user.status)}
                      </td>
                      <td className="p-4">
                        <div className="text-sm text-gray-900">{formatDate(user.created_at)}</div>
                      </td>
                      <td className="text-center p-4">
                        <div className="flex items-center justify-center gap-1">
                          <button
                            onClick={() => handleEditUser(user)}
                            className="p-1 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded transition-colors"
                            title="编辑用户"
                          >
                            <Edit className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => handleToggleUserStatus(user)}
                            className={`p-1 text-gray-600 rounded transition-colors ${
                              user.status
                                ? 'hover:text-red-600 hover:bg-red-50'
                                : 'hover:text-green-600 hover:bg-green-50'
                            }`}
                            title={user.status ? "停用用户" : "启用用户"}
                          >
                            {user.status ? (
                              <UserX className="h-4 w-4" />
                            ) : (
                              <UserCheck className="h-4 w-4" />
                            )}
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
            
            {/* 分页 */}
            {Math.ceil(total / pageSize) > 1 && (
              <div className="flex items-center justify-between p-4 border-t border-gray-200">
                <div className="text-sm text-gray-700">
                  显示第 {(page - 1) * pageSize + 1} 到 {Math.min(page * pageSize, total)} 条，共 {total} 条记录
                </div>
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => loadUsers(page - 1)}
                    disabled={page <= 1 || loading}
                  >
                    上一页
                  </Button>
                  <span className="text-sm text-gray-700">
                    第 {page} 页，共 {Math.ceil(total / pageSize)} 页
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => loadUsers(page + 1)}
                    disabled={page >= Math.ceil(total / pageSize) || loading}
                  >
                    下一页
                  </Button>
                </div>
              </div>
            )}
          </>
        ) : (
          <div className="text-center py-8">
            <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">暂无用户数据</h3>
            <p className="text-gray-500">
              {search ? "没有找到符合搜索条件的用户" : "系统中暂无用户"}
            </p>
          </div>
        )}
      </Card>

      {/* 用户编辑模态框 */}
      <UserEditModal
        user={selectedUser}
        isOpen={isEditModalOpen}
        onClose={() => {
          setIsEditModalOpen(false);
          setSelectedUser(null);
        }}
        onSuccess={handleEditSuccess}
      />
    </div>
  );
};

export default UserManagementTab;
