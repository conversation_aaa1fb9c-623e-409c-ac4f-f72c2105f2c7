import React, { useState, useEffect } from 'react';
import { 
  Key, 
  Plus, 
  Search, 
  Edit, 
  Trash2, 
  Eye,
  RefreshCw,
  Filter
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { toast } from '@/hooks/use-toast';
import { Badge } from '@/components/ui/badge';

// 权限数据类型
interface Permission {
  id: number;
  permission_name: string;
  permission_code: string;
  permission_type: 'menu' | 'button' | 'api' | 'data';
  parent_id: number;
  app_id?: number;
  app_name?: string;
  resource_path?: string;
  description?: string;
  sort_order: number;
  status: boolean;
  created_at: string;
  updated_at: string;
  created_by?: string;
  updated_by?: string;
}

const PermissionManagementTab: React.FC = () => {
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(1);
  const [pageSize] = useState(20);
  const [total, setTotal] = useState(0);
  const [search, setSearch] = useState('');
  const [selectedAppId, setSelectedAppId] = useState<number | null>(null);

  // 加载权限列表
  const loadPermissions = async (currentPage: number = 1) => {
    setLoading(true);
    try {
      // TODO: 调用API获取权限列表
      // const response = await permissionApi.getPermissions(currentPage, pageSize, search, selectedAppId);
      
      // 模拟数据
      const mockPermissions: Permission[] = [
        {
          id: 1,
          permission_name: 'DTTrip应用访问',
          permission_code: 'dttrip:access',
          permission_type: 'menu',
          parent_id: 0,
          app_id: 1,
          app_name: 'DTTrip服务运营平台',
          resource_path: '/dashboard',
          description: 'DTTrip应用访问权限',
          sort_order: 1,
          status: true,
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-01T00:00:00Z',
          created_by: 'system'
        },
        {
          id: 2,
          permission_name: '项目管理',
          permission_code: 'dttrip:project',
          permission_type: 'menu',
          parent_id: 1,
          app_id: 1,
          app_name: 'DTTrip服务运营平台',
          resource_path: '/projects',
          description: '项目管理模块',
          sort_order: 10,
          status: true,
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-01T00:00:00Z',
          created_by: 'system'
        },
        {
          id: 3,
          permission_name: '项目查看',
          permission_code: 'dttrip:project:view',
          permission_type: 'button',
          parent_id: 2,
          app_id: 1,
          app_name: 'DTTrip服务运营平台',
          resource_path: '/projects',
          description: '查看项目列表',
          sort_order: 11,
          status: true,
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-01T00:00:00Z',
          created_by: 'system'
        },
        {
          id: 24,
          permission_name: '用户管理应用访问',
          permission_code: 'user_mgmt:access',
          permission_type: 'menu',
          parent_id: 0,
          app_id: 2,
          app_name: '用户管理系统',
          resource_path: '/user-management',
          description: '用户管理应用访问权限',
          sort_order: 1,
          status: true,
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-01T00:00:00Z',
          created_by: 'system'
        }
      ];
      
      setPermissions(mockPermissions);
      setTotal(mockPermissions.length);
      setPage(currentPage);
      
    } catch (error) {
      console.error('加载权限列表失败:', error);
      toast({
        title: "加载失败",
        description: "无法加载权限列表",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // 初始化加载
  useEffect(() => {
    loadPermissions(1);
  }, []);

  // 搜索处理
  const handleSearch = () => {
    setPage(1);
    loadPermissions(1);
  };

  // 清空搜索
  const handleClearSearch = () => {
    setSearch('');
    setSelectedAppId(null);
    setPage(1);
    loadPermissions(1);
  };

  // 格式化日期
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN');
  };

  // 获取状态徽章
  const getStatusBadge = (status: boolean) => {
    return status ? (
      <Badge variant="default" className="bg-green-100 text-green-800">启用</Badge>
    ) : (
      <Badge variant="secondary" className="bg-red-100 text-red-800">禁用</Badge>
    );
  };

  // 获取权限类型徽章
  const getPermissionTypeBadge = (type: string) => {
    const typeMap = {
      menu: { label: '菜单', className: 'bg-blue-100 text-blue-800' },
      button: { label: '按钮', className: 'bg-green-100 text-green-800' },
      api: { label: 'API', className: 'bg-purple-100 text-purple-800' },
      data: { label: '数据', className: 'bg-orange-100 text-orange-800' }
    };
    
    const config = typeMap[type as keyof typeof typeMap] || { label: type, className: 'bg-gray-100 text-gray-800' };
    
    return (
      <Badge variant="secondary" className={config.className}>
        {config.label}
      </Badge>
    );
  };

  return (
    <div className="space-y-4">
      {/* 操作栏 */}
      <Card className="p-4">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <Key className="h-5 w-5 text-blue-600" />
            <h3 className="text-lg font-medium text-gray-900">权限管理</h3>
            {total > 0 && (
              <span className="ml-2 bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded-full">
                {total}
              </span>
            )}
          </div>
          <div className="flex items-center gap-2">
            <Button
              onClick={() => loadPermissions(page)}
              variant="outline"
              size="sm"
              disabled={loading}
              className="flex items-center gap-2"
            >
              <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
              刷新
            </Button>
            <Button
              size="sm"
              className="bg-blue-600 hover:bg-blue-700 text-white flex items-center gap-2"
            >
              <Plus className="h-4 w-4" />
              新建权限
            </Button>
          </div>
        </div>
        
        {/* 搜索区域 */}
        <div className="flex items-center gap-3">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="搜索权限名称或编码..."
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>
          <select
            value={selectedAppId || ''}
            onChange={(e) => setSelectedAppId(e.target.value ? Number(e.target.value) : null)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="">所有应用</option>
            <option value="1">DTTrip服务运营平台</option>
            <option value="2">用户管理系统</option>
          </select>
          <Button
            onClick={handleSearch}
            size="sm"
            className="bg-blue-600 hover:bg-blue-700 text-white"
          >
            搜索
          </Button>
          <Button
            onClick={handleClearSearch}
            variant="outline"
            size="sm"
          >
            清空
          </Button>
        </div>
      </Card>

      {/* 权限列表 */}
      <Card className="p-0">
        {loading ? (
          <div className="flex items-center justify-center py-8">
            <RefreshCw className="h-6 w-6 animate-spin text-blue-500 mr-2" />
            <span className="text-gray-600">加载权限数据中...</span>
          </div>
        ) : permissions.length > 0 ? (
          <>
            <div className="overflow-x-auto">
              <table className="w-full text-sm">
                <thead className="bg-gray-50 border-b border-gray-200">
                  <tr>
                    <th className="text-left p-4 font-medium text-gray-900">权限信息</th>
                    <th className="text-left p-4 font-medium text-gray-900">权限编码</th>
                    <th className="text-left p-4 font-medium text-gray-900">类型</th>
                    <th className="text-left p-4 font-medium text-gray-900">所属应用</th>
                    <th className="text-left p-4 font-medium text-gray-900">资源路径</th>
                    <th className="text-left p-4 font-medium text-gray-900">状态</th>
                    <th className="text-center p-4 font-medium text-gray-900">操作</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {permissions.map((permission) => (
                    <tr key={permission.id} className="hover:bg-gray-50">
                      <td className="p-4">
                        <div>
                          <div className="text-sm font-medium text-gray-900">{permission.permission_name}</div>
                          {permission.description && (
                            <div className="text-sm text-gray-500 mt-1">{permission.description}</div>
                          )}
                          <div className="text-xs text-gray-400 mt-1">
                            排序: {permission.sort_order} | 父级ID: {permission.parent_id}
                          </div>
                        </div>
                      </td>
                      <td className="p-4">
                        <code className="text-sm bg-gray-100 px-2 py-1 rounded">{permission.permission_code}</code>
                      </td>
                      <td className="p-4">
                        {getPermissionTypeBadge(permission.permission_type)}
                      </td>
                      <td className="p-4">
                        {permission.app_name ? (
                          <Badge variant="outline">{permission.app_name}</Badge>
                        ) : (
                          <span className="text-gray-400">-</span>
                        )}
                      </td>
                      <td className="p-4">
                        {permission.resource_path ? (
                          <code className="text-xs bg-gray-100 px-2 py-1 rounded">{permission.resource_path}</code>
                        ) : (
                          <span className="text-gray-400">-</span>
                        )}
                      </td>
                      <td className="p-4">
                        {getStatusBadge(permission.status)}
                      </td>
                      <td className="text-center p-4">
                        <div className="flex items-center justify-center gap-1">
                          <button
                            className="p-1 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded transition-colors"
                            title="查看详情"
                          >
                            <Eye className="h-4 w-4" />
                          </button>
                          <button
                            className="p-1 text-gray-600 hover:text-green-600 hover:bg-green-50 rounded transition-colors"
                            title="编辑权限"
                          >
                            <Edit className="h-4 w-4" />
                          </button>
                          <button
                            className="p-1 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded transition-colors"
                            title="删除权限"
                          >
                            <Trash2 className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </>
        ) : (
          <div className="text-center py-8">
            <Key className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">暂无权限数据</h3>
            <p className="text-gray-500">
              {search || selectedAppId ? "没有找到符合搜索条件的权限" : "系统中暂无权限"}
            </p>
          </div>
        )}
      </Card>
    </div>
  );
};

export default PermissionManagementTab;
