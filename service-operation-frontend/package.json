{"name": "service-operation-frontend", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview"}, "dependencies": {"@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-tabs": "^1.1.12", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "file-saver": "^2.0.5", "lucide-react": "^0.511.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.22.3", "tailwind-merge": "^2.6.0", "tailwind-scrollbar": "^3.1.0", "xlsx": "^0.18.5", "xlsx-js-style": "^1.2.0"}, "devDependencies": {"@types/file-saver": "^2.0.7", "@types/react": "^19.1.5", "@types/react-dom": "^19.1.5", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.16", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "tailwindcss-animate": "^1.0.7", "typescript": "^5.4.2", "vite": "^4.5.2"}, "description": "服务运营前端管理系统"}