# 权限角色体系实现文档

## 概述

本文档记录了DTTrip服务运营平台权限角色体系的完整实现，包括数据库设计、后端API、前端界面等。

## 系统架构

### 核心概念

1. **用户(User)**: 系统使用者，通过SSO单点登录
2. **角色(Role)**: 权限的集合，分为系统角色和自定义角色
3. **权限(Permission)**: 具体的操作权限，包括菜单、按钮、API、数据权限
4. **应用(Application)**: 系统应用，用户需要应用权限才能访问

### 权限模型

- **RBAC模型**: 基于角色的访问控制
- **用户-角色-权限**: 用户通过角色获得权限
- **用户-应用**: 用户需要应用权限才能访问特定应用
- **特殊权限**: 用户可以有额外的授权或拒绝权限

## 数据库设计

### 核心表结构

#### 1. 角色表 (roles)
```sql
CREATE TABLE roles (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    role_name VARCHAR(50) NOT NULL UNIQUE,
    role_code VARCHAR(50) NOT NULL UNIQUE,
    description TEXT,
    is_system TINYINT(1) DEFAULT 0,
    status TINYINT(1) DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    updated_by VARCHAR(100)
);
```

#### 2. 应用表 (applications)
```sql
CREATE TABLE applications (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    app_name VARCHAR(100) NOT NULL,
    app_code VARCHAR(50) NOT NULL UNIQUE,
    app_url VARCHAR(255),
    description TEXT,
    icon VARCHAR(100),
    sort_order INT DEFAULT 0,
    status TINYINT(1) DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    updated_by VARCHAR(100)
);
```

#### 3. 权限表 (permissions)
```sql
CREATE TABLE permissions (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    permission_name VARCHAR(100) NOT NULL,
    permission_code VARCHAR(100) NOT NULL UNIQUE,
    permission_type ENUM('menu', 'button', 'api', 'data') DEFAULT 'menu',
    parent_id BIGINT DEFAULT 0,
    app_id BIGINT,
    resource_path VARCHAR(255),
    description TEXT,
    sort_order INT DEFAULT 0,
    status TINYINT(1) DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    updated_by VARCHAR(100)
);
```

#### 4. 用户表扩展 (users)
扩展了现有用户表，添加了以下字段：
- `email`: 邮箱
- `full_name`: 姓名
- `department`: 部门
- `phone`: 手机号
- `avatar`: 头像
- `sso_user_id`: SSO用户ID
- `last_login_at`: 最后登录时间
- `login_count`: 登录次数
- `status`: 状态
- `created_by`: 创建人
- `updated_by`: 更新人

#### 5. 关联表
- `user_roles`: 用户角色关联
- `role_permissions`: 角色权限关联
- `user_app_permissions`: 用户应用权限
- `user_permissions`: 用户特殊权限

### 初始化数据

#### 默认角色
1. **超级管理员** (`super_admin`): 拥有所有权限
2. **管理员** (`admin`): 拥有管理权限和DTTrip基础权限
3. **普通用户** (`user`): 只有DTTrip应用基础查看权限

#### 默认应用
1. **DTTrip服务运营平台** (`dttrip`): 主要业务应用
2. **用户管理系统** (`user_management`): 权限管理应用

#### 权限体系
- **DTTrip应用权限**: 包括项目管理、火车票预订、酒店预订、护照识别、系统设置等
- **用户管理权限**: 包括用户管理、角色管理、权限管理、用户权限管理等

## 后端实现

### 模型定义
- `app/models/auth.py`: 定义了所有权限相关的Tortoise ORM模型和Pydantic模型

### 服务层
- `app/services/auth_service.py`: 权限认证服务，包含所有业务逻辑
  - 用户管理: 创建、更新、删除、查询用户
  - 角色管理: 创建、更新、删除、查询角色
  - 权限管理: 创建、更新、删除、查询权限
  - 权限检查: 检查用户权限、应用访问权限
  - SSO集成: 从SSO创建用户，默认分配普通用户角色

### API接口
- `app/api/auth.py`: 权限管理相关的REST API接口
  - `/auth/users`: 用户管理接口
  - `/auth/roles`: 角色管理接口
  - `/auth/permissions`: 权限管理接口
  - `/auth/applications`: 应用管理接口
  - `/auth/user-permissions`: 用户权限分配接口
  - `/auth/me/permissions`: 当前用户权限查询

## 前端实现

### 页面结构
- `src/pages/user-management/UserManagementPage.tsx`: 用户管理主页面
- `src/components/user-management/`: 用户管理相关组件
  - `UserManagementTab.tsx`: 用户管理Tab
  - `RoleManagementTab.tsx`: 角色管理Tab
  - `PermissionManagementTab.tsx`: 权限管理Tab
  - `UserPermissionManagementTab.tsx`: 用户权限管理Tab

### 功能特性
1. **Tab式界面**: 四个主要管理模块
2. **搜索过滤**: 支持关键词搜索和条件过滤
3. **分页显示**: 大数据量的分页处理
4. **权限详情**: 详细的权限信息展示
5. **操作权限**: 基于权限的操作按钮显示

### 路由配置
- 添加了 `/user-management` 路由
- 在侧边导航菜单中添加了"用户管理"入口

## 权限控制流程

### 1. 用户登录
1. 用户通过SSO登录
2. 系统检查用户是否存在，不存在则自动创建
3. 新用户默认分配"普通用户"角色和"DTTrip应用"权限

### 2. 权限检查
1. 用户访问页面时，检查应用权限
2. 执行操作时，检查具体功能权限
3. 权限计算：角色权限 + 用户特殊权限（授权/拒绝）

### 3. 权限管理
1. 管理员可以创建、编辑角色和权限
2. 管理员可以为用户分配角色和应用权限
3. 支持权限的层级结构和继承

## 安全特性

1. **最小权限原则**: 用户默认只有最基础的权限
2. **权限分离**: 应用权限和功能权限分离管理
3. **审计日志**: 记录权限变更的操作人和时间
4. **系统角色保护**: 系统角色不能被删除或修改核心属性

## 扩展性

1. **权限类型**: 支持菜单、按钮、API、数据四种权限类型
2. **层级权限**: 支持权限的父子关系
3. **应用扩展**: 可以轻松添加新的应用和权限
4. **自定义角色**: 支持创建自定义角色和权限组合

## 部署说明

### 数据库迁移
1. 运行 `poetry run python scripts/create_auth_tables.py` 创建表
2. 运行 `poetry run python scripts/init_auth_data.py` 初始化数据

### 配置要求
- MySQL 5.7+ 数据库
- 支持UTF8MB4字符集
- 需要CREATE、ALTER、INSERT、UPDATE、DELETE权限

## 使用指南

### 管理员操作
1. 访问 `/user-management` 进入用户管理界面
2. 在"用户管理"Tab中查看和管理用户
3. 在"角色管理"Tab中创建和配置角色
4. 在"权限管理"Tab中管理系统权限
5. 在"用户权限管理"Tab中为用户分配权限

### 开发者集成
1. 使用 `AuthService.check_user_permission()` 检查用户权限
2. 使用 `AuthService.check_user_app_access()` 检查应用访问权限
3. 在API接口中使用权限装饰器进行权限控制

## 总结

本权限角色体系提供了完整的用户权限管理解决方案，支持：
- ✅ 基于角色的权限控制
- ✅ 应用级别的访问控制
- ✅ 灵活的权限分配和管理
- ✅ SSO单点登录集成
- ✅ 完整的管理界面
- ✅ 可扩展的权限模型

系统已经成功部署并可以投入使用。
