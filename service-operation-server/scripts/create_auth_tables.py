#!/usr/bin/env python3
"""
创建权限认证相关表的迁移脚本
"""
import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from tortoise import Tortoise
from src.core.config import settings


async def create_auth_tables():
    """创建权限认证相关表"""
    
    # 构建数据库URL
    database_url = f"mysql://{settings.db_user}:{settings.db_password}@{settings.db_host}:{settings.db_port}/{settings.db_base}"

    # 初始化数据库连接
    await Tortoise.init(
        db_url=database_url,
        modules={'models': ['src.db.models']}
    )
    
    # 创建表的SQL语句
    create_tables_sql = """
    -- 1. 角色表 (roles)
    CREATE TABLE IF NOT EXISTS roles (
        id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '角色ID',
        role_name VARCHAR(50) NOT NULL UNIQUE COMMENT '角色名称',
        role_code VARCHAR(50) NOT NULL UNIQUE COMMENT '角色编码',
        description TEXT COMMENT '角色描述',
        is_system TINYINT(1) DEFAULT 0 COMMENT '是否系统角色(1:是, 0:否)',
        status TINYINT(1) DEFAULT 1 COMMENT '状态(1:启用, 0:禁用)',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        created_by VARCHAR(100) COMMENT '创建人',
        updated_by VARCHAR(100) COMMENT '更新人'
    ) COMMENT='角色表';

    -- 2. 应用表 (applications)
    CREATE TABLE IF NOT EXISTS applications (
        id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '应用ID',
        app_name VARCHAR(100) NOT NULL COMMENT '应用名称',
        app_code VARCHAR(50) NOT NULL UNIQUE COMMENT '应用编码',
        app_url VARCHAR(255) COMMENT '应用URL',
        description TEXT COMMENT '应用描述',
        icon VARCHAR(100) COMMENT '应用图标',
        sort_order INT DEFAULT 0 COMMENT '排序',
        status TINYINT(1) DEFAULT 1 COMMENT '状态(1:启用, 0:禁用)',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        created_by VARCHAR(100) COMMENT '创建人',
        updated_by VARCHAR(100) COMMENT '更新人'
    ) COMMENT='应用表';

    -- 3. 权限表 (permissions)
    CREATE TABLE IF NOT EXISTS permissions (
        id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '权限ID',
        permission_name VARCHAR(100) NOT NULL COMMENT '权限名称',
        permission_code VARCHAR(100) NOT NULL UNIQUE COMMENT '权限编码',
        permission_type ENUM('menu', 'button', 'api', 'data') DEFAULT 'menu' COMMENT '权限类型',
        parent_id BIGINT DEFAULT 0 COMMENT '父权限ID',
        app_id BIGINT COMMENT '所属应用ID',
        resource_path VARCHAR(255) COMMENT '资源路径',
        description TEXT COMMENT '权限描述',
        sort_order INT DEFAULT 0 COMMENT '排序',
        status TINYINT(1) DEFAULT 1 COMMENT '状态(1:启用, 0:禁用)',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        created_by VARCHAR(100) COMMENT '创建人',
        updated_by VARCHAR(100) COMMENT '更新人'
    ) COMMENT='权限表';

    -- 4. 扩展用户表字段（逐个添加，忽略已存在的字段错误）
    ALTER TABLE users ADD COLUMN email VARCHAR(255) UNIQUE COMMENT '邮箱';
    ALTER TABLE users ADD COLUMN full_name VARCHAR(100) COMMENT '姓名';
    ALTER TABLE users ADD COLUMN department VARCHAR(100) COMMENT '部门';
    ALTER TABLE users ADD COLUMN phone VARCHAR(20) COMMENT '手机号';
    ALTER TABLE users ADD COLUMN avatar VARCHAR(255) COMMENT '头像';
    ALTER TABLE users ADD COLUMN sso_user_id VARCHAR(100) COMMENT 'SSO用户ID';
    ALTER TABLE users ADD COLUMN last_login_at DATETIME COMMENT '最后登录时间';
    ALTER TABLE users ADD COLUMN login_count INT DEFAULT 0 COMMENT '登录次数';
    ALTER TABLE users ADD COLUMN status TINYINT(1) DEFAULT 1 COMMENT '状态(1:启用, 0:禁用)';
    ALTER TABLE users ADD COLUMN created_by VARCHAR(100) COMMENT '创建人';
    ALTER TABLE users ADD COLUMN updated_by VARCHAR(100) COMMENT '更新人';

    -- 5. 用户角色关联表 (user_roles)
    CREATE TABLE IF NOT EXISTS user_roles (
        id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '关联ID',
        user_id BIGINT NOT NULL COMMENT '用户ID',
        role_id BIGINT NOT NULL COMMENT '角色ID',
        assigned_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '分配时间',
        assigned_by VARCHAR(100) COMMENT '分配人',
        expires_at DATETIME COMMENT '过期时间',
        status TINYINT(1) DEFAULT 1 COMMENT '状态(1:有效, 0:无效)',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        UNIQUE KEY uk_user_role (user_id, role_id)
    ) COMMENT='用户角色关联表';

    -- 6. 角色权限关联表 (role_permissions)
    CREATE TABLE IF NOT EXISTS role_permissions (
        id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '关联ID',
        role_id BIGINT NOT NULL COMMENT '角色ID',
        permission_id BIGINT NOT NULL COMMENT '权限ID',
        granted_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '授权时间',
        granted_by VARCHAR(100) COMMENT '授权人',
        status TINYINT(1) DEFAULT 1 COMMENT '状态(1:有效, 0:无效)',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        UNIQUE KEY uk_role_permission (role_id, permission_id)
    ) COMMENT='角色权限关联表';

    -- 7. 用户应用权限表 (user_app_permissions)
    CREATE TABLE IF NOT EXISTS user_app_permissions (
        id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '权限ID',
        user_id BIGINT NOT NULL COMMENT '用户ID',
        app_id BIGINT NOT NULL COMMENT '应用ID',
        granted_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '授权时间',
        granted_by VARCHAR(100) COMMENT '授权人',
        expires_at DATETIME COMMENT '过期时间',
        status TINYINT(1) DEFAULT 1 COMMENT '状态(1:有效, 0:无效)',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        UNIQUE KEY uk_user_app (user_id, app_id)
    ) COMMENT='用户应用权限表';

    -- 8. 用户权限表 (user_permissions) - 用户特殊权限
    CREATE TABLE IF NOT EXISTS user_permissions (
        id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '权限ID',
        user_id BIGINT NOT NULL COMMENT '用户ID',
        permission_id BIGINT NOT NULL COMMENT '权限ID',
        permission_type ENUM('grant', 'deny') DEFAULT 'grant' COMMENT '权限类型(grant:授权, deny:拒绝)',
        granted_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '授权时间',
        granted_by VARCHAR(100) COMMENT '授权人',
        expires_at DATETIME COMMENT '过期时间',
        status TINYINT(1) DEFAULT 1 COMMENT '状态(1:有效, 0:无效)',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        UNIQUE KEY uk_user_permission (user_id, permission_id)
    ) COMMENT='用户权限表';

    -- 创建索引（忽略错误，如果索引已存在）
    """
    
    try:
        # 获取数据库连接
        connection = Tortoise.get_connection("default")
        
        # 执行SQL语句
        print("开始创建权限认证相关表...")
        
        # 分割SQL语句并逐个执行
        sql_statements = [stmt.strip() for stmt in create_tables_sql.split(';') if stmt.strip()]
        
        for i, sql in enumerate(sql_statements, 1):
            try:
                await connection.execute_query(sql)
                print(f"✓ 执行SQL语句 {i}/{len(sql_statements)}")
            except Exception as e:
                print(f"✗ 执行SQL语句 {i} 失败: {e}")
                # 继续执行其他语句
                continue
        
        print("权限认证相关表创建完成！")
        
    except Exception as e:
        print(f"创建表时发生错误: {e}")
        raise
    finally:
        await Tortoise.close_connections()


if __name__ == "__main__":
    asyncio.run(create_auth_tables())
