#!/usr/bin/env python3
"""
临时脚本：创建测试用户
"""

import asyncio
from tortoise import Tortoise
from src.db.models.user import User
from src.core.config import settings

async def create_test_user():
    await Tortoise.init(
        db_url=settings.db_url,
        modules={'models': ['src.db.models']}
    )
    
    # 检查用户是否存在
    user = await User.filter(user_id='123497').first()
    if not user:
        # 创建测试用户
        user = await User.create(
            user_id='123497',
            username='测试用户',
            email='<EMAIL>'
        )
        print(f'已创建测试用户: {user.username} (ID: {user.user_id})')
    else:
        print(f'用户已存在: {user.username} (ID: {user.user_id})')
    
    await Tortoise.close_connections()

if __name__ == "__main__":
    asyncio.run(create_test_user()) 