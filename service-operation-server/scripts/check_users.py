import asyncio
from src.db.models.user import User
from tortoise import Tortoise
from src.core.config import settings

async def check_users():
    await Tortoise.init(
        db_url=settings.db_url,
        modules={'models': ['src.db.models']}
    )
    users = await User.all()
    print(f'数据库中的用户数量: {len(users)}')
    for user in users:
        print(f'用户ID: {user.user_id}, 用户名: {user.username}')
    await Tortoise.close_connections()

if __name__ == "__main__":
    asyncio.run(check_users()) 