import asyncio
from src.db.models.passport import Passport
from src.db.models.user import User
from tortoise import Tortoise
from src.core.config import settings
from tortoise.functions import Count, Max

async def debug_tasks():
    await Tortoise.init(
        db_url=settings.db_url,
        modules={'models': ['src.db.models']}
    )
    
    # 获取用户
    user = await User.filter(user_id='123497').first()
    print(f'用户: {user.username if user else "未找到"}')
    
    # 获取该用户的护照记录
    passports = await Passport.filter(user_id='123497').all()
    print(f'护照记录数量: {len(passports)}')
    
    if passports:
        for passport in passports[:3]:  # 只显示前3个
            print(f'护照ID: {passport.id}, 任务ID: {passport.task_id}, 状态: {passport.processing_status}')
    
    # 测试聚合查询
    try:
        task_query = Passport.filter(user_id='123497')\
            .group_by('task_id')\
            .values(
                'task_id',
                total_files=Count('id'),
                latest_created=Max('created_at'),
                latest_updated=Max('updated_at')
            )
        
        tasks_data = await task_query
        print(f'任务数据: {tasks_data}')
        
    except Exception as e:
        print(f'聚合查询错误: {e}')
    
    await Tortoise.close_connections()

if __name__ == "__main__":
    asyncio.run(debug_tasks()) 