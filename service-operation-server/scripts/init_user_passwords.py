#!/usr/bin/env python3
"""
为现有用户初始化默认密码的脚本
"""
import asyncio
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.core.config import settings
from tortoise import Tortoise
from src.api.user_management.password_service import password_service

async def init_user_passwords():
    """为现有用户初始化默认密码"""
    
    print("开始为现有用户初始化默认密码...")
    
    # 初始化Tortoise ORM
    await Tortoise.init(
        db_url=settings.db_url,
        modules={'models': ['src.db.models']}
    )
    
    # 获取数据库连接
    connection = Tortoise.get_connection("default")
    
    try:
        # 查询没有密码的用户
        users_sql = """
        SELECT user_id, username, member_id 
        FROM users 
        WHERE (password IS NULL OR password = '') AND status = 1
        """
        
        result = await connection.execute_query(users_sql)
        if isinstance(result, tuple) and len(result) > 1:
            users_data = result[1]
        else:
            users_data = result if isinstance(result, list) else []
        
        if not users_data:
            print("所有用户都已设置密码，无需初始化")
            return
        
        print(f"找到 {len(users_data)} 个用户需要初始化密码")
        
        # 默认密码
        default_password = "123456Aa"  # 符合密码强度要求的默认密码
        
        # 为每个用户设置默认密码
        for user in users_data:
            user_id = user['user_id']
            username = user['username']
            member_id = user['member_id']
            
            # 加密密码
            encrypted_password, salt = password_service.hash_password(default_password)
            
            # 更新用户密码
            update_sql = """
            UPDATE users 
            SET password = %s, password_salt = %s, updated_at = NOW()
            WHERE user_id = %s
            """
            
            await connection.execute_query(update_sql, [encrypted_password, salt, user_id])
            
            print(f"✓ 用户 {username} ({member_id}) 密码初始化完成")
        
        print(f"所有用户密码初始化完成！默认密码: {default_password}")
        print("建议用户首次登录后立即修改密码")
        
    except Exception as e:
        print(f"初始化用户密码失败: {e}")
        raise
    finally:
        # 关闭Tortoise连接
        await Tortoise.close_connections()

if __name__ == "__main__":
    asyncio.run(init_user_passwords())
