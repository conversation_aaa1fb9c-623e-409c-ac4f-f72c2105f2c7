-- 权限角色体系数据库设计
-- 创建时间: 2024-12-28

-- 1. 角色表 (roles)
CREATE TABLE roles (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '角色ID',
    role_name VARCHAR(50) NOT NULL UNIQUE COMMENT '角色名称',
    role_code VARCHAR(50) NOT NULL UNIQUE COMMENT '角色编码',
    description TEXT COMMENT '角色描述',
    is_system TINYINT(1) DEFAULT 0 COMMENT '是否系统角色(1:是, 0:否)',
    status TINYINT(1) DEFAULT 1 COMMENT '状态(1:启用, 0:禁用)',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by <PERSON><PERSON><PERSON><PERSON>(100) COMMENT '创建人',
    updated_by VARCHA<PERSON>(100) COMMENT '更新人'
) COMMENT='角色表';

-- 2. 应用表 (applications)
CREATE TABLE applications (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '应用ID',
    app_name VARCHAR(100) NOT NULL COMMENT '应用名称',
    app_code VARCHAR(50) NOT NULL UNIQUE COMMENT '应用编码',
    app_url VARCHAR(255) COMMENT '应用URL',
    description TEXT COMMENT '应用描述',
    icon VARCHAR(100) COMMENT '应用图标',
    sort_order INT DEFAULT 0 COMMENT '排序',
    status TINYINT(1) DEFAULT 1 COMMENT '状态(1:启用, 0:禁用)',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by VARCHAR(100) COMMENT '创建人',
    updated_by VARCHAR(100) COMMENT '更新人'
) COMMENT='应用表';

-- 3. 权限表 (permissions)
CREATE TABLE permissions (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '权限ID',
    permission_name VARCHAR(100) NOT NULL COMMENT '权限名称',
    permission_code VARCHAR(100) NOT NULL UNIQUE COMMENT '权限编码',
    permission_type ENUM('menu', 'button', 'api', 'data') DEFAULT 'menu' COMMENT '权限类型',
    parent_id BIGINT DEFAULT 0 COMMENT '父权限ID',
    app_id BIGINT COMMENT '所属应用ID',
    resource_path VARCHAR(255) COMMENT '资源路径',
    description TEXT COMMENT '权限描述',
    sort_order INT DEFAULT 0 COMMENT '排序',
    status TINYINT(1) DEFAULT 1 COMMENT '状态(1:启用, 0:禁用)',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by VARCHAR(100) COMMENT '创建人',
    updated_by VARCHAR(100) COMMENT '更新人',
    FOREIGN KEY (app_id) REFERENCES applications(id) ON DELETE CASCADE
) COMMENT='权限表';

-- 4. 用户表 (users) - 扩展现有用户表
CREATE TABLE users (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '用户ID',
    username VARCHAR(100) NOT NULL UNIQUE COMMENT '用户名',
    email VARCHAR(255) UNIQUE COMMENT '邮箱',
    full_name VARCHAR(100) COMMENT '姓名',
    department VARCHAR(100) COMMENT '部门',
    phone VARCHAR(20) COMMENT '手机号',
    avatar VARCHAR(255) COMMENT '头像',
    sso_user_id VARCHAR(100) COMMENT 'SSO用户ID',
    last_login_at DATETIME COMMENT '最后登录时间',
    login_count INT DEFAULT 0 COMMENT '登录次数',
    status TINYINT(1) DEFAULT 1 COMMENT '状态(1:启用, 0:禁用)',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by VARCHAR(100) COMMENT '创建人',
    updated_by VARCHAR(100) COMMENT '更新人'
) COMMENT='用户表';

-- 5. 用户角色关联表 (user_roles)
CREATE TABLE user_roles (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '关联ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    role_id BIGINT NOT NULL COMMENT '角色ID',
    assigned_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '分配时间',
    assigned_by VARCHAR(100) COMMENT '分配人',
    expires_at DATETIME COMMENT '过期时间',
    status TINYINT(1) DEFAULT 1 COMMENT '状态(1:有效, 0:无效)',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
    UNIQUE KEY uk_user_role (user_id, role_id)
) COMMENT='用户角色关联表';

-- 6. 角色权限关联表 (role_permissions)
CREATE TABLE role_permissions (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '关联ID',
    role_id BIGINT NOT NULL COMMENT '角色ID',
    permission_id BIGINT NOT NULL COMMENT '权限ID',
    granted_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '授权时间',
    granted_by VARCHAR(100) COMMENT '授权人',
    status TINYINT(1) DEFAULT 1 COMMENT '状态(1:有效, 0:无效)',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE,
    UNIQUE KEY uk_role_permission (role_id, permission_id)
) COMMENT='角色权限关联表';

-- 7. 用户应用权限表 (user_app_permissions)
CREATE TABLE user_app_permissions (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '权限ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    app_id BIGINT NOT NULL COMMENT '应用ID',
    granted_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '授权时间',
    granted_by VARCHAR(100) COMMENT '授权人',
    expires_at DATETIME COMMENT '过期时间',
    status TINYINT(1) DEFAULT 1 COMMENT '状态(1:有效, 0:无效)',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (app_id) REFERENCES applications(id) ON DELETE CASCADE,
    UNIQUE KEY uk_user_app (user_id, app_id)
) COMMENT='用户应用权限表';

-- 8. 用户权限表 (user_permissions) - 用户特殊权限
CREATE TABLE user_permissions (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '权限ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    permission_id BIGINT NOT NULL COMMENT '权限ID',
    permission_type ENUM('grant', 'deny') DEFAULT 'grant' COMMENT '权限类型(grant:授权, deny:拒绝)',
    granted_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '授权时间',
    granted_by VARCHAR(100) COMMENT '授权人',
    expires_at DATETIME COMMENT '过期时间',
    status TINYINT(1) DEFAULT 1 COMMENT '状态(1:有效, 0:无效)',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE,
    UNIQUE KEY uk_user_permission (user_id, permission_id)
) COMMENT='用户权限表';

-- 创建索引
CREATE INDEX idx_roles_code ON roles(role_code);
CREATE INDEX idx_roles_status ON roles(status);
CREATE INDEX idx_applications_code ON applications(app_code);
CREATE INDEX idx_applications_status ON applications(status);
CREATE INDEX idx_permissions_code ON permissions(permission_code);
CREATE INDEX idx_permissions_type ON permissions(permission_type);
CREATE INDEX idx_permissions_app ON permissions(app_id);
CREATE INDEX idx_permissions_parent ON permissions(parent_id);
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_sso ON users(sso_user_id);
CREATE INDEX idx_users_status ON users(status);
CREATE INDEX idx_user_roles_user ON user_roles(user_id);
CREATE INDEX idx_user_roles_role ON user_roles(role_id);
CREATE INDEX idx_role_permissions_role ON role_permissions(role_id);
CREATE INDEX idx_role_permissions_permission ON role_permissions(permission_id);
CREATE INDEX idx_user_app_permissions_user ON user_app_permissions(user_id);
CREATE INDEX idx_user_app_permissions_app ON user_app_permissions(app_id);
CREATE INDEX idx_user_permissions_user ON user_permissions(user_id);
CREATE INDEX idx_user_permissions_permission ON user_permissions(permission_id);
