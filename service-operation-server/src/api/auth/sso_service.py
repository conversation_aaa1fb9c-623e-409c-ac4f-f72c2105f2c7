"""
SSO认证服务模块。

此模块处理与同程统一授权登录中心的交互，包括生成登录URL、验证票据和登出URL。
"""

import urllib.parse
import httpx
from fastapi import status
from loguru import logger
from datetime import datetime, timedelta

from src.core.config import settings
from src.core.security import create_access_token
from src.db.models.user import User
from src.api.auth.exceptions import AuthenticationError

# 简单的内存缓存，存储已处理的授权码
_processed_codes = {}


class SSOAuthenticationError(AuthenticationError):
    """SSO认证错误异常"""
    def __init__(self, message: str = "SSO认证失败", headers: dict = None):
        super().__init__(
            message=message,

            headers=headers
        )


async def get_sso_login_url() -> str:
    """
    获取SSO登录URL。
    
    Returns:
        str: SSO登录URL
    
    Raises:
        SSOAuthenticationError: 如果获取SSO登录URL失败
    """
    try:
        # 强制使用正确的同程 SSO 域名，而不是从配置中获取
        sso_domain = settings.sso_domain
        client_id = settings.sso_client_id
        # 使用端口 5174，因为 Vite 现在使用这个端口
        redirect_uri = settings.sso_redirect_uri
        response_type = "code"  # 固定为 code
        scope = "read"  # 固定为 read
        state = "random_state"  # 实际应用中应该生成随机字符串并存储在会话中
        
        # 构建授权URL (符合同程SSO规范)
        login_url = f"{sso_domain}/oauth/authorize?response_type={response_type}&scope={scope}&client_id={client_id}&redirect_uri={urllib.parse.quote(redirect_uri)}&state={state}"
        
        logger.info(f"生成SSO登录URL: {login_url}")
        return login_url
    except Exception as e:
        logger.exception(f"获取SSO登录URL时发生错误: {str(e)}")
        raise SSOAuthenticationError(message="获取SSO登录URL失败")


async def exchange_code_for_token(code: str, state: str):
    """
    使用授权码获取访问令牌和用户信息。
    
    Args:
        code: 授权码
        state: 状态值
    
    Returns:
        tuple: (访问令牌, 用户信息, 应用访问令牌)
    
    Raises:
        SSOAuthenticationError: 如果获取访问令牌失败
    """
    global _processed_codes  # 声明使用全局变量
    
    try:
        # 防重复处理：检查授权码是否已经被处理过
        current_time = datetime.now()
        if code in _processed_codes:
            last_processed_time = _processed_codes[code]
            # 如果在5分钟内已经处理过，直接抛出错误
            if current_time - last_processed_time < timedelta(minutes=5):
                logger.warning(f"授权码 {code[:8]}... 已在 {last_processed_time} 被处理过，拒绝重复处理")
                raise SSOAuthenticationError(message="授权码已被使用，请重新获取")
        
        # 记录授权码处理时间
        _processed_codes[code] = current_time
        
        # 清理超过1小时的旧记录，防止内存泄漏
        cutoff_time = current_time - timedelta(hours=1)
        _processed_codes = {k: v for k, v in _processed_codes.items() if v > cutoff_time}
        
        # 使用授权码获取访问令牌
        # 从配置文件获取SSO相关配置
        sso_domain = settings.sso_domain
        client_id = settings.sso_client_id
        client_secret = settings.sso_client_secret
        redirect_uri = settings.sso_redirect_uri
        grant_type = "authorization_code"  # 固定为 authorization_code
        
        async with httpx.AsyncClient() as client:
            # 记录请求参数（隐藏敏感信息）
            logger.info(f"请求SSO令牌接口 - URL: {sso_domain}/oauth/token")
            logger.info(f"请求参数 - client_id: {client_id}, redirect_uri: {redirect_uri}, grant_type: {grant_type}")
            logger.info(f"授权码: {code[:8]}...")  # 只显示前8位
            
            # 调用同程SSO的令牌接口
            token_response = await client.post(
                f"{sso_domain}/oauth/token",
                headers={
                    "Content-Type": "application/x-www-form-urlencoded",
                    "Accept": "application/json"
                },
                data={
                    "client_id": client_id,
                    "client_secret": client_secret,
                    "redirect_uri": redirect_uri,
                    "code": code,
                    "grant_type": grant_type
                },
                timeout=10.0
            )
            
            logger.info(f"SSO令牌接口响应状态码: {token_response.status_code}")
            logger.info(f"SSO令牌接口响应内容: {token_response.text}")
            
            if token_response.status_code != 200:
                logger.error(f"获取访问令牌失败: {token_response.status_code} - {token_response.text}")
                raise SSOAuthenticationError(message="获取访问令牌失败")
            
            token_data = token_response.json()
            access_token = token_data.get("access_token")
            
            if not access_token:
                raise SSOAuthenticationError(message="获取访问令牌失败: 响应中没有访问令牌")
            
            # 使用访问令牌获取用户信息
            user_info_response = await client.post(
                f"{sso_domain}/oauth/rs/getuserinfo",
                headers={
                    "Content-Type": "application/x-www-form-urlencoded"
                },
                data={
                    "access_token": access_token
                },
                timeout=10.0
            )
            
            if user_info_response.status_code != 200:
                logger.error(f"获取用户信息失败: {user_info_response.status_code} - {user_info_response.text}")
                raise SSOAuthenticationError(message="获取用户信息失败")
            
            user_data = user_info_response.json()
        
        # 在本地数据库中查找或创建用户
        user_dict = await get_or_create_user_from_sso(user_data)
        
        # 创建应用的JWT访问令牌
        app_access_token = create_access_token(subject=str(user_dict["userId"]))
        
        return access_token, user_dict, app_access_token
    except httpx.RequestError as e:
        logger.exception(f"SSO服务请求失败: {str(e)}")
        raise SSOAuthenticationError(message="SSO服务连接失败")
    except Exception as e:
        logger.exception(f"处理授权码时发生错误: {str(e)}")
        raise SSOAuthenticationError(message=f"处理授权码失败: {str(e)}")


async def get_or_create_user_from_sso(user_data: dict) -> dict:
    """
    根据SSO用户数据获取或创建用户记录。
    
    Args:
        user_data: SSO返回的用户数据
    
    Returns:
        dict: 用户信息字典
    """
    try:
        # 记录完整的用户数据，便于调试
        logger.info(f"SSO返回的用户数据: {user_data}")
        
        # 尝试通过用户ID查找用户
        user_id = user_data.get("userId")
        if not user_id:
            logger.error(f"SSO返回的用户数据中没有userId字段: {user_data}")
            raise SSOAuthenticationError(message="SSO返回的用户数据无效")
            
        user = await User.filter(user_id=user_id).first()
        
        if not user:
            # 创建新用户
            logger.info(f"创建新用户: {user_id}")
            user = User(
                username=user_data.get("username", "SSO User"),
                department=user_data.get("department"),
                user_id=user_id,
                work_id=user_data.get("workId") or user_data.get("work_id"),
                new_work_id=user_data.get("newWorkId"),
                department_id=user_data.get("departmentId"),
                gender=user_data.get("gender"),
                email=user_data.get("email"),
                dept_level_id=user_data.get("deptLevelId"),
                dept_level_name=user_data.get("deptLevelName"),
                phone_number=user_data.get("phoneNumber"),
                mtid=user_data.get("MTID"),
                ctids=user_data.get("CTIDS"),
                gid=user_data.get("GID"),
                mobile=user_data.get("mobile"),
                member_id=user_data.get("memberId"),
                is_virtual=user_data.get("isVirtual"),
                tid=user_data.get("TID"),
                device_id=user_data.get("deviceId")
            )
            await user.save()
            logger.info(f"从SSO创建新用户成功: {user_id}")
        else:
            # 更新现有用户信息
            logger.info(f"更新现有用户: {user_id}")
            user.username = user_data.get("username", user.username)
            user.department = user_data.get("department", user.department)
            user.work_id = user_data.get("workId", user.work_id) or user_data.get("work_id", user.work_id)
            user.new_work_id = user_data.get("newWorkId", user.new_work_id)
            user.department_id = user_data.get("departmentId", user.department_id)
            user.gender = user_data.get("gender", user.gender)
            user.email = user_data.get("email", user.email)
            user.dept_level_id = user_data.get("deptLevelId", user.dept_level_id)
            user.dept_level_name = user_data.get("deptLevelName", user.dept_level_name)
            user.phone_number = user_data.get("phoneNumber", user.phone_number)
            user.mtid = user_data.get("MTID", user.mtid)
            user.ctids = user_data.get("CTIDS", user.ctids)
            user.gid = user_data.get("GID", user.gid)
            user.mobile = user_data.get("mobile", user.mobile)
            user.member_id = user_data.get("memberId", user.member_id)
            user.is_virtual = user_data.get("isVirtual", user.is_virtual)
            user.tid = user_data.get("TID", user.tid)
            user.device_id = user_data.get("deviceId", user.device_id)
            await user.save()
            logger.info(f"更新现有用户成功: {user_id}")
        
        # 返回用户信息时，确保包含work_id和department
        user_dict = {
            "userId": user.user_id,
            "username": user.username,
            "department": user.department,
            "work_id": user.work_id,
            "email": user.email or "",
            "role": "user"
        }
        logger.info(f"返回的用户信息: {user_dict}")
        return user_dict
    except Exception as e:
        logger.exception(f"处理SSO用户数据时发生错误: {str(e)}")
        raise SSOAuthenticationError(message=f"处理SSO用户数据失败: {str(e)}")


async def get_sso_logout_url(access_token: str) -> str:
    """
    获取SSO登出URL。
    根据同程SSO官方文档：建议使用POST方式的/oauth/logoutapi接口
    
    Args:
        access_token: SSO访问令牌
    
    Returns:
        str: SSO登出URL
    
    Raises:
        SSOAuthenticationError: 如果获取SSO登出URL失败
    """
    try:
        # 构建SSO登出URL
        sso_domain = settings.sso_domain
        
        # 根据官方文档，建议使用POST方式的logoutapi接口
        logout_url = f"{sso_domain}/oauth/logoutapi?access_token={access_token}"
        
        logger.info(f"生成SSO登出URL: {logout_url}")
        return logout_url
    except Exception as e:
        logger.exception(f"获取SSO登出URL时发生错误: {str(e)}")
        raise SSOAuthenticationError(message="获取SSO登出URL失败")


async def sso_logout(access_token: str) -> bool:
    """
    执行SSO登出操作。
    根据同程SSO官方文档：
    - 推荐使用POST方式的 /oauth/logoutapi 接口
    - 支持线下、预发、正式环境
    - 返回格式：{"success_code": "1"} 正常，{"success_code": "0"} 异常
    
    Args:
        access_token: SSO访问令牌
    
    Returns:
        bool: 登出是否成功
    
    Raises:
        SSOAuthenticationError: 如果SSO登出失败
    """
    try:
        sso_domain = settings.sso_domain
        
        logger.info(f"开始执行SSO登出，使用官方logoutapi接口...")
        logger.info(f"SSO域名: {sso_domain}")
        logger.info(f"Token: {access_token[:20]}...")
        
        async with httpx.AsyncClient(timeout=15.0) as client:
            
            # === 方式1: 官方推荐的POST方式 /oauth/logoutapi ===
            logger.info("🔥 使用官方推荐的POST方式登出...")
            try:
                # 根据官方文档，使用POST方式调用logoutapi
                logout_response = await client.post(
                    f"{sso_domain}/oauth/logoutapi?access_token={access_token}",
                    headers={
                        "Content-Type": "application/x-www-form-urlencoded",
                        "Accept": "application/json",
                        "User-Agent": "DTTrip-SSO-Client/1.0"
                    },
                    data={}  # POST方式，参数在URL中
                )
                
                logger.info(f"logoutapi响应状态: {logout_response.status_code}")
                
                if logout_response.status_code == 200:
                    try:
                        response_data = logout_response.json()
                        logger.info(f"logoutapi响应数据: {response_data}")
                        
                        # 根据官方文档检查success_code
                        success_code = response_data.get("success_code")
                        if success_code == "1":
                            logger.info("✅ 官方logoutapi登出成功")
                            return True
                        elif success_code == "0":
                            logger.warning("⚠️ 官方logoutapi登出异常，但继续执行")
                        else:
                            logger.warning(f"⚠️ 官方logoutapi返回未知success_code: {success_code}")
                            
                    except Exception as json_error:
                        logger.warning(f"解析logoutapi响应JSON失败: {json_error}")
                        logger.warning(f"原始响应: {logout_response.text}")
                
                # 即使success_code为0或解析失败，也尝试备用方式
                        
            except Exception as logoutapi_error:
                logger.warning(f"logoutapi调用失败: {logoutapi_error}")
            
            # === 方式2: 备用的GET方式 /oauth/logout ===
            logger.info("📤 尝试备用GET方式登出...")
            try:
                # 官方文档提到的GET方式（退出到统一登录页面）
                get_logout_response = await client.get(
                    f"{sso_domain}/oauth/logout?access_token={access_token}",
                    headers={
                        "Accept": "text/html,application/json",
                        "User-Agent": "DTTrip-SSO-Client/1.0"
                    }
                )
                
                logger.info(f"GET logout响应状态: {get_logout_response.status_code}")
                
                if get_logout_response.status_code in [200, 302]:
                    logger.info("✅ GET方式登出成功")
                    return True
                    
            except Exception as get_error:
                logger.warning(f"GET方式登出失败: {get_error}")
            
            # === 方式3: 备用POST方式 /oauth/logout ===
            logger.info("📥 尝试备用POST方式登出...")
            try:
                post_logout_response = await client.post(
                    f"{sso_domain}/oauth/logout",
                    headers={
                        "Content-Type": "application/x-www-form-urlencoded",
                        "Accept": "application/json"
                    },
                    data={
                        "access_token": access_token
                    }
                )
                
                logger.info(f"POST logout响应状态: {post_logout_response.status_code}")
                
                if post_logout_response.status_code in [200, 302]:
                    logger.info("✅ 备用POST方式登出成功")
                    return True
                    
            except Exception as post_error:
                logger.warning(f"备用POST方式登出失败: {post_error}")
            
            # 即使所有方式都失败，也返回True，因为本地清理更重要
            logger.warning("⚠️ 所有SSO登出方式都失败，但本地清理将继续")
            return True
                
    except Exception as e:
        logger.exception(f"SSO登出时发生错误: {str(e)}")
        # 任何错误都返回True，确保本地清理能够执行
        return True
