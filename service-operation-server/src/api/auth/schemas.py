"""
认证模块的数据模型定义。
"""
from typing import Optional
from pydantic import Field

from src.core.base_models import CustomBaseModel, DataResponse


class TokenRequest(CustomBaseModel):
    """API密钥认证请求模型。"""
    api_key: str = Field(..., description="API密钥")
    api_secret: str = Field(..., description="API密钥秘钥")
    
    
class Token(CustomBaseModel):
    """JWT令牌响应模型。"""
    access_token: str = Field(..., description="访问令牌")
    token_type: str = Field("bearer", description="令牌类型")


class TokenResponse(DataResponse[Token]):
    """令牌响应包装模型。"""
    message: str = Field("认证成功", description="响应消息")
    

class TokenPayload(CustomBaseModel):
    """JWT令牌载荷模型。"""
    sub: Optional[str] = Field(None, description="令牌主题（通常是开发者ID）")
    exp: Optional[int] = Field(None, description="过期时间戳")
