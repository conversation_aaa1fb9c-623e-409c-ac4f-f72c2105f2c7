from fastapi import APIRouter, status
from loguru import logger

from src.api.auth.schemas import Token, TokenRequest, TokenResponse
from src.api.auth.constants import TOKEN_TYPE, CREDENTIALS_EXCEPTION_HEADERS
from src.api.auth.service import authenticate_api_key
from src.api.auth.exceptions import InvalidApiKeyError, InvalidApiSecretError, DeveloperNotFoundError
from src.core.exceptions import APIError
from src.api.auth.sso_endpoints import router as sso_router

router = APIRouter()

# 包含 SSO 路由器
router.include_router(sso_router, tags=["SSO认证"])


@router.post(
    "/token", 
    response_model=TokenResponse,
    status_code=status.HTTP_200_OK,
    summary="获取访问令牌",
    description="使用API密钥和秘钥进行认证，返回JWT访问令牌",
    responses={
        status.HTTP_401_UNAUTHORIZED: {
            "description": "认证失败，API密钥或秘钥无效",
            "headers": CREDENTIALS_EXCEPTION_HEADERS,
        },
        status.HTTP_500_INTERNAL_SERVER_ERROR: {
            "description": "服务器内部错误，可能是开发者关联信息丢失"
        }
    }
)
async def login_for_access_token(form_data: TokenRequest) -> TokenResponse:
    """
    使用API密钥和秘钥进行认证，返回JWT访问令牌。
    
    参数:
        form_data: 包含API密钥和秘钥的请求数据
        
    返回:
        TokenResponse: 包含JWT访问令牌的响应
        
    异常:
        InvalidApiKeyError: 如果API密钥无效或不存在
        InvalidApiSecretError: 如果API密钥秘钥无效
        DeveloperNotFoundError: 如果找不到关联的开发者信息
        APIError: 如果发生其他未预期的错误
    """
    try:
        # 调用服务层进行认证，获取访问令牌
        access_token = await authenticate_api_key(
            api_key=form_data.api_key,
            api_secret=form_data.api_secret
        )
        
        # 返回响应
        return TokenResponse(
            data=Token(
                access_token=access_token,
                token_type=TOKEN_TYPE
            )
        )
        
    except (InvalidApiKeyError, InvalidApiSecretError, DeveloperNotFoundError):
        # 已定义的特定异常直接重新抛出
        raise
    except Exception as e:
        # 其他未预期的异常记录并包装为APIError
        logger.exception(f"处理认证请求时发生错误: {str(e)}")
        raise APIError(
            message="处理认证请求时发生错误",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            details=str(e) if str(e) else None
        )