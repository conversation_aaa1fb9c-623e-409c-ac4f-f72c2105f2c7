"""
认证模块的业务逻辑服务。

此模块集中处理与认证相关的业务逻辑，将其与路由处理分离，提高代码可维护性和可测试性。
"""

from loguru import logger
from fastapi import status

from src.api.auth.exceptions import InvalidApiKeyError, InvalidApiSecretError, DeveloperNotFoundError
from src.api.auth.constants import CREDENTIALS_EXCEPTION_HEADERS
from src.core.security import create_access_token, verify_password
from src.services.api_key_service import get_api_key_by_key, update_api_key_last_used
from src.db.models.api_key import ApiKey


async def authenticate_api_key(api_key: str, api_secret: str) -> str:
    """
    验证API密钥和秘钥，成功后返回JWT令牌。
    
    Args:
        api_key: API密钥
        api_secret: API密钥秘钥
        
    Returns:
        str: JWT访问令牌
        
    Raises:
        InvalidApiKeyError: 如果API密钥无效或不存在
        InvalidApiSecretError: 如果API密钥秘钥无效
        DeveloperNotFoundError: 如果找不到关联的开发者信息
    """
    # 获取API密钥记录
    api_key_record: ApiKey | None = await get_api_key_by_key(api_key_value=api_key)
    
    # 验证API密钥是否存在且有效
    if not api_key_record:
        logger.warning(f"尝试使用无效的API密钥: {api_key}")
        raise InvalidApiKeyError(
            message="无效的API密钥或秘钥",
            headers=CREDENTIALS_EXCEPTION_HEADERS
        )

    # 验证秘钥
    is_password_valid = verify_password(api_secret, api_key_record.secret)
    if not is_password_valid:
        logger.warning(f"API密钥 {api_key} 的秘钥验证失败")
        raise InvalidApiSecretError(
            message="无效的API密钥或秘钥",
            headers=CREDENTIALS_EXCEPTION_HEADERS
        )

    # 检查是否有关联的开发者
    if not api_key_record.developer_id:
        # 这种情况理论上不应该发生，如果数据完整性得到维护
        logger.error(f"API密钥 {api_key} 没有关联的开发者ID")
        raise DeveloperNotFoundError()

    # 创建主题（开发者ID）
    subject = str(api_key_record.developer_id)
    
    # 更新API密钥的最后使用时间
    await update_api_key_last_used(api_key_record)
    
    # 创建JWT访问令牌
    access_token = create_access_token(subject=subject)
    
    # 记录成功登录
    logger.info(f"开发者 {api_key_record.developer_id} 使用API密钥 {api_key} 成功认证")
    
    return access_token
