"""
SSO认证相关的API端点。

此模块提供与SSO认证相关的API端点，包括获取SSO登录URL、验证SSO票据和获取SSO登出URL。
"""

from fastapi import APIRouter, status, Depends
from loguru import logger

from src.api.auth.sso_schemas import SSOUrlResponse, SSOTicketRequest, SSOTokenResponse, SSOUserInfo
from src.api.auth.schemas import Token
from src.api.auth.sso_service import get_sso_login_url, exchange_code_for_token, get_sso_logout_url, sso_logout
from src.api.auth.constants import TOKEN_TYPE
from src.core.exceptions import APIError
from src.db.models.user import User

router = APIRouter(prefix="/sso")


@router.get(
    "/login-url",
    response_model=SSOUrlResponse,
    status_code=status.HTTP_200_OK,
    summary="获取SSO登录URL",
    description="获取同程统一授权登录中心的登录URL"
)
async def get_login_url() -> SSOUrlResponse:
    """
    获取SSO登录URL。
    
    Returns:
        SSOUrlResponse: 包含SSO登录URL的响应
    """
    try:
        login_url = await get_sso_login_url()
        return SSOUrlResponse(data={"login_url": login_url})
    except Exception as e:
        logger.exception(f"获取SSO登录URL时发生错误: {str(e)}")
        raise APIError(
            message="获取SSO登录URL失败",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            details=str(e) if str(e) else None
        )


@router.get(
    "/callback",
    response_model=SSOTokenResponse,
    status_code=status.HTTP_200_OK,
    summary="处理SSO回调(GET)",
    description="处理同程统一授权登录中心的授权回调(GET重定向)"
)
async def handle_callback_get(code: str, state: str) -> SSOTokenResponse:
    """
    处理SSO授权回调(GET方法)。
    
    Args:
        code: 授权码
        state: 状态值
    
    Returns:
        SSOTokenResponse: 包含访问令牌和用户信息的响应
    """
    return await _handle_callback_logic(code, state)


@router.post(
    "/callback",
    response_model=SSOTokenResponse,
    status_code=status.HTTP_200_OK,
    summary="处理SSO回调(POST)",
    description="处理同程统一授权登录中心的授权回调(POST请求)"
)
async def handle_callback_post(code: str, state: str) -> SSOTokenResponse:
    """
    处理SSO授权回调(POST方法)。
    
    Args:
        code: 授权码
        state: 状态值
    
    Returns:
        SSOTokenResponse: 包含访问令牌和用户信息的响应
    """
    return await _handle_callback_logic(code, state)


async def _handle_callback_logic(code: str, state: str) -> SSOTokenResponse:
    """
    处理SSO授权回调。
    
    Args:
        code: 授权码
        state: 状态值
    
    Returns:
        SSOTokenResponse: 包含访问令牌和用户信息的响应
    """
    # 记录请求参数
    logger.info(f"开始处理SSO回调 - code: {code[:20]}..., state: {state}")
    
    try:
        access_token, user_info, app_token = await exchange_code_for_token(code, state)
        
        # 打印用户信息详情
        logger.info(f"SSO 返回的用户信息类型: {type(user_info)}")
        logger.info(f"SSO 返回的用户信息字段: {list(user_info.keys()) if isinstance(user_info, dict) else 'Not a dict'}")
        logger.info(f"SSO 返回的用户信息内容: {user_info}")
        
        # 从users表中获取用户信息
        user_id = user_info.get("userId", "")
        db_user = await User.filter(user_id=user_id).first()
        
        if db_user:
            # 使用数据库中的用户信息
            user = SSOUserInfo(
                id=user_id,
                name=db_user.username,
                email=db_user.email or "",
                role="user",
                department=db_user.department,
                work_id=db_user.work_id
            )
        else:
            # 如果数据库中没有用户信息，使用SSO返回的信息
            user = SSOUserInfo(
                id=user_id,
                name=user_info.get("username", ""),
                email=user_info.get("email", ""),
                role="user"
            )
        
        # 创建令牌对象
        token_data = Token(
            access_token=app_token,
            token_type="bearer"
        )
        
        # 返回正确格式的响应
        logger.info(f"SSO回调处理成功 - user_id: {user_id}")
        return SSOTokenResponse(
            data=token_data,
            user=user
        )
    except Exception as e:
        # 记录详细的错误信息
        logger.error(f"处理SSO回调失败 - code: {code[:20]}..., state: {state}")
        logger.error(f"错误类型: {type(e).__name__}")
        logger.error(f"错误消息: {str(e)}")
        logger.exception(f"处理SSO回调时发生错误的完整堆栈跟踪:")
        
        # 抛出包含详细错误信息的APIError
        raise APIError(
            message="处理SSO回调失败",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            details=f"错误类型: {type(e).__name__}, 错误消息: {str(e)}"
        )


@router.get(
    "/logout-url",
    response_model=SSOUrlResponse,
    status_code=status.HTTP_200_OK,
    summary="获取SSO登出URL",
    description="获取同程统一授权登录中心的登出URL"
)
async def get_logout_url(access_token: str) -> SSOUrlResponse:
    """
    获取SSO登出URL。
    
    Args:
        access_token: SSO访问令牌
    
    Returns:
        SSOUrlResponse: 包含SSO登出URL的响应
    """
    try:
        logout_url = await get_sso_logout_url(access_token)
        return SSOUrlResponse(data={"logout_url": logout_url})
    except Exception as e:
        logger.exception(f"获取SSO登出URL时发生错误: {str(e)}")
        raise APIError(
            message="获取SSO登出URL失败",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            details=str(e) if str(e) else None
        )


@router.post(
    "/verify",
    response_model=SSOTokenResponse,
    status_code=status.HTTP_200_OK,
    summary="验证SSO授权码",
    description="验证同程统一授权登录中心的授权码并返回访问令牌",
    responses={
        status.HTTP_401_UNAUTHORIZED: {
            "description": "SSO授权码验证失败",
        },
        status.HTTP_500_INTERNAL_SERVER_ERROR: {
            "description": "服务器内部错误"
        }
    }
)
async def verify_code(code: str, state: str) -> SSOTokenResponse:
    """
    验证SSO授权码并返回访问令牌。
    
    Args:
        code: 授权码
        state: 状态值
    
    Returns:
        SSOTokenResponse: 包含访问令牌和用户信息的响应
    """
    try:
        access_token, user_info, app_token = await exchange_code_for_token(code, state)
        
        # 打印用户信息详情
        logger.info(f"SSO 返回的用户信息类型: {type(user_info)}")
        logger.info(f"SSO 返回的用户信息字段: {list(user_info.keys()) if isinstance(user_info, dict) else 'Not a dict'}")
        logger.info(f"SSO 返回的用户信息内容: {user_info}")
        
        # 创建用户信息对象
        user = SSOUserInfo(
            id=user_info.get("userId", ""),
            name=user_info.get("username", ""),
            email=user_info.get("email", "")
        )
        
        # 创建令牌对象
        token_data = Token(
            access_token=app_token,
            token_type="bearer"
        )
        
        # 返回正确格式的响应
        return SSOTokenResponse(
            data=token_data,
            user=user
        )
    except Exception as e:
        # 其他未预期的异常记录并包装为APIError
        logger.exception(f"验证SSO授权码时发生错误: {str(e)}")
        raise APIError(
            message="验证SSO授权码时发生错误",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            details=str(e) if str(e) else None
        )


@router.post(
    "/logout",
    status_code=status.HTTP_200_OK,
    summary="执行SSO登出",
    description="执行同程统一授权登录中心的登出操作"
)
async def logout(access_token: str) -> dict:
    """
    执行SSO登出操作。
    
    Args:
        access_token: SSO访问令牌
    
    Returns:
        dict: 登出结果
    """
    try:
        success = await sso_logout(access_token)
        return {
            "success": success,
            "message": "登出成功" if success else "登出失败，但本地清理已完成"
        }
    except Exception as e:
        logger.exception(f"执行SSO登出时发生错误: {str(e)}")
        # 即使出错也返回成功，确保前端能够清理本地状态
        return {
            "success": True,
            "message": "登出过程中出现错误，但本地清理已完成"
        }
