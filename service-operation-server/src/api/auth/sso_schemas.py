"""
SSO认证相关的数据模型。

此模块定义与SSO认证相关的请求和响应模型。
"""

from pydantic import BaseModel, Field
from typing import Dict, Any, Optional

from src.api.auth.schemas import Token


class SSOUrlResponse(BaseModel):
    """SSO URL响应模型"""
    data: Dict[str, str] = Field(..., description="包含SSO URL的数据")


class SSOTicketRequest(BaseModel):
    """SSO票据请求模型"""
    ticket: str = Field(..., description="SSO票据")


class SSOUserInfo(BaseModel):
    """SSO用户信息模型"""
    id: str = Field(..., description="用户ID")
    name: str = Field(..., description="用户名")
    email: str = Field(..., description="用户邮箱")
    role: Optional[str] = Field(default="user", description="用户角色")
    department: Optional[str] = Field(None, description="部门")
    work_id: Optional[str] = Field(None, description="工号")


class SSOTokenResponse(BaseModel):
    """SSO令牌响应模型"""
    data: Token = Field(..., description="JWT访问令牌")
    user: SSOUserInfo = Field(..., description="用户信息")
