from fastapi.routing import APIRouter

# 导入各功能模块路由
from src.api.auth.endpoints import router as auth_router
from src.api.health.endpoints import router as health_router
from src.api.passport.endpoints import router as passport_router
from src.api.project.endpoints import router as project_router
from src.api.project_task.endpoints import router as project_task_router
from src.api.train_order.endpoints import router as train_order_router
from src.api.hotel_order.endpoints import router as hotel_order_router

# 主API路由器
api_router = APIRouter()

# 直接包含各功能模块路由，移除版本分组
api_router.include_router(auth_router, prefix="/auth", tags=["认证管理"])
api_router.include_router(health_router, prefix="/health", tags=["健康检查"])
api_router.include_router(passport_router, prefix="/passport", tags=["护照识别"])
api_router.include_router(project_router, prefix="/project", tags=["项目管理"])
api_router.include_router(project_task_router, prefix="/project-task", tags=["项目任务管理"])
api_router.include_router(train_order_router, prefix="/train-order", tags=["火车票订单管理"])
api_router.include_router(hotel_order_router, prefix="/hotel-order", tags=["酒店订单管理"])

# 系统监控相关路由可以在这里添加
# api_router.include_router(monitoring_router, prefix="/monitoring", tags=["系统监控"])

# 注意：添加新的功能模块时，按照功能域组织，创建对应的模块目录
# 然后在这里导入并添加路由
from src.api.system_settings.endpoints import router as system_settings_router
api_router.include_router(system_settings_router, prefix="/system-settings", tags=["系统设置"])

api_router.include_router(system_settings_router, prefix="/system-settings", tags=["系统设置"])
