"""系统设置API端点"""
from fastapi import APIRouter, Depends, HTTPException, status
from typing import List

from src.api.dependencies import get_current_user
from src.db.models import User
from src.services.system_settings_service import SystemSettingsService
from .schemas import (
    SystemSettingResponse,
    SystemSettingCreate,
    SystemSettingUpdate,
    TongchengCredentialsResponse,
    TongchengCredentialsUpdate
)

router = APIRouter()


@router.get("/", response_model=List[SystemSettingResponse])
async def get_user_settings(
    current_user: User = Depends(get_current_user)
):
    """获取当前用户的所有系统设置"""
    try:
        settings = await SystemSettingsService.get_user_settings(current_user.user_id)
        return settings
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取系统设置失败: {str(e)}"
        )


@router.post("/", response_model=SystemSettingResponse)
async def create_setting(
    setting_data: SystemSettingCreate,
    current_user: User = Depends(get_current_user)
):
    """创建或更新系统设置"""
    try:
        setting = await SystemSettingsService.set_setting(
            user_id=current_user.user_id,
            config_key=setting_data.config_key,
            config_value=setting_data.config_value
        )
        return setting
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"设置系统配置失败: {str(e)}"
        )


@router.put("/{config_key}", response_model=SystemSettingResponse)
async def update_setting(
    config_key: str,
    setting_data: SystemSettingUpdate,
    current_user: User = Depends(get_current_user)
):
    """更新指定的系统设置"""
    try:
        setting = await SystemSettingsService.set_setting(
            user_id=current_user.user_id,
            config_key=config_key,
            config_value=setting_data.config_value
        )
        return setting
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新系统配置失败: {str(e)}"
        )


@router.delete("/{config_key}")
async def delete_setting(
    config_key: str,
    current_user: User = Depends(get_current_user)
):
    """删除指定的系统设置"""
    try:
        success = await SystemSettingsService.delete_setting(
            user_id=current_user.user_id,
            config_key=config_key
        )
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="配置项不存在"
            )
        return {"message": "配置项删除成功"}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除系统配置失败: {str(e)}"
        )


@router.get("/tongcheng/credentials", response_model=TongchengCredentialsResponse)
async def get_tongcheng_credentials(
    # current_user: User = Depends(get_current_user)  # 暂时注释掉认证，用于测试
):
    """获取同程管家登录凭证"""
    try:
        # 暂时使用固定用户ID进行测试
        test_user_id = "123497"  # wee.guo
        credentials = await SystemSettingsService.get_tongcheng_credentials(test_user_id)
        return credentials
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取同程管家凭证失败: {str(e)}"
        )


@router.put("/tongcheng/credentials", response_model=TongchengCredentialsResponse)
async def update_tongcheng_credentials(
    credentials_data: TongchengCredentialsUpdate,
    current_user: User = Depends(get_current_user)
):
    """更新同程管家登录凭证"""
    try:
        # 更新用户名
        if credentials_data.username is not None:
            await SystemSettingsService.set_setting(
                user_id=current_user.user_id,
                config_key='tongcheng_username',
                config_value=credentials_data.username
            )
        
        # 更新密码
        if credentials_data.password is not None:
            await SystemSettingsService.set_setting(
                user_id=current_user.user_id,
                config_key='tongcheng_password',
                config_value=credentials_data.password
            )
        
        # 返回更新后的凭证
        credentials = await SystemSettingsService.get_tongcheng_credentials(current_user.user_id)
        return credentials
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新同程管家凭证失败: {str(e)}"
        )