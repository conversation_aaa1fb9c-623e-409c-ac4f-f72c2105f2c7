"""项目管理API端点"""

from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException, Query
from fastapi.responses import JSO<PERSON>esponse
from typing import Optional
from tortoise.exceptions import DoesNotExist
from tortoise.functions import Count
import logging

from src.db.models.project import Project
from src.db.models.user import User
from src.api.dependencies import get_current_user
from .schemas import (
    ProjectCreate, 
    ProjectUpdate, 
    ProjectResponse, 
    ProjectListResponse,
    ProjectQuery
)

router = APIRouter()
logger = logging.getLogger(__name__)


@router.post("/", response_model=ProjectResponse, summary="创建项目")
async def create_project(
    project_data: ProjectCreate,
    current_user: User = Depends(get_current_user)
):
    """
    创建新项目
    - 项目编号会自动生成YYMMDDHHMM格式
    - 创建者信息自动从当前登录用户获取
    """
    try:
        # 将日期字符串转换为datetime对象
        project_date = datetime.strptime(project_data.project_date, "%Y-%m-%d")
        
        # 创建项目
        project = await Project.create(
            project_name=project_data.project_name,
            creator_user_id=current_user.user_id,  # 使用当前登录用户的ID
            creator_name=current_user.username,    # 使用当前登录用户的用户名
            project_description=project_data.project_description,
            client_name=project_data.client_name,
            cost_center=project_data.cost_center or "默认成本中心",  # 提供默认值
            booking_agent_phone=project_data.booking_agent_phone or "",  # 如果为空则使用空字符串
            project_date=project_date  # 使用转换后的datetime对象
        )
        
        return ProjectResponse.model_validate(project)
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=f"日期格式无效，请使用YYYY-MM-DD格式: {str(e)}")
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"创建项目失败: {str(e)}")


@router.get("/{project_id}", response_model=ProjectResponse, summary="获取项目详情")
async def get_project(project_id: int):
    """根据项目ID获取项目详情"""
    try:
        project = await Project.get(id=project_id)
        
        # 获取创建人的部门信息
        creator_department = None
        try:
            creator_user = await User.get(user_id=project.creator_user_id)
            creator_department = creator_user.department
        except DoesNotExist:
            logger.warning(f"创建人用户不存在，用户ID: {project.creator_user_id}")
        
        # 创建响应对象，手动设置creator_department
        project_dict = {
            "id": project.id,
            "project_number": project.project_number,
            "project_name": project.project_name,
            "project_description": project.project_description,
            "client_name": project.client_name,
            "cost_center": project.cost_center,
            "booking_agent_phone": project.booking_agent_phone,
            "project_date": project.project_date,
            "creator_user_id": project.creator_user_id,
            "creator_name": project.creator_name,
            "creator_department": creator_department,
            "created_at": project.created_at,
            "updated_at": project.updated_at
        }
        
        return ProjectResponse.model_validate(project_dict)
    except DoesNotExist:
        raise HTTPException(status_code=404, detail="项目不存在")


@router.get("/", response_model=ProjectListResponse, summary="获取项目列表")
async def list_projects(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    project_name: Optional[str] = Query(None, description="项目名称（模糊查询）"),
    client_name: Optional[str] = Query(None, description="客户名称（模糊查询）"),
    creator_name: Optional[str] = Query(None, description="创建人姓名（模糊查询）"),
    show_all: bool = Query(False, description="是否显示所有用户的项目"),
    current_user: User = Depends(get_current_user)
):
    """
    获取项目列表，支持分页和筛选
    - show_all=false: 只显示当前用户创建的项目（默认）
    - show_all=true: 显示所有用户的项目
    """
    try:
        # 构建查询条件
        query = Project.all()
        query = query.filter(is_deleted=False)
        # 根据show_all参数决定是否过滤用户
        if not show_all:
            # 确保类型匹配：将字符串user_id转换为整数来匹配数据库中的BigIntField
            query = query.filter(creator_user_id=current_user.user_id)
            logger.info(f"只显示用户 {current_user.user_id} 的项目")
        else:
            logger.info(f"显示所有用户的项目")
            
        if project_name:
            query = query.filter(project_name__icontains=project_name)
        if client_name:
            query = query.filter(client_name__icontains=client_name)
        if creator_name:
            query = query.filter(creator_name__icontains=creator_name)
        
        # 获取总数
        total = await query.count()
        
        # 分页查询
        offset = (page - 1) * page_size
        projects = await query.offset(offset).limit(page_size)
        
        # 转换为响应模型
        project_responses = []
        for project in projects:
            try:
                # 获取创建人的部门信息
                creator_department = None
                try:
                    creator_user = await User.get(user_id=project.creator_user_id)
                    creator_department = creator_user.department
                except DoesNotExist:
                    logger.warning(f"创建人用户不存在，用户ID: {project.creator_user_id}")
                
                # 创建响应对象，手动设置creator_department
                project_dict = {
                    "id": project.id,
                    "project_number": project.project_number,
                    "project_name": project.project_name,
                    "project_description": project.project_description,
                    "client_name": project.client_name,
                    "cost_center": project.cost_center,
                    "booking_agent_phone": project.booking_agent_phone,
                    "project_date": project.project_date,
                    "creator_user_id": project.creator_user_id,
                    "creator_name": project.creator_name,
                    "creator_department": creator_department,
                    "created_at": project.created_at,
                    "updated_at": project.updated_at
                }
                
                project_response = ProjectResponse.model_validate(project_dict)
                project_responses.append(project_response)
            except Exception as e:
                logger.error(f"转换项目数据失败: {str(e)}, 项目ID: {project.id}")
                continue
        
        return ProjectListResponse(
            total=total,
            items=project_responses
        )
        
    except Exception as e:
        logger.error(f"查询项目列表失败: {str(e)}")
        raise HTTPException(
            status_code=400,
            detail={
                "message": "查询项目列表失败",
                "error": str(e)
            }
        )


@router.put("/{project_id}", response_model=ProjectResponse, summary="更新项目")
async def update_project(project_id: int, project_data: ProjectUpdate):
    """更新项目信息"""
    try:
        project = await Project.get(id=project_id)
        
        # 更新字段
        update_data = project_data.model_dump(exclude_unset=True)
        
        # 如果有project_date字段，转换为datetime对象
        if 'project_date' in update_data:
            try:
                update_data['project_date'] = datetime.strptime(
                    update_data['project_date'], 
                    "%Y-%m-%d"
                )
            except ValueError as e:
                raise HTTPException(
                    status_code=400, 
                    detail=f"日期格式无效，请使用YYYY-MM-DD格式: {str(e)}"
                )
        
        if update_data:
            await project.update_from_dict(update_data)
            await project.save()
        
        # 重新获取更新后的项目
        updated_project = await Project.get(id=project_id)
        return ProjectResponse.model_validate(updated_project)
        
    except DoesNotExist:
        raise HTTPException(status_code=404, detail="项目不存在")
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"更新项目失败: {str(e)}")


@router.delete("/{project_id}", summary="删除项目")
async def delete_project(project_id: int):
    """软删除项目"""
    try:
        project = await Project.get(id=project_id)
        await project.soft_delete()
        return JSONResponse(content={"message": "项目删除成功"})
        
    except DoesNotExist:
        raise HTTPException(status_code=404, detail="项目不存在")
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"删除项目失败: {str(e)}")


@router.get("/by-number/{project_number}", response_model=ProjectResponse, summary="根据项目编号获取项目")
async def get_project_by_number(project_number: str):
    """根据项目编号获取项目详情"""
    try:
        project = await Project.get(project_number=project_number)
        return ProjectResponse.model_validate(project)
    except DoesNotExist:
        raise HTTPException(status_code=404, detail="项目不存在")


@router.get("/stats/summary", summary="获取项目统计信息")
async def get_project_stats():
    """获取项目统计信息"""
    try:
        total_projects = await Project.all().count()
        
        return {
            "total_projects": total_projects,
            "message": "项目统计信息获取成功"
        }
        
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"获取统计信息失败: {str(e)}") 