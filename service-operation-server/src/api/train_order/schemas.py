"""火车票订单API的数据模式定义"""

from pydantic import BaseModel, ConfigDict, Field, field_serializer, model_validator
from typing import Optional, List, Any
from datetime import date, time, datetime, timedelta
from decimal import Decimal


class TrainOrderBase(BaseModel):
    """火车票订单基础数据模式"""
    project_id: int
    sequence_number: int
    traveler_full_name: str
    traveler_surname: Optional[str] = None
    traveler_given_name: Optional[str] = None
    nationality: Optional[str] = None
    gender: Optional[str] = None
    birth_date: Optional[str] = None
    id_type: Optional[str] = None
    id_number: Optional[str] = None
    id_expiry_date: Optional[str] = None
    mobile_phone: Optional[str] = None
    mobile_phone_country_code: Optional[str] = Field(default="+86", description="手机号国际区号")
    travel_date: Optional[str] = None
    departure_station: Optional[str] = None
    arrival_station: Optional[str] = None
    train_number: Optional[str] = None
    seat_type: Optional[str] = None
    departure_time: Optional[str] = None
    arrival_time: Optional[str] = None
    cost_center: Optional[str] = None
    trip_submission_item: Optional[str] = None
    contact_person: Optional[str] = None
    contact_phone: Optional[str] = None
    contact_email: Optional[str] = None
    approval_reference: Optional[str] = None
    company_name: Optional[str] = None
    booking_agent: Optional[str] = None
    ticket_sms: Optional[str] = None
    amount: Optional[Decimal] = Field(default=0, description="金额")
    order_number: Optional[str] = None
    bill_number: Optional[str] = None
    order_status: Optional[str] = Field(default="initial", description="订单状态")
    fail_reason: Optional[str] = Field(None, description="失败原因")


class TrainOrderCreate(TrainOrderBase):
    """创建火车票订单的数据模式"""
    pass


class TrainOrderUpdate(BaseModel):
    """更新火车票订单的数据模式"""
    traveler_full_name: Optional[str] = None
    traveler_surname: Optional[str] = None
    traveler_given_name: Optional[str] = None
    nationality: Optional[str] = None
    gender: Optional[str] = None
    birth_date: Optional[str] = None
    id_type: Optional[str] = None
    id_number: Optional[str] = None
    id_expiry_date: Optional[str] = None
    mobile_phone: Optional[str] = None
    mobile_phone_country_code: Optional[str] = None
    travel_date: Optional[str] = None
    departure_station: Optional[str] = None
    arrival_station: Optional[str] = None
    train_number: Optional[str] = None
    seat_type: Optional[str] = None
    departure_time: Optional[str] = None
    arrival_time: Optional[str] = None
    cost_center: Optional[str] = None
    trip_submission_item: Optional[str] = None
    contact_person: Optional[str] = None
    contact_phone: Optional[str] = None
    contact_email: Optional[str] = None
    approval_reference: Optional[str] = None
    company_name: Optional[str] = None
    booking_agent: Optional[str] = None
    ticket_sms: Optional[str] = None
    amount: Optional[Decimal] = Field(default=0, description="金额")
    order_number: Optional[str] = None
    bill_number: Optional[str] = None
    order_status: Optional[str] = Field(default="initial", description="订单状态")
    fail_reason: Optional[str] = None


class TrainOrderResponse(BaseModel):
    """火车票订单响应数据模式"""
    id: int
    project_id: int
    sequence_number: int
    traveler_full_name: str
    traveler_surname: Optional[str] = None
    traveler_given_name: Optional[str] = None
    nationality: Optional[str] = None
    gender: Optional[str] = None
    birth_date: Optional[str] = None
    id_type: Optional[str] = None
    id_number: Optional[str] = None
    id_expiry_date: Optional[str] = None
    mobile_phone: Optional[str] = None
    mobile_phone_country_code: Optional[str] = None
    travel_date: Optional[str] = None
    departure_station: Optional[str] = None
    arrival_station: Optional[str] = None
    train_number: Optional[str] = None
    seat_type: Optional[str] = None
    departure_time: Optional[str] = None
    arrival_time: Optional[str] = None
    cost_center: Optional[str] = None
    trip_submission_item: Optional[str] = None
    contact_person: Optional[str] = None
    contact_phone: Optional[str] = None
    contact_email: Optional[str] = None
    approval_reference: Optional[str] = None
    company_name: Optional[str] = None
    booking_agent: Optional[str] = None
    ticket_sms: Optional[str] = None
    amount: Optional[Decimal] = Field(default=0, description="金额")
    order_number: Optional[str] = None
    bill_number: Optional[str] = None
    order_status: Optional[str] = Field(default="initial", description="订单状态")
    fail_reason: Optional[str] = None
    order_type: Optional[str] = Field(None, description="预定类型: book(仅预定), book_and_issue(预定且出票)")
    created_at: datetime
    updated_at: datetime
    is_deleted: bool
    
    model_config = ConfigDict(from_attributes=True)
    

    
    @field_serializer('created_at', 'updated_at')
    def serialize_datetime(self, dt: datetime) -> str:
        """序列化日期时间字段"""
        return dt.isoformat()


class TrainOrderListResponse(BaseModel):
    """火车票订单列表响应"""
    total: int
    page: int
    page_size: int
    items: List[TrainOrderResponse]


class ExcelUploadResponse(BaseModel):
    """Excel上传响应"""
    project_id: int = Field(..., description="项目ID")
    total_orders: int = Field(..., description="成功创建的订单数量")
    failed_orders: int = Field(default=0, description="失败的订单数量")
    skipped_duplicate_orders: int = Field(default=0, description="跳过的重复订单数量")
    file_path: Optional[str] = Field(None, description="保存的Excel文件路径")
    message: str = Field(..., description="处理结果消息")


class ValidationError(BaseModel):
    """数据验证错误"""
    row: int = Field(..., description="行号")
    field: str = Field(..., description="字段名")
    error_type: str = Field(..., description="错误类型")
    message: str = Field(..., description="错误消息")
    value: Optional[str] = Field(None, description="错误值")


class ExcelValidationResponse(BaseModel):
    """Excel验证响应"""
    project_id: int = Field(..., description="项目ID")
    total_rows: int = Field(..., description="总行数")
    valid_rows: int = Field(..., description="有效行数")
    error_rows: int = Field(..., description="错误行数")
    has_errors: bool = Field(..., description="是否有错误")
    errors: List[ValidationError] = Field(default=[], description="验证错误列表")
    message: str = Field(..., description="验证结果消息")
    can_proceed: bool = Field(..., description="是否可以继续导入")
    file_path: Optional[str] = Field(None, description="保存的Excel文件路径")


class BookingRequest(BaseModel):
    """预订请求"""
    booking_type: str = Field(..., description="预订类型: book_only 或 book_and_ticket")
    orders: List[int] = Field(..., description="订单ID列表")
    sms_notify: Optional[bool] = Field(default=False, description="是否短信通知")
    has_agent: Optional[bool] = Field(default=False, description="是否有代订人")
    agent_phone: Optional[str] = Field(None, description="代订人手机号码")


class BookingResponse(BaseModel):
    """预订响应"""
    processed_count: int = Field(..., description="处理的订单数量")
    success_count: int = Field(..., description="成功的订单数量")
    failed_count: int = Field(..., description="失败的订单数量")
    message: str = Field(..., description="处理结果消息")


class ClearOrdersResponse(BaseModel):
    """清空订单响应"""
    deleted_count: int = Field(..., description="删除的订单数量")
    message: str = Field(..., description="处理结果消息")


class TaskToTrainOrderBase(BaseModel):
    """任务订单映射基础模型"""
    project_id: int
    task_id: str
    order_id: int
    order_status: str = Field(default="initial", description="订单状态")
    order_type: str = Field(default="book", description="订单类型: book(预订), book_and_issue(预订且出票)")
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    message: Optional[str] = None

    model_config = ConfigDict(from_attributes=True)


class TaskToTrainOrderCreate(TaskToTrainOrderBase):
    """创建任务订单映射"""
    pass


class TaskToTrainOrderUpdate(BaseModel):
    """更新任务订单映射"""
    order_status: Optional[str] = None
    order_type: Optional[str] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    message: Optional[str] = None

    model_config = ConfigDict(from_attributes=True)


class TaskToTrainOrderResponse(TaskToTrainOrderBase):
    """任务订单映射响应"""
    id: int
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)


class TaskToTrainOrderListResponse(BaseModel):
    """任务订单映射列表响应"""
    mappings: List[TaskToTrainOrderResponse]
    total: int


class CreateBookingTaskRequest(BaseModel):
    """创建预订任务请求"""
    booking_type: str = Field(..., description="预订类型: book_only 或 book_and_ticket")
    task_title: str = Field(..., description="任务标题")
    task_description: Optional[str] = Field(None, description="任务描述")
    sms_notify: bool = Field(default=False, description="是否短信通知")
    has_agent: bool = Field(default=False, description="是否有代订人")
    agent_phone: Optional[str] = Field(None, description="代订人手机号码")
    order_ids: Optional[List[int]] = Field(None, description="指定要预订的订单ID列表，如果为None则预订所有initial状态的订单")


class CreateBookingTaskResponse(BaseModel):
    """创建预订任务响应"""
    task_id: str = Field(..., description="任务ID")
    project_id: int = Field(..., description="项目ID")
    task_title: str = Field(..., description="任务标题")
    submitted_orders_count: int = Field(..., description="提交的订单数量")
    message: str = Field(..., description="处理结果消息")


class ProjectOrderStatsResponse(BaseModel):
    """项目订单统计响应"""
    project_id: int = Field(..., description="项目ID")
    total_orders: int = Field(..., description="总订单数")
    completed_orders: int = Field(..., description="预定完成订单数")
    completed_amount: Decimal = Field(..., description="预定完成订单金额总和")
    
    model_config = ConfigDict(from_attributes=True)
    
    @field_serializer('completed_amount')
    def serialize_amount(self, amount: Decimal) -> str:
        """序列化金额字段为字符串，避免前导0问题"""
        return str(amount)


class ProjectOrderDetailStatsResponse(BaseModel):
    """项目订单详细统计响应"""
    project_id: int = Field(..., description="项目ID")
    total_orders: int = Field(..., description="总订单数")
    check_failed_orders: int = Field(..., description="验证失败订单数")
    initial_orders: int = Field(..., description="待提交订单数")
    submitted_orders: int = Field(..., description="已提交订单数")
    processing_orders: int = Field(..., description="处理中订单数")
    completed_orders: int = Field(..., description="预定完成订单数")
    failed_orders: int = Field(..., description="预定失败订单数")
    completed_amount: Decimal = Field(..., description="预定完成订单金额总和")
    total_amount: Decimal = Field(..., description="所有订单金额总和")
    
    model_config = ConfigDict(from_attributes=True)
    
    @field_serializer('completed_amount', 'total_amount')
    def serialize_amount(self, amount: Decimal) -> str:
        """序列化金额字段为字符串，避免前导0问题"""
        return str(amount)


class TaskOrderStatsResponse(BaseModel):
    """任务订单统计响应"""
    task_id: str = Field(..., description="任务ID")
    order_count: int = Field(..., description="订单总数")
    submitted_orders: int = Field(..., description="已提交订单数")
    processing_orders: int = Field(..., description="处理中订单数")
    completed_orders: int = Field(..., description="预定完成订单数")
    failed_orders: int = Field(..., description="预定失败订单数")
    total_amount: Decimal = Field(..., description="订单总金额")
    total_people: int = Field(..., description="总人数")
    
    model_config = ConfigDict(from_attributes=True) 