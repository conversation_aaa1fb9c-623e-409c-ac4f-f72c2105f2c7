"""
用户管理API端点
"""
from fastapi import APIRouter, HTTPException, Depends, Query
from typing import Optional

from .service import UserManagementService
from .schemas import (
    UserListResponse, RoleListResponse, PermissionListResponse, ApplicationListResponse,
    UserSchema, UserPermissionDetailResponse
)
from src.api.dependencies import get_current_user

router = APIRouter()


@router.get("/users", response_model=UserListResponse)
async def get_users(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    search: Optional[str] = Query(None, description="搜索关键词")
    # current_user=Depends(get_current_user)  # 暂时注释掉认证，用于测试
):
    """获取用户列表"""
    try:
        result = await UserManagementService.get_users(page, page_size, search)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取用户列表失败: {str(e)}")


@router.get("/users/{user_id}", response_model=UserSchema)
async def get_user(
    user_id: int
    # current_user=Depends(get_current_user)  # 暂时注释掉认证，用于测试
):
    """获取用户详情"""
    try:
        user = await UserManagementService.get_user_by_id(user_id)
        if not user:
            raise HTTPException(status_code=404, detail="用户不存在")
        return user
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取用户详情失败: {str(e)}")


@router.get("/roles", response_model=RoleListResponse)
async def get_roles(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    search: Optional[str] = Query(None, description="搜索关键词")
    # current_user=Depends(get_current_user)  # 暂时注释掉认证，用于测试
):
    """获取角色列表"""
    try:
        result = await UserManagementService.get_roles(page, page_size, search)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取角色列表失败: {str(e)}")


@router.get("/permissions", response_model=PermissionListResponse)
async def get_permissions(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    app_id: Optional[int] = Query(None, description="应用ID")
    # current_user=Depends(get_current_user)  # 暂时注释掉认证，用于测试
):
    """获取权限列表"""
    try:
        result = await UserManagementService.get_permissions(page, page_size, search, app_id)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取权限列表失败: {str(e)}")


@router.get("/applications", response_model=ApplicationListResponse)
async def get_applications(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    search: Optional[str] = Query(None, description="搜索关键词")
    # current_user=Depends(get_current_user)  # 暂时注释掉认证，用于测试
):
    """获取应用列表"""
    try:
        result = await UserManagementService.get_applications(page, page_size, search)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取应用列表失败: {str(e)}")


@router.get("/user-permissions/{user_id}", response_model=UserPermissionDetailResponse)
async def get_user_permission_details(
    user_id: int
    # current_user=Depends(get_current_user)  # 暂时注释掉认证，用于测试
):
    """获取用户权限详情"""
    try:
        result = await UserManagementService.get_user_permission_details(user_id)
        if not result:
            raise HTTPException(status_code=404, detail="用户不存在")
        return result
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取用户权限详情失败: {str(e)}")


# 当前用户权限相关接口
@router.get("/me/permissions")
async def get_my_permissions(current_user=Depends(get_current_user)):
    """获取当前用户权限"""
    try:
        # 这里可以根据current_user获取权限信息
        # 暂时返回模拟数据
        return {
            "user": {
                "id": 1,
                "username": current_user.get("username", "admin"),
                "full_name": "系统管理员",
                "email": "<EMAIL>"
            },
            "permissions": [
                "dttrip:access",
                "dttrip:project:view",
                "dttrip:project:create",
                "dttrip:project:edit",
                "user_mgmt:access",
                "user_mgmt:user:view",
                "user_mgmt:user:create",
                "user_mgmt:user:edit"
            ],
            "applications": [
                {
                    "id": 1,
                    "app_name": "DTTrip服务运营平台",
                    "app_code": "dttrip",
                    "app_url": "http://localhost:5173/dashboard"
                },
                {
                    "id": 2,
                    "app_name": "用户管理系统",
                    "app_code": "user_management",
                    "app_url": "http://localhost:5173/user-management"
                }
            ]
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取用户权限失败: {str(e)}")


@router.get("/me/applications")
async def get_my_applications(current_user=Depends(get_current_user)):
    """获取当前用户有权限的应用"""
    try:
        # 暂时返回模拟数据
        return [
            {
                "id": 1,
                "app_name": "DTTrip服务运营平台",
                "app_code": "dttrip",
                "app_url": "http://localhost:5173/dashboard",
                "icon": "building-office",
                "description": "DTTrip服务运营自动化平台"
            },
            {
                "id": 2,
                "app_name": "用户管理系统",
                "app_code": "user_management",
                "app_url": "http://localhost:5173/user-management",
                "icon": "users",
                "description": "用户权限管理系统"
            }
        ]
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取用户应用失败: {str(e)}")
