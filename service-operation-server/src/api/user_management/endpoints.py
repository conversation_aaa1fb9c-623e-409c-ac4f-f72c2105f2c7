"""
用户管理API端点
"""
from fastapi import APIRouter, HTTPException, Depends, Query
from typing import Optional

from .service import UserManagementService
from .schemas import (
    UserListResponse, RoleListResponse, PermissionListResponse, ApplicationListResponse,
    UserSchema, UserPermissionDetailResponse, UpdateUserStatusRequest, UserUpdateRequest,
    RoleUpdateRequest, RoleSchema, PermissionSchema, PermissionCreateRequest, PermissionUpdateRequest
)
from src.api.dependencies import get_current_user

router = APIRouter()


@router.get("/users", response_model=UserListResponse)
async def get_users(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    search: Optional[str] = Query(None, description="搜索关键词")
    # current_user=Depends(get_current_user)  # 暂时注释掉认证，用于测试
):
    """获取用户列表"""
    try:
        result = await UserManagementService.get_users(page, page_size, search)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取用户列表失败: {str(e)}")


@router.get("/users/{user_id}", response_model=UserSchema)
async def get_user(
    user_id: int
    # current_user=Depends(get_current_user)  # 暂时注释掉认证，用于测试
):
    """获取用户详情"""
    try:
        user = await UserManagementService.get_user_by_id(user_id)
        if not user:
            raise HTTPException(status_code=404, detail="用户不存在")
        return user
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取用户详情失败: {str(e)}")


@router.get("/roles", response_model=RoleListResponse)
async def get_roles(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    search: Optional[str] = Query(None, description="搜索关键词")
    # current_user=Depends(get_current_user)  # 暂时注释掉认证，用于测试
):
    """获取角色列表"""
    try:
        result = await UserManagementService.get_roles(page, page_size, search)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取角色列表失败: {str(e)}")


@router.get("/permissions", response_model=PermissionListResponse)
async def get_permissions(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    app_id: Optional[int] = Query(None, description="应用ID")
    # current_user=Depends(get_current_user)  # 暂时注释掉认证，用于测试
):
    """获取权限列表"""
    try:
        result = await UserManagementService.get_permissions(page, page_size, search, app_id)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取权限列表失败: {str(e)}")


@router.post("/permissions", response_model=PermissionSchema)
async def create_permission(
    permission_data: PermissionCreateRequest
    # current_user=Depends(get_current_user)  # 暂时注释掉认证，用于测试
):
    """创建权限"""
    try:
        result = await UserManagementService.create_permission(permission_data)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建权限失败: {str(e)}")


@router.put("/permissions/{permission_id}", response_model=PermissionSchema)
async def update_permission(
    permission_id: int,
    permission_data: PermissionUpdateRequest
    # current_user=Depends(get_current_user)  # 暂时注释掉认证，用于测试
):
    """更新权限"""
    try:
        result = await UserManagementService.update_permission(permission_id, permission_data)
        return result
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新权限失败: {str(e)}")


@router.delete("/permissions/{permission_id}")
async def delete_permission(
    permission_id: int
    # current_user=Depends(get_current_user)  # 暂时注释掉认证，用于测试
):
    """删除权限"""
    try:
        result = await UserManagementService.delete_permission(permission_id)
        if not result:
            raise HTTPException(status_code=404, detail="权限不存在")
        return {"message": "权限删除成功"}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除权限失败: {str(e)}")


@router.get("/roles/{role_id}/permissions")
async def get_role_permissions(
    role_id: int
    # current_user=Depends(get_current_user)  # 暂时注释掉认证，用于测试
):
    """获取角色的权限列表"""
    try:
        result = await UserManagementService.get_role_permissions(role_id)
        return result
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取角色权限失败: {str(e)}")


@router.post("/roles/{role_id}/permissions")
async def assign_role_permissions(
    role_id: int,
    permission_ids: list[int]
    # current_user=Depends(get_current_user)  # 暂时注释掉认证，用于测试
):
    """为角色分配权限"""
    try:
        result = await UserManagementService.assign_role_permissions(role_id, permission_ids)
        return {"message": "角色权限分配成功", "assigned_count": result}
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"分配角色权限失败: {str(e)}")


@router.get("/users/{user_id}/roles-permissions")
async def get_user_roles_permissions(
    user_id: int
    # current_user=Depends(get_current_user)  # 暂时注释掉认证，用于测试
):
    """获取用户的角色和权限信息"""
    try:
        result = await UserManagementService.get_user_roles_permissions(user_id)
        return result
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取用户角色权限失败: {str(e)}")


@router.post("/users/{user_id}/roles")
async def assign_user_roles(
    user_id: int,
    role_ids: list[int]
    # current_user=Depends(get_current_user)  # 暂时注释掉认证，用于测试
):
    """为用户分配角色"""
    try:
        result = await UserManagementService.assign_user_roles(user_id, role_ids)
        return {"message": "用户角色分配成功", "assigned_count": result}
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"分配用户角色失败: {str(e)}")


@router.post("/users/{user_id}/permissions")
async def assign_user_permissions(
    user_id: int,
    permission_ids: list[int]
    # current_user=Depends(get_current_user)  # 暂时注释掉认证，用于测试
):
    """为用户分配特殊权限（除角色权限外的额外权限）"""
    try:
        result = await UserManagementService.assign_user_permissions(user_id, permission_ids)
        return {"message": "用户权限分配成功", "assigned_count": result}
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"分配用户权限失败: {str(e)}")


@router.get("/applications", response_model=ApplicationListResponse)
async def get_applications(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    search: Optional[str] = Query(None, description="搜索关键词")
    # current_user=Depends(get_current_user)  # 暂时注释掉认证，用于测试
):
    """获取应用列表"""
    try:
        result = await UserManagementService.get_applications(page, page_size, search)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取应用列表失败: {str(e)}")


@router.get("/user-permissions/{user_id}", response_model=UserPermissionDetailResponse)
async def get_user_permission_details(
    user_id: int
    # current_user=Depends(get_current_user)  # 暂时注释掉认证，用于测试
):
    """获取用户权限详情"""
    try:
        result = await UserManagementService.get_user_permission_details(user_id)
        if not result:
            raise HTTPException(status_code=404, detail="用户不存在")
        return result
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取用户权限详情失败: {str(e)}")


# 当前用户权限相关接口
@router.get("/me/permissions")
async def get_my_permissions(current_user=Depends(get_current_user)):
    """获取当前用户权限"""
    try:
        # 这里可以根据current_user获取权限信息
        # 暂时返回模拟数据
        return {
            "user": {
                "id": 1,
                "username": current_user.get("username", "admin"),
                "full_name": "系统管理员",
                "email": "<EMAIL>"
            },
            "permissions": [
                "dttrip:access",
                "dttrip:project:view",
                "dttrip:project:create",
                "dttrip:project:edit",
                "user_mgmt:access",
                "user_mgmt:user:view",
                "user_mgmt:user:create",
                "user_mgmt:user:edit"
            ],
            "applications": [
                {
                    "id": 1,
                    "app_name": "DTTrip服务运营平台",
                    "app_code": "dttrip",
                    "app_url": "http://localhost:5173/dashboard"
                },
                {
                    "id": 2,
                    "app_name": "用户管理系统",
                    "app_code": "user_management",
                    "app_url": "http://localhost:5173/user-management"
                }
            ]
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取用户权限失败: {str(e)}")


@router.get("/me/applications")
async def get_my_applications(current_user=Depends(get_current_user)):
    """获取当前用户有权限的应用"""
    try:
        # 暂时返回模拟数据
        return [
            {
                "id": 1,
                "app_name": "DTTrip服务运营平台",
                "app_code": "dttrip",
                "app_url": "http://localhost:5173/dashboard",
                "icon": "building-office",
                "description": "DTTrip服务运营自动化平台"
            },
            {
                "id": 2,
                "app_name": "用户管理系统",
                "app_code": "user_management",
                "app_url": "http://localhost:5173/user-management",
                "icon": "users",
                "description": "用户权限管理系统"
            }
        ]
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取用户应用失败: {str(e)}")


@router.put("/users/{user_id}/status", response_model=UserSchema)
async def update_user_status(
    user_id: int,
    status_request: UpdateUserStatusRequest
    # current_user=Depends(get_current_user)  # 暂时注释掉认证，用于测试
):
    """更新用户状态（启用/停用）"""
    try:
        result = await UserManagementService.update_user_status(user_id, status_request.status)
        return result
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新用户状态失败: {str(e)}")


@router.put("/users/{user_id}", response_model=UserSchema)
async def update_user(
    user_id: int,
    user_data: UserUpdateRequest
    # current_user=Depends(get_current_user)  # 暂时注释掉认证，用于测试
):
    """更新用户信息"""
    try:
        result = await UserManagementService.update_user(user_id, user_data)
        return result
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新用户信息失败: {str(e)}")


@router.put("/roles/{role_id}", response_model=RoleSchema)
async def update_role(
    role_id: int,
    role_data: RoleUpdateRequest
    # current_user=Depends(get_current_user)  # 暂时注释掉认证，用于测试
):
    """更新角色信息"""
    try:
        result = await UserManagementService.update_role(role_id, role_data)
        return result
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新角色信息失败: {str(e)}")
