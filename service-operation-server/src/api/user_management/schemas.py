"""
用户管理相关的数据模型
"""
from typing import List, Optional
from pydantic import BaseModel
from datetime import datetime


class RoleSchema(BaseModel):
    """角色模型"""
    id: int
    role_name: str
    role_code: str
    description: Optional[str] = None
    is_system: bool
    status: bool
    created_at: datetime
    updated_at: datetime
    created_by: Optional[str] = None
    updated_by: Optional[str] = None

    class Config:
        from_attributes = True


class ApplicationSchema(BaseModel):
    """应用模型"""
    id: int
    app_name: str
    app_code: str
    app_url: Optional[str] = None
    description: Optional[str] = None
    icon: Optional[str] = None
    sort_order: int
    status: bool
    created_at: datetime
    updated_at: datetime
    created_by: Optional[str] = None
    updated_by: Optional[str] = None

    class Config:
        from_attributes = True


class PermissionSchema(BaseModel):
    """权限模型"""
    id: int
    permission_name: str
    permission_code: str
    permission_type: str
    parent_id: int
    app_id: Optional[int] = None
    app_name: Optional[str] = None
    resource_path: Optional[str] = None
    description: Optional[str] = None
    sort_order: int
    status: bool
    created_at: datetime
    updated_at: datetime
    created_by: Optional[str] = None
    updated_by: Optional[str] = None

    class Config:
        from_attributes = True


class UserSchema(BaseModel):
    """用户模型"""
    id: int
    username: str
    member_id: Optional[str] = None
    work_id: Optional[str] = None
    email: Optional[str] = None
    full_name: Optional[str] = None
    department: Optional[str] = None
    phone: Optional[str] = None
    avatar: Optional[str] = None
    sso_user_id: Optional[str] = None
    last_login_at: Optional[datetime] = None
    login_count: int = 0
    status: bool = True
    created_at: datetime
    updated_at: datetime
    created_by: Optional[str] = None
    updated_by: Optional[str] = None
    roles: List[RoleSchema] = []
    applications: List[ApplicationSchema] = []
    # 注意：密码字段不在查询结果中返回，仅用于创建和更新

    class Config:
        from_attributes = True


class UserListResponse(BaseModel):
    """用户列表响应"""
    items: List[UserSchema]
    total: int
    page: int
    page_size: int
    pages: int


class RoleListResponse(BaseModel):
    """角色列表响应"""
    items: List[RoleSchema]
    total: int
    page: int
    page_size: int
    pages: int


class PermissionListResponse(BaseModel):
    """权限列表响应"""
    items: List[PermissionSchema]
    total: int
    page: int
    page_size: int
    pages: int


class ApplicationListResponse(BaseModel):
    """应用列表响应"""
    items: List[ApplicationSchema]
    total: int
    page: int
    page_size: int
    pages: int


class UserCreateRequest(BaseModel):
    """用户创建请求"""
    username: str
    member_id: Optional[str] = None
    work_id: Optional[str] = None
    email: Optional[str] = None
    full_name: Optional[str] = None
    department: Optional[str] = None
    phone: Optional[str] = None
    password: str  # 明文密码，将被加密存储
    sso_user_id: Optional[str] = None
    role_ids: List[int] = []
    app_ids: List[int] = []


class UserUpdateRequest(BaseModel):
    """用户更新请求"""
    username: Optional[str] = None
    member_id: Optional[str] = None
    work_id: Optional[str] = None
    email: Optional[str] = None
    full_name: Optional[str] = None
    department: Optional[str] = None
    phone: Optional[str] = None
    password: Optional[str] = None  # 可选的新密码，将被加密存储
    status: Optional[bool] = None
    role_ids: Optional[List[int]] = None
    app_ids: Optional[List[int]] = None


class ChangePasswordRequest(BaseModel):
    """修改密码请求模型"""
    old_password: str  # 旧密码
    new_password: str  # 新密码

    class Config:
        from_attributes = True


class ResetPasswordRequest(BaseModel):
    """重置密码请求模型（管理员操作）"""
    new_password: str  # 新密码

    class Config:
        from_attributes = True


class UpdateUserStatusRequest(BaseModel):
    """更新用户状态请求模型"""
    status: bool  # 用户状态：True=启用，False=停用

    class Config:
        from_attributes = True


class RoleCreateRequest(BaseModel):
    """角色创建请求"""
    role_name: str
    role_code: str
    description: Optional[str] = None
    permission_ids: List[int] = []


class RoleUpdateRequest(BaseModel):
    """角色更新请求"""
    role_name: Optional[str] = None
    role_code: Optional[str] = None
    description: Optional[str] = None
    status: Optional[bool] = None
    permission_ids: Optional[List[int]] = None


class PermissionCreateRequest(BaseModel):
    """权限创建请求"""
    permission_name: str
    permission_code: str
    permission_type: str = "menu"
    parent_id: int = 0
    app_id: Optional[int] = None
    resource_path: Optional[str] = None
    description: Optional[str] = None
    sort_order: int = 0


class PermissionUpdateRequest(BaseModel):
    """权限更新请求"""
    permission_name: Optional[str] = None
    permission_type: Optional[str] = None
    parent_id: Optional[int] = None
    app_id: Optional[int] = None
    resource_path: Optional[str] = None
    description: Optional[str] = None
    sort_order: Optional[int] = None
    status: Optional[bool] = None


class UserPermissionAssignRequest(BaseModel):
    """用户权限分配请求"""
    user_id: int
    role_ids: Optional[List[int]] = None
    app_ids: Optional[List[int]] = None
    permission_ids: Optional[List[int]] = None
    permission_type: str = "grant"  # grant 或 deny


class UserPermissionDetailResponse(BaseModel):
    """用户权限详情响应"""
    user: UserSchema
    role_permissions: List[dict]
    special_permissions: List[PermissionSchema]
    applications: List[ApplicationSchema]
