"""
用户管理服务
"""
from typing import List, Optional, Dict, Any
from datetime import datetime
from tortoise import connections
from tortoise.exceptions import DoesNotExist, IntegrityError

from .schemas import (
    UserSchema, RoleSchema, PermissionSchema, ApplicationSchema,
    UserListResponse, RoleListResponse, PermissionListResponse, ApplicationListResponse,
    UserCreateRequest, UserUpdateRequest, RoleCreateRequest, RoleUpdateRequest,
    PermissionCreateRequest, PermissionUpdateRequest, UserPermissionAssignRequest,
    UserPermissionDetailResponse, ChangePasswordRequest, ResetPasswordRequest,
    UpdateUserStatusRequest
)
from .password_service import password_service


class UserManagementService:
    """用户管理服务类"""

    @staticmethod
    def extract_query_result(result):
        """提取查询结果，处理 execute_query 返回的元组格式"""
        if isinstance(result, tuple) and len(result) > 1:
            return result[1]  # 返回实际的结果列表
        return result if isinstance(result, list) else []

    @staticmethod
    async def get_users(page: int = 1, page_size: int = 20, search: str = None) -> UserListResponse:
        """获取用户列表"""
        connection = connections.get("default")
        
        # 构建查询条件
        where_conditions = []  # 查询所有用户（包括已停用的）
        params = []
        
        if search:
            where_conditions.append("(u.username LIKE %s OR u.member_id LIKE %s OR u.work_id LIKE %s OR u.email LIKE %s OR u.full_name LIKE %s)")
            search_param = f"%{search}%"
            params.extend([search_param, search_param, search_param, search_param, search_param])
        
        where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"
        
        # 查询总数
        count_sql = f"""
        SELECT COUNT(*) as total
        FROM users u
        WHERE {where_clause}
        """
        
        count_result = await connection.execute_query(count_sql, params)
        result_list = UserManagementService.extract_query_result(count_result)
        total = result_list[0]['total'] if result_list else 0
        
        # 查询用户列表
        offset = (page - 1) * page_size
        
        users_sql = f"""
        SELECT
            u.user_id as id,
            u.username,
            u.member_id,
            u.work_id,
            u.email,
            u.full_name,
            u.department,
            u.phone_number as phone,
            u.sso_user_id,
            u.last_login_at,
            u.login_count,
            u.status,
            u.created_at,
            u.updated_at,
            u.created_by,
            u.updated_by
        FROM users u
        WHERE {where_clause}
        ORDER BY u.created_at DESC
        LIMIT %s OFFSET %s
        """
        
        params.extend([page_size, offset])
        users_result = await connection.execute_query(users_sql, params)
        users_data = UserManagementService.extract_query_result(users_result)

        # 转换为用户对象
        users = []
        for row in users_data:
            # 获取用户角色
            roles_sql = """
            SELECT r.id, r.role_name, r.role_code, r.description, r.is_system, r.status,
                   r.created_at, r.updated_at, r.created_by, r.updated_by
            FROM roles r
            INNER JOIN user_roles ur ON r.id = ur.role_id
            WHERE ur.user_id = %s AND ur.status = 1 AND r.status = 1
            """
            roles_result = await connection.execute_query(roles_sql, [row['id']])
            roles_data = UserManagementService.extract_query_result(roles_result)
            roles = [RoleSchema(**role) for role in roles_data]
            
            # 获取用户应用权限
            apps_sql = """
            SELECT a.id, a.app_name, a.app_code, a.app_url, a.description, a.icon,
                   a.sort_order, a.status, a.created_at, a.updated_at, a.created_by, a.updated_by
            FROM applications a
            INNER JOIN user_app_permissions uap ON a.id = uap.app_id
            WHERE uap.user_id = %s AND uap.status = 1 AND a.status = 1
            """
            apps_result = await connection.execute_query(apps_sql, [row['id']])
            apps_data = UserManagementService.extract_query_result(apps_result)
            applications = [ApplicationSchema(**app) for app in apps_data]
            
            user_data = dict(row)
            user_data['roles'] = roles
            user_data['applications'] = applications
            users.append(UserSchema(**user_data))
        
        pages = (total + page_size - 1) // page_size
        
        return UserListResponse(
            items=users,
            total=total,
            page=page,
            page_size=page_size,
            pages=pages
        )

    @staticmethod
    async def get_user_by_id(user_id: int, include_inactive: bool = False) -> Optional[UserSchema]:
        """根据ID获取用户"""
        connection = connections.get("default")

        # 根据参数决定是否包含非活跃用户
        status_condition = "" if include_inactive else "AND u.status = 1"

        user_sql = f"""
        SELECT
            u.user_id as id,
            u.username,
            u.member_id,
            u.work_id,
            u.email,
            u.full_name,
            u.department,
            u.phone_number as phone,
            u.sso_user_id,
            u.last_login_at,
            u.login_count,
            u.status,
            u.created_at,
            u.updated_at,
            u.created_by,
            u.updated_by
        FROM users u
        WHERE u.user_id = %s {status_condition}
        """
        
        user_result = await connection.execute_query(user_sql, [user_id])
        user_data_list = UserManagementService.extract_query_result(user_result)
        if not user_data_list:
            return None

        user_data = dict(user_data_list[0])
        
        # 获取用户角色
        roles_sql = """
        SELECT r.id, r.role_name, r.role_code, r.description, r.is_system, r.status,
               r.created_at, r.updated_at, r.created_by, r.updated_by
        FROM roles r
        INNER JOIN user_roles ur ON r.id = ur.role_id
        WHERE ur.user_id = %s AND ur.status = 1 AND r.status = 1
        """
        roles_result = await connection.execute_query(roles_sql, [user_id])
        roles_data = UserManagementService.extract_query_result(roles_result)
        roles = [RoleSchema(**role) for role in roles_data]
        
        # 获取用户应用权限
        apps_sql = """
        SELECT a.id, a.app_name, a.app_code, a.app_url, a.description, a.icon,
               a.sort_order, a.status, a.created_at, a.updated_at, a.created_by, a.updated_by
        FROM applications a
        INNER JOIN user_app_permissions uap ON a.id = uap.app_id
        WHERE uap.user_id = %s AND uap.status = 1 AND a.status = 1
        """
        apps_result = await connection.execute_query(apps_sql, [user_id])
        apps_data = UserManagementService.extract_query_result(apps_result)
        applications = [ApplicationSchema(**app) for app in apps_data]
        
        user_data['roles'] = roles
        user_data['applications'] = applications
        
        return UserSchema(**user_data)

    @staticmethod
    async def get_roles(page: int = 1, page_size: int = 20, search: str = None) -> RoleListResponse:
        """获取角色列表"""
        connection = connections.get("default")
        
        # 构建查询条件
        where_conditions = ["r.status = 1"]
        params = []
        
        if search:
            where_conditions.append("r.role_name LIKE %s")
            params.append(f"%{search}%")
        
        where_clause = " AND ".join(where_conditions)
        
        # 查询总数
        count_sql = f"""
        SELECT COUNT(*) as total
        FROM roles r
        WHERE {where_clause}
        """
        
        count_result = await connection.execute_query(count_sql, params)
        count_data = UserManagementService.extract_query_result(count_result)
        total = count_data[0]['total'] if count_data else 0
        
        # 查询角色列表
        offset = (page - 1) * page_size
        
        roles_sql = f"""
        SELECT r.id, r.role_name, r.role_code, r.description, r.is_system, r.status,
               r.created_at, r.updated_at, r.created_by, r.updated_by
        FROM roles r
        WHERE {where_clause}
        ORDER BY r.created_at DESC
        LIMIT %s OFFSET %s
        """
        
        params.extend([page_size, offset])
        roles_result = await connection.execute_query(roles_sql, params)
        roles_data = UserManagementService.extract_query_result(roles_result)
        roles = [RoleSchema(**role) for role in roles_data]
        pages = (total + page_size - 1) // page_size
        
        return RoleListResponse(
            items=roles,
            total=total,
            page=page,
            page_size=page_size,
            pages=pages
        )

    @staticmethod
    async def get_permissions(page: int = 1, page_size: int = 20, search: str = None, app_id: int = None) -> PermissionListResponse:
        """获取权限列表（只返回菜单权限/应用权限）"""
        connection = connections.get("default")

        # 构建查询条件，只查询菜单权限
        where_conditions = ["p.status = 1", "p.permission_type = 'menu'"]
        params = []
        
        if search:
            where_conditions.append("(p.permission_name LIKE %s OR p.permission_code LIKE %s)")
            search_param = f"%{search}%"
            params.extend([search_param, search_param])
        
        if app_id:
            where_conditions.append("p.app_id = %s")
            params.append(app_id)
        
        where_clause = " AND ".join(where_conditions)
        
        # 查询总数
        count_sql = f"""
        SELECT COUNT(*) as total
        FROM permissions p
        WHERE {where_clause}
        """
        
        count_result = await connection.execute_query(count_sql, params)
        count_data = UserManagementService.extract_query_result(count_result)
        total = count_data[0]['total'] if count_data else 0
        
        # 查询权限列表
        offset = (page - 1) * page_size
        
        permissions_sql = f"""
        SELECT 
            p.id, p.permission_name, p.permission_code, p.permission_type, p.parent_id,
            p.app_id, a.app_name, p.resource_path, p.description, p.sort_order, p.status,
            p.created_at, p.updated_at, p.created_by, p.updated_by
        FROM permissions p
        LEFT JOIN applications a ON p.app_id = a.id
        WHERE {where_clause}
        ORDER BY p.sort_order, p.created_at DESC
        LIMIT %s OFFSET %s
        """
        
        params.extend([page_size, offset])
        permissions_result = await connection.execute_query(permissions_sql, params)
        permissions_data = UserManagementService.extract_query_result(permissions_result)
        permissions = [PermissionSchema(**permission) for permission in permissions_data]
        pages = (total + page_size - 1) // page_size
        
        return PermissionListResponse(
            items=permissions,
            total=total,
            page=page,
            page_size=page_size,
            pages=pages
        )

    @staticmethod
    async def create_permission(permission_data: PermissionCreateRequest) -> PermissionSchema:
        """创建权限"""
        connection = connections.get("default")

        try:
            # 检查权限编码是否已存在
            check_sql = "SELECT id FROM permissions WHERE permission_code = %s"
            check_result = await connection.execute_query(check_sql, [permission_data.permission_code])
            check_data = UserManagementService.extract_query_result(check_result)

            if check_data:
                raise Exception("权限编码已存在")

            # 插入新权限
            insert_sql = """
            INSERT INTO permissions (
                permission_name, permission_code, permission_type, parent_id, app_id,
                resource_path, description, sort_order, status, created_at, updated_at, created_by
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, 1, NOW(), NOW(), 'system')
            """

            await connection.execute_query(insert_sql, [
                permission_data.permission_name,
                permission_data.permission_code,
                permission_data.permission_type,
                permission_data.parent_id,
                permission_data.app_id,
                permission_data.resource_path,
                permission_data.description,
                permission_data.sort_order
            ])

            # 获取新创建的权限
            get_sql = """
            SELECT
                p.id, p.permission_name, p.permission_code, p.permission_type, p.parent_id,
                p.app_id, a.app_name, p.resource_path, p.description, p.sort_order, p.status,
                p.created_at, p.updated_at, p.created_by, p.updated_by
            FROM permissions p
            LEFT JOIN applications a ON p.app_id = a.id
            WHERE p.permission_code = %s
            """

            result = await connection.execute_query(get_sql, [permission_data.permission_code])
            data = UserManagementService.extract_query_result(result)

            if not data:
                raise Exception("创建权限失败")

            return PermissionSchema(**data[0])

        except Exception as e:
            raise Exception(f"创建权限失败: {str(e)}")

    @staticmethod
    async def update_permission(permission_id: int, permission_data: PermissionUpdateRequest) -> PermissionSchema:
        """更新权限"""
        connection = connections.get("default")

        try:
            # 检查权限是否存在
            check_sql = "SELECT id FROM permissions WHERE id = %s"
            check_result = await connection.execute_query(check_sql, [permission_id])
            check_data = UserManagementService.extract_query_result(check_result)

            if not check_data:
                raise ValueError("权限不存在")

            # 构建更新字段
            update_fields = []
            params = []

            if permission_data.permission_name is not None:
                update_fields.append("permission_name = %s")
                params.append(permission_data.permission_name)

            if permission_data.permission_type is not None:
                update_fields.append("permission_type = %s")
                params.append(permission_data.permission_type)

            if permission_data.parent_id is not None:
                update_fields.append("parent_id = %s")
                params.append(permission_data.parent_id)

            if permission_data.app_id is not None:
                update_fields.append("app_id = %s")
                params.append(permission_data.app_id)

            if permission_data.resource_path is not None:
                update_fields.append("resource_path = %s")
                params.append(permission_data.resource_path)

            if permission_data.description is not None:
                update_fields.append("description = %s")
                params.append(permission_data.description)

            if permission_data.sort_order is not None:
                update_fields.append("sort_order = %s")
                params.append(permission_data.sort_order)

            if permission_data.status is not None:
                update_fields.append("status = %s")
                params.append(permission_data.status)

            if not update_fields:
                raise Exception("没有提供要更新的字段")

            # 添加更新时间
            update_fields.append("updated_at = NOW()")
            update_fields.append("updated_by = 'system'")

            # 执行更新
            update_sql = f"UPDATE permissions SET {', '.join(update_fields)} WHERE id = %s"
            params.append(permission_id)

            await connection.execute_query(update_sql, params)

            # 获取更新后的权限
            get_sql = """
            SELECT
                p.id, p.permission_name, p.permission_code, p.permission_type, p.parent_id,
                p.app_id, a.app_name, p.resource_path, p.description, p.sort_order, p.status,
                p.created_at, p.updated_at, p.created_by, p.updated_by
            FROM permissions p
            LEFT JOIN applications a ON p.app_id = a.id
            WHERE p.id = %s
            """

            result = await connection.execute_query(get_sql, [permission_id])
            data = UserManagementService.extract_query_result(result)

            return PermissionSchema(**data[0])

        except ValueError:
            raise
        except Exception as e:
            raise Exception(f"更新权限失败: {str(e)}")

    @staticmethod
    async def delete_permission(permission_id: int) -> bool:
        """删除权限"""
        connection = connections.get("default")

        try:
            # 检查权限是否存在
            check_sql = "SELECT id FROM permissions WHERE id = %s"
            check_result = await connection.execute_query(check_sql, [permission_id])
            check_data = UserManagementService.extract_query_result(check_result)

            if not check_data:
                return False

            # 检查是否有角色使用此权限
            role_check_sql = "SELECT id FROM role_permissions WHERE permission_id = %s"
            role_result = await connection.execute_query(role_check_sql, [permission_id])
            role_data = UserManagementService.extract_query_result(role_result)

            if role_data:
                # 先删除角色权限关联
                await connection.execute_query("DELETE FROM role_permissions WHERE permission_id = %s", [permission_id])

            # 删除权限
            delete_sql = "DELETE FROM permissions WHERE id = %s"
            await connection.execute_query(delete_sql, [permission_id])

            return True

        except Exception as e:
            raise Exception(f"删除权限失败: {str(e)}")

    @staticmethod
    async def get_role_permissions(role_id: int) -> dict:
        """获取角色的权限列表"""
        connection = connections.get("default")

        try:
            # 检查角色是否存在
            role_check_sql = "SELECT id, role_name FROM roles WHERE id = %s"
            role_result = await connection.execute_query(role_check_sql, [role_id])
            role_data = UserManagementService.extract_query_result(role_result)

            if not role_data:
                raise ValueError("角色不存在")

            role_info = role_data[0]

            # 获取所有可用权限
            all_permissions_sql = """
            SELECT
                p.id, p.permission_name, p.permission_code, p.permission_type,
                p.app_id, a.app_name, p.resource_path, p.description, p.sort_order
            FROM permissions p
            LEFT JOIN applications a ON p.app_id = a.id
            WHERE p.status = 1 AND p.permission_type = 'menu'
            ORDER BY p.sort_order, p.created_at DESC
            """

            all_permissions_result = await connection.execute_query(all_permissions_sql)
            all_permissions_data = UserManagementService.extract_query_result(all_permissions_result)

            # 获取角色已分配的权限
            role_permissions_sql = """
            SELECT p.id
            FROM role_permissions rp
            INNER JOIN permissions p ON rp.permission_id = p.id
            WHERE rp.role_id = %s AND rp.status = 1 AND p.status = 1
            """

            role_permissions_result = await connection.execute_query(role_permissions_sql, [role_id])
            role_permissions_data = UserManagementService.extract_query_result(role_permissions_result)
            assigned_permission_ids = [item['id'] for item in role_permissions_data]

            # 构建权限列表，标记已分配的权限
            permissions = []
            for perm in all_permissions_data:
                permissions.append({
                    **perm,
                    'assigned': perm['id'] in assigned_permission_ids
                })

            return {
                "role": role_info,
                "permissions": permissions,
                "assigned_permission_ids": assigned_permission_ids
            }

        except ValueError:
            raise
        except Exception as e:
            raise Exception(f"获取角色权限失败: {str(e)}")

    @staticmethod
    async def assign_role_permissions(role_id: int, permission_ids: list[int]) -> int:
        """为角色分配权限"""
        connection = connections.get("default")

        try:
            # 检查角色是否存在
            role_check_sql = "SELECT id FROM roles WHERE id = %s"
            role_result = await connection.execute_query(role_check_sql, [role_id])
            role_data = UserManagementService.extract_query_result(role_result)

            if not role_data:
                raise ValueError("角色不存在")

            # 删除角色现有的权限分配
            delete_sql = "DELETE FROM role_permissions WHERE role_id = %s"
            await connection.execute_query(delete_sql, [role_id])

            # 分配新的权限
            assigned_count = 0
            for permission_id in permission_ids:
                # 检查权限是否存在
                perm_check_sql = "SELECT id FROM permissions WHERE id = %s AND status = 1"
                perm_result = await connection.execute_query(perm_check_sql, [permission_id])
                perm_data = UserManagementService.extract_query_result(perm_result)

                if perm_data:
                    insert_sql = """
                    INSERT INTO role_permissions (role_id, permission_id, granted_by, status, created_at, updated_at)
                    VALUES (%s, %s, 'system', 1, NOW(), NOW())
                    """
                    await connection.execute_query(insert_sql, [role_id, permission_id])
                    assigned_count += 1

            return assigned_count

        except ValueError:
            raise
        except Exception as e:
            raise Exception(f"分配角色权限失败: {str(e)}")

    @staticmethod
    async def get_user_roles_permissions(user_id: int) -> dict:
        """获取用户的角色和权限信息"""
        connection = connections.get("default")

        try:
            # 检查用户是否存在
            user_check_sql = "SELECT user_id as id, username, full_name FROM users WHERE user_id = %s"
            user_result = await connection.execute_query(user_check_sql, [user_id])
            user_data = UserManagementService.extract_query_result(user_result)

            if not user_data:
                raise ValueError("用户不存在")

            user_info = user_data[0]

            # 获取所有可用角色
            all_roles_sql = """
            SELECT id, role_name, role_code, description, is_system
            FROM roles
            WHERE status = 1
            ORDER BY role_name
            """

            all_roles_result = await connection.execute_query(all_roles_sql)
            all_roles_data = UserManagementService.extract_query_result(all_roles_result)

            # 获取用户已分配的角色
            user_roles_sql = """
            SELECT r.id
            FROM user_roles ur
            INNER JOIN roles r ON ur.role_id = r.id
            INNER JOIN users u ON ur.user_id = u.id
            WHERE u.user_id = %s AND ur.status = 1 AND r.status = 1
            """

            user_roles_result = await connection.execute_query(user_roles_sql, [user_id])
            user_roles_data = UserManagementService.extract_query_result(user_roles_result)
            assigned_role_ids = [item['id'] for item in user_roles_data]

            # 构建角色列表，标记已分配的角色
            roles = []
            for role in all_roles_data:
                roles.append({
                    **role,
                    'assigned': role['id'] in assigned_role_ids
                })

            # 获取所有可用权限
            all_permissions_sql = """
            SELECT
                p.id, p.permission_name, p.permission_code, p.permission_type,
                p.app_id, a.app_name, p.resource_path, p.description, p.sort_order
            FROM permissions p
            LEFT JOIN applications a ON p.app_id = a.id
            WHERE p.status = 1 AND p.permission_type = 'menu'
            ORDER BY p.sort_order, p.created_at DESC
            """

            all_permissions_result = await connection.execute_query(all_permissions_sql)
            all_permissions_data = UserManagementService.extract_query_result(all_permissions_result)

            # 获取用户通过角色获得的权限
            role_permissions_sql = """
            SELECT DISTINCT p.id
            FROM user_roles ur
            INNER JOIN role_permissions rp ON ur.role_id = rp.role_id
            INNER JOIN permissions p ON rp.permission_id = p.id
            INNER JOIN users u ON ur.user_id = u.id
            WHERE u.user_id = %s AND ur.status = 1 AND rp.status = 1 AND p.status = 1
            """

            role_permissions_result = await connection.execute_query(role_permissions_sql, [user_id])
            role_permissions_data = UserManagementService.extract_query_result(role_permissions_result)
            role_permission_ids = [item['id'] for item in role_permissions_data]

            # 获取用户的特殊权限（直接分配的权限）
            user_permissions_sql = """
            SELECT p.id
            FROM user_permissions up
            INNER JOIN permissions p ON up.permission_id = p.id
            INNER JOIN users u ON up.user_id = u.id
            WHERE u.user_id = %s AND up.status = 1 AND p.status = 1 AND up.permission_type = 'grant'
            """

            user_permissions_result = await connection.execute_query(user_permissions_sql, [user_id])
            user_permissions_data = UserManagementService.extract_query_result(user_permissions_result)
            user_permission_ids = [item['id'] for item in user_permissions_data]

            # 构建权限列表，标记权限来源
            permissions = []
            for perm in all_permissions_data:
                has_role_permission = perm['id'] in role_permission_ids
                has_user_permission = perm['id'] in user_permission_ids
                permissions.append({
                    **perm,
                    'has_role_permission': has_role_permission,
                    'has_user_permission': has_user_permission,
                    'effective': has_role_permission or has_user_permission
                })

            return {
                "user": user_info,
                "roles": roles,
                "permissions": permissions,
                "assigned_role_ids": assigned_role_ids,
                "user_permission_ids": user_permission_ids
            }

        except ValueError:
            raise
        except Exception as e:
            raise Exception(f"获取用户角色权限失败: {str(e)}")

    @staticmethod
    async def assign_user_roles(user_id: int, role_ids: list[int]) -> int:
        """为用户分配角色"""
        connection = connections.get("default")

        try:
            # 检查用户是否存在
            user_check_sql = "SELECT user_id FROM users WHERE user_id = %s"
            user_result = await connection.execute_query(user_check_sql, [user_id])
            user_data = UserManagementService.extract_query_result(user_result)

            if not user_data:
                raise ValueError("用户不存在")

            # 删除用户现有的角色分配（需要通过user_id关联）
            delete_sql = """
            DELETE ur FROM user_roles ur
            INNER JOIN users u ON ur.user_id = u.id
            WHERE u.user_id = %s
            """
            await connection.execute_query(delete_sql, [user_id])

            # 分配新的角色
            assigned_count = 0
            for role_id in role_ids:
                # 检查角色是否存在
                role_check_sql = "SELECT id FROM roles WHERE id = %s AND status = 1"
                role_result = await connection.execute_query(role_check_sql, [role_id])
                role_data = UserManagementService.extract_query_result(role_result)

                if role_data:
                    # 获取用户的内部ID
                    internal_user_sql = "SELECT id FROM users WHERE user_id = %s"
                    internal_user_result = await connection.execute_query(internal_user_sql, [user_id])
                    internal_user_data = UserManagementService.extract_query_result(internal_user_result)

                    if internal_user_data:
                        internal_user_id = internal_user_data[0]['id']
                        insert_sql = """
                        INSERT INTO user_roles (user_id, role_id, assigned_by, status, created_at, updated_at)
                        VALUES (%s, %s, 'system', 1, NOW(), NOW())
                        """
                        await connection.execute_query(insert_sql, [internal_user_id, role_id])
                        assigned_count += 1

            return assigned_count

        except ValueError:
            raise
        except Exception as e:
            raise Exception(f"分配用户角色失败: {str(e)}")

    @staticmethod
    async def assign_user_permissions(user_id: int, permission_ids: list[int]) -> int:
        """为用户分配特殊权限（除角色权限外的额外权限）"""
        connection = connections.get("default")

        try:
            # 检查用户是否存在
            user_check_sql = "SELECT user_id FROM users WHERE user_id = %s"
            user_result = await connection.execute_query(user_check_sql, [user_id])
            user_data = UserManagementService.extract_query_result(user_result)

            if not user_data:
                raise ValueError("用户不存在")

            # 删除用户现有的特殊权限分配（需要通过user_id关联）
            delete_sql = """
            DELETE up FROM user_permissions up
            INNER JOIN users u ON up.user_id = u.id
            WHERE u.user_id = %s
            """
            await connection.execute_query(delete_sql, [user_id])

            # 分配新的特殊权限
            assigned_count = 0
            for permission_id in permission_ids:
                # 检查权限是否存在
                perm_check_sql = "SELECT id FROM permissions WHERE id = %s AND status = 1"
                perm_result = await connection.execute_query(perm_check_sql, [permission_id])
                perm_data = UserManagementService.extract_query_result(perm_result)

                if perm_data:
                    # 获取用户的内部ID
                    internal_user_sql = "SELECT id FROM users WHERE user_id = %s"
                    internal_user_result = await connection.execute_query(internal_user_sql, [user_id])
                    internal_user_data = UserManagementService.extract_query_result(internal_user_result)

                    if internal_user_data:
                        internal_user_id = internal_user_data[0]['id']
                        insert_sql = """
                        INSERT INTO user_permissions (user_id, permission_id, permission_type, granted_by, status, created_at, updated_at)
                        VALUES (%s, %s, 'grant', 'system', 1, NOW(), NOW())
                        """
                        await connection.execute_query(insert_sql, [internal_user_id, permission_id])
                        assigned_count += 1

            return assigned_count

        except ValueError:
            raise
        except Exception as e:
            raise Exception(f"分配用户权限失败: {str(e)}")

    @staticmethod
    async def get_applications(page: int = 1, page_size: int = 20, search: str = None) -> ApplicationListResponse:
        """获取应用列表"""
        connection = connections.get("default")
        
        # 构建查询条件
        where_conditions = ["a.status = 1"]
        params = []
        
        if search:
            where_conditions.append("a.app_name LIKE %s")
            params.append(f"%{search}%")
        
        where_clause = " AND ".join(where_conditions)
        
        # 查询总数
        count_sql = f"""
        SELECT COUNT(*) as total
        FROM applications a
        WHERE {where_clause}
        """
        
        count_result = await connection.execute_query(count_sql, params)
        count_data = UserManagementService.extract_query_result(count_result)
        total = count_data[0]['total'] if count_data else 0
        
        # 查询应用列表
        offset = (page - 1) * page_size
        
        apps_sql = f"""
        SELECT a.id, a.app_name, a.app_code, a.app_url, a.description, a.icon,
               a.sort_order, a.status, a.created_at, a.updated_at, a.created_by, a.updated_by
        FROM applications a
        WHERE {where_clause}
        ORDER BY a.sort_order, a.created_at DESC
        LIMIT %s OFFSET %s
        """
        
        params.extend([page_size, offset])
        apps_result = await connection.execute_query(apps_sql, params)
        apps_data = UserManagementService.extract_query_result(apps_result)
        applications = [ApplicationSchema(**app) for app in apps_data]
        pages = (total + page_size - 1) // page_size
        
        return ApplicationListResponse(
            items=applications,
            total=total,
            page=page,
            page_size=page_size,
            pages=pages
        )

    @staticmethod
    async def get_user_permission_details(user_id: int) -> Optional[UserPermissionDetailResponse]:
        """获取用户权限详情"""
        connection = connections.get("default")
        
        # 获取用户基本信息
        user = await UserManagementService.get_user_by_id(user_id)
        if not user:
            return None
        
        # 获取用户角色权限
        role_permissions_sql = """
        SELECT 
            r.id as role_id, r.role_name, r.role_code, r.is_system,
            p.id as permission_id, p.permission_name, p.permission_code, p.permission_type
        FROM roles r
        INNER JOIN user_roles ur ON r.id = ur.role_id
        INNER JOIN role_permissions rp ON r.id = rp.role_id
        INNER JOIN permissions p ON rp.permission_id = p.id
        WHERE ur.user_id = %s AND ur.status = 1 AND r.status = 1 AND rp.status = 1 AND p.status = 1
        ORDER BY r.role_name, p.sort_order
        """
        
        role_permissions_result = await connection.execute_query(role_permissions_sql, [user_id])
        role_permissions_data = UserManagementService.extract_query_result(role_permissions_result)

        # 组织角色权限数据
        role_permissions = {}
        for row in role_permissions_data:
            role_id = row['role_id']
            if role_id not in role_permissions:
                role_permissions[role_id] = {
                    'role': {
                        'id': row['role_id'],
                        'role_name': row['role_name'],
                        'role_code': row['role_code'],
                        'is_system': row['is_system']
                    },
                    'permissions': []
                }
            
            role_permissions[role_id]['permissions'].append({
                'id': row['permission_id'],
                'permission_name': row['permission_name'],
                'permission_code': row['permission_code'],
                'permission_type': row['permission_type']
            })
        
        # 获取用户特殊权限
        special_permissions_sql = """
        SELECT p.id, p.permission_name, p.permission_code, p.permission_type,
               p.parent_id, p.app_id, p.resource_path, p.description, p.sort_order, p.status,
               p.created_at, p.updated_at, p.created_by, p.updated_by
        FROM permissions p
        INNER JOIN user_permissions up ON p.id = up.permission_id
        WHERE up.user_id = %s AND up.status = 1 AND p.status = 1
        """
        
        special_permissions_result = await connection.execute_query(special_permissions_sql, [user_id])
        special_permissions_data = UserManagementService.extract_query_result(special_permissions_result)
        special_permissions = [PermissionSchema(**permission) for permission in special_permissions_data]
        
        return UserPermissionDetailResponse(
            user=user,
            role_permissions=list(role_permissions.values()),
            special_permissions=special_permissions,
            applications=user.applications
        )

    @staticmethod
    async def create_user(user_data: UserCreateRequest) -> UserSchema:
        """创建新用户"""
        connection = connections.get("default")

        try:
            # 验证密码强度
            is_valid, error_msg = password_service.validate_password_strength(user_data.password)
            if not is_valid:
                raise ValueError(f"密码不符合要求: {error_msg}")

            # 加密密码
            encrypted_password, salt = password_service.hash_password(user_data.password)

            # 检查用户名是否已存在
            existing_user_sql = "SELECT COUNT(*) as count FROM users WHERE username = %s OR member_id = %s"
            existing_result = await connection.execute_query(existing_user_sql, [user_data.username, user_data.member_id])
            existing_data = UserManagementService.extract_query_result(existing_result)
            if existing_data and existing_data[0]['count'] > 0:
                raise ValueError("用户名或member_id已存在")

            # 插入用户数据
            insert_sql = """
            INSERT INTO users (
                username, member_id, work_id, email, full_name, department,
                phone_number, password, password_salt, status, created_at, updated_at
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, NOW(), NOW())
            """

            await connection.execute_query(insert_sql, [
                user_data.username,
                user_data.member_id,
                user_data.work_id,
                user_data.email,
                user_data.full_name,
                user_data.department,
                user_data.phone,
                encrypted_password,
                salt,
                True  # 默认启用状态
            ])

            # 获取新创建的用户ID
            user_id_sql = "SELECT LAST_INSERT_ID() as user_id"
            user_id_result = await connection.execute_query(user_id_sql)
            user_id_data = UserManagementService.extract_query_result(user_id_result)
            user_id = user_id_data[0]['user_id']

            # 分配角色
            if user_data.role_ids:
                for role_id in user_data.role_ids:
                    role_sql = """
                    INSERT INTO user_roles (user_id, role_id, status, created_at, updated_at)
                    VALUES (%s, %s, 1, NOW(), NOW())
                    """
                    await connection.execute_query(role_sql, [user_id, role_id])

            # 分配应用权限
            if user_data.app_ids:
                for app_id in user_data.app_ids:
                    app_sql = """
                    INSERT INTO user_app_permissions (user_id, app_id, status, created_at, updated_at)
                    VALUES (%s, %s, 1, NOW(), NOW())
                    """
                    await connection.execute_query(app_sql, [user_id, app_id])

            # 返回创建的用户信息
            return await UserManagementService.get_user_by_id(user_id)

        except Exception as e:
            raise Exception(f"创建用户失败: {str(e)}")

    @staticmethod
    async def update_user(user_id: int, user_data: UserUpdateRequest) -> UserSchema:
        """更新用户信息"""
        connection = connections.get("default")

        try:
            # 构建更新字段
            update_fields = []
            params = []

            if user_data.username is not None:
                update_fields.append("username = %s")
                params.append(user_data.username)

            if user_data.member_id is not None:
                update_fields.append("member_id = %s")
                params.append(user_data.member_id)

            if user_data.work_id is not None:
                update_fields.append("work_id = %s")
                params.append(user_data.work_id)

            if user_data.email is not None:
                update_fields.append("email = %s")
                params.append(user_data.email)

            if user_data.full_name is not None:
                update_fields.append("full_name = %s")
                params.append(user_data.full_name)

            if user_data.department is not None:
                update_fields.append("department = %s")
                params.append(user_data.department)

            if user_data.phone is not None:
                update_fields.append("phone_number = %s")
                params.append(user_data.phone)

            if user_data.status is not None:
                update_fields.append("status = %s")
                params.append(user_data.status)

            # 处理密码更新
            if user_data.password is not None:
                # 验证密码强度
                is_valid, error_msg = password_service.validate_password_strength(user_data.password)
                if not is_valid:
                    raise ValueError(f"密码不符合要求: {error_msg}")

                # 加密新密码
                encrypted_password, salt = password_service.hash_password(user_data.password)
                update_fields.append("password = %s")
                update_fields.append("password_salt = %s")
                params.extend([encrypted_password, salt])

            if update_fields:
                update_fields.append("updated_at = NOW()")
                params.append(user_id)

                update_sql = f"""
                UPDATE users
                SET {', '.join(update_fields)}
                WHERE user_id = %s
                """
                await connection.execute_query(update_sql, params)

            # 更新角色分配
            if user_data.role_ids is not None:
                # 删除现有角色
                await connection.execute_query("DELETE FROM user_roles WHERE user_id = %s", [user_id])

                # 添加新角色
                for role_id in user_data.role_ids:
                    role_sql = """
                    INSERT INTO user_roles (user_id, role_id, status, created_at, updated_at)
                    VALUES (%s, %s, 1, NOW(), NOW())
                    """
                    await connection.execute_query(role_sql, [user_id, role_id])

            # 更新应用权限
            if user_data.app_ids is not None:
                # 删除现有应用权限
                await connection.execute_query("DELETE FROM user_app_permissions WHERE user_id = %s", [user_id])

                # 添加新应用权限
                for app_id in user_data.app_ids:
                    app_sql = """
                    INSERT INTO user_app_permissions (user_id, app_id, status, created_at, updated_at)
                    VALUES (%s, %s, 1, NOW(), NOW())
                    """
                    await connection.execute_query(app_sql, [user_id, app_id])

            # 返回更新后的用户信息
            return await UserManagementService.get_user_by_id(user_id)

        except Exception as e:
            raise Exception(f"更新用户失败: {str(e)}")

    @staticmethod
    async def change_password(user_id: int, password_data: ChangePasswordRequest) -> bool:
        """用户修改密码"""
        connection = connections.get("default")

        try:
            # 获取用户当前密码和盐值
            password_sql = """
            SELECT password, password_salt
            FROM users
            WHERE user_id = %s AND status = 1
            """
            password_result = await connection.execute_query(password_sql, [user_id])
            password_data_list = UserManagementService.extract_query_result(password_result)

            if not password_data_list:
                raise ValueError("用户不存在或已被禁用")

            current_password = password_data_list[0]['password']
            current_salt = password_data_list[0]['password_salt']

            # 验证旧密码
            if not password_service.verify_password(password_data.old_password, current_password, current_salt):
                raise ValueError("旧密码不正确")

            # 验证新密码强度
            is_valid, error_msg = password_service.validate_password_strength(password_data.new_password)
            if not is_valid:
                raise ValueError(f"新密码不符合要求: {error_msg}")

            # 加密新密码
            encrypted_password, salt = password_service.hash_password(password_data.new_password)

            # 更新密码
            update_sql = """
            UPDATE users
            SET password = %s, password_salt = %s, updated_at = NOW()
            WHERE user_id = %s
            """
            await connection.execute_query(update_sql, [encrypted_password, salt, user_id])

            return True

        except Exception as e:
            raise Exception(f"修改密码失败: {str(e)}")

    @staticmethod
    async def reset_password(user_id: int, password_data: ResetPasswordRequest) -> bool:
        """管理员重置用户密码"""
        connection = connections.get("default")

        try:
            # 验证新密码强度
            is_valid, error_msg = password_service.validate_password_strength(password_data.new_password)
            if not is_valid:
                raise ValueError(f"新密码不符合要求: {error_msg}")

            # 加密新密码
            encrypted_password, salt = password_service.hash_password(password_data.new_password)

            # 更新密码
            update_sql = """
            UPDATE users
            SET password = %s, password_salt = %s, updated_at = NOW()
            WHERE user_id = %s
            """
            await connection.execute_query(update_sql, [encrypted_password, salt, user_id])

            return True

        except Exception as e:
            raise Exception(f"重置密码失败: {str(e)}")

    @staticmethod
    async def verify_user_password(username: str, password: str) -> Optional[UserSchema]:
        """验证用户密码（用于登录）"""
        connection = connections.get("default")

        try:
            # 获取用户信息和密码
            user_sql = """
            SELECT
                u.user_id as id, u.username, u.member_id, u.work_id, u.email,
                u.full_name, u.department, u.phone_number as phone, u.sso_user_id,
                u.last_login_at, u.login_count, u.status, u.created_at, u.updated_at,
                u.created_by, u.updated_by, u.password, u.password_salt
            FROM users u
            WHERE (u.username = %s OR u.member_id = %s) AND u.status = 1
            """
            user_result = await connection.execute_query(user_sql, [username, username])
            user_data_list = UserManagementService.extract_query_result(user_result)

            if not user_data_list:
                return None

            user_data = user_data_list[0]
            stored_password = user_data['password']
            salt = user_data['password_salt']

            # 验证密码
            if not password_service.verify_password(password, stored_password, salt):
                return None

            # 更新登录统计
            update_login_sql = """
            UPDATE users
            SET last_login_at = NOW(), login_count = login_count + 1
            WHERE user_id = %s
            """
            await connection.execute_query(update_login_sql, [user_data['id']])

            # 移除密码字段，返回用户信息
            user_data.pop('password', None)
            user_data.pop('password_salt', None)

            # 获取用户角色和应用信息
            user = UserSchema(**user_data, roles=[], applications=[])
            return await UserManagementService.get_user_by_id(user.id)

        except Exception as e:
            raise Exception(f"验证用户密码失败: {str(e)}")

    @staticmethod
    async def update_user_status(user_id: int, status: bool) -> UserSchema:
        """更新用户状态（启用/停用）"""
        connection = connections.get("default")

        try:
            # 检查用户是否存在
            check_sql = "SELECT COUNT(*) as count FROM users WHERE user_id = %s"
            check_result = await connection.execute_query(check_sql, [user_id])
            check_data = UserManagementService.extract_query_result(check_result)

            if not check_data or check_data[0]['count'] == 0:
                raise ValueError("用户不存在")

            # 更新用户状态
            update_sql = """
            UPDATE users
            SET status = %s, updated_at = NOW()
            WHERE user_id = %s
            """
            await connection.execute_query(update_sql, [status, user_id])

            # 返回更新后的用户信息（包含非活跃用户）
            return await UserManagementService.get_user_by_id(user_id, include_inactive=True)

        except Exception as e:
            raise Exception(f"更新用户状态失败: {str(e)}")

    @staticmethod
    async def update_role(role_id: int, role_data: RoleUpdateRequest) -> RoleSchema:
        """更新角色信息"""
        connection = connections.get("default")

        try:
            # 检查角色是否存在
            check_sql = "SELECT COUNT(*) as count FROM roles WHERE id = %s"
            check_result = await connection.execute_query(check_sql, [role_id])
            check_data = UserManagementService.extract_query_result(check_result)

            if not check_data or check_data[0]['count'] == 0:
                raise ValueError("角色不存在")

            # 检查是否为系统角色
            system_check_sql = "SELECT is_system FROM roles WHERE id = %s"
            system_result = await connection.execute_query(system_check_sql, [role_id])
            system_data = UserManagementService.extract_query_result(system_result)

            if system_data and system_data[0]['is_system']:
                raise ValueError("系统角色不允许修改")

            # 更新角色信息
            update_fields = []
            params = []

            if role_data.role_name is not None:
                update_fields.append("role_name = %s")
                params.append(role_data.role_name)

            if role_data.role_code is not None:
                update_fields.append("role_code = %s")
                params.append(role_data.role_code)

            if role_data.description is not None:
                update_fields.append("description = %s")
                params.append(role_data.description)

            if not update_fields:
                raise ValueError("没有提供要更新的字段")

            update_fields.append("updated_at = NOW()")
            params.append(role_id)

            update_sql = f"""
            UPDATE roles
            SET {', '.join(update_fields)}
            WHERE id = %s
            """

            await connection.execute_query(update_sql, params)

            # 返回更新后的角色信息
            return await UserManagementService.get_role_by_id(role_id)

        except Exception as e:
            raise Exception(f"更新角色信息失败: {str(e)}")

    @staticmethod
    async def get_role_by_id(role_id: int) -> RoleSchema:
        """根据ID获取角色详情"""
        connection = connections.get("default")

        role_sql = """
        SELECT
            r.id,
            r.role_name,
            r.role_code,
            r.description,
            r.is_system,
            r.status,
            r.created_at,
            r.updated_at,
            r.created_by,
            r.updated_by
        FROM roles r
        WHERE r.id = %s
        """

        result = await connection.execute_query(role_sql, [role_id])
        data = UserManagementService.extract_query_result(result)

        if not data:
            raise ValueError("角色不存在")

        role_data = data[0]

        return RoleSchema(
            id=role_data['id'],
            role_name=role_data['role_name'],
            role_code=role_data['role_code'],
            description=role_data['description'],
            is_system=bool(role_data['is_system']),
            status=bool(role_data['status']),
            created_at=role_data['created_at'],
            updated_at=role_data['updated_at'],
            created_by=role_data['created_by'],
            updated_by=role_data['updated_by']
        )
