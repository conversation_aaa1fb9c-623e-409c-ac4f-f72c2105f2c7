"""
用户管理服务
"""
from typing import List, Optional, Dict, Any
from datetime import datetime
from tortoise import connections
from tortoise.exceptions import DoesNotExist, IntegrityError

from .schemas import (
    UserSchema, RoleSchema, PermissionSchema, ApplicationSchema,
    UserListResponse, RoleListResponse, PermissionListResponse, ApplicationListResponse,
    UserCreateRequest, UserUpdateRequest, RoleCreateRequest, RoleUpdateRequest,
    PermissionCreateRequest, PermissionUpdateRequest, UserPermissionAssignRequest,
    UserPermissionDetailResponse, ChangePasswordRequest, ResetPasswordRequest,
    UpdateUserStatusRequest
)
from .password_service import password_service


class UserManagementService:
    """用户管理服务类"""

    @staticmethod
    def extract_query_result(result):
        """提取查询结果，处理 execute_query 返回的元组格式"""
        if isinstance(result, tuple) and len(result) > 1:
            return result[1]  # 返回实际的结果列表
        return result if isinstance(result, list) else []

    @staticmethod
    async def get_users(page: int = 1, page_size: int = 20, search: str = None) -> UserListResponse:
        """获取用户列表"""
        connection = connections.get("default")
        
        # 构建查询条件
        where_conditions = []  # 查询所有用户（包括已停用的）
        params = []
        
        if search:
            where_conditions.append("(u.username LIKE %s OR u.member_id LIKE %s OR u.work_id LIKE %s OR u.email LIKE %s OR u.full_name LIKE %s)")
            search_param = f"%{search}%"
            params.extend([search_param, search_param, search_param, search_param, search_param])
        
        where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"
        
        # 查询总数
        count_sql = f"""
        SELECT COUNT(*) as total
        FROM users u
        WHERE {where_clause}
        """
        
        count_result = await connection.execute_query(count_sql, params)
        result_list = UserManagementService.extract_query_result(count_result)
        total = result_list[0]['total'] if result_list else 0
        
        # 查询用户列表
        offset = (page - 1) * page_size
        
        users_sql = f"""
        SELECT
            u.user_id as id,
            u.username,
            u.member_id,
            u.work_id,
            u.email,
            u.full_name,
            u.department,
            u.phone_number as phone,
            u.sso_user_id,
            u.last_login_at,
            u.login_count,
            u.status,
            u.created_at,
            u.updated_at,
            u.created_by,
            u.updated_by
        FROM users u
        WHERE {where_clause}
        ORDER BY u.created_at DESC
        LIMIT %s OFFSET %s
        """
        
        params.extend([page_size, offset])
        users_result = await connection.execute_query(users_sql, params)
        users_data = UserManagementService.extract_query_result(users_result)

        # 转换为用户对象
        users = []
        for row in users_data:
            # 获取用户角色
            roles_sql = """
            SELECT r.id, r.role_name, r.role_code, r.description, r.is_system, r.status,
                   r.created_at, r.updated_at, r.created_by, r.updated_by
            FROM roles r
            INNER JOIN user_roles ur ON r.id = ur.role_id
            WHERE ur.user_id = %s AND ur.status = 1 AND r.status = 1
            """
            roles_result = await connection.execute_query(roles_sql, [row['id']])
            roles_data = UserManagementService.extract_query_result(roles_result)
            roles = [RoleSchema(**role) for role in roles_data]
            
            # 获取用户应用权限
            apps_sql = """
            SELECT a.id, a.app_name, a.app_code, a.app_url, a.description, a.icon,
                   a.sort_order, a.status, a.created_at, a.updated_at, a.created_by, a.updated_by
            FROM applications a
            INNER JOIN user_app_permissions uap ON a.id = uap.app_id
            WHERE uap.user_id = %s AND uap.status = 1 AND a.status = 1
            """
            apps_result = await connection.execute_query(apps_sql, [row['id']])
            apps_data = UserManagementService.extract_query_result(apps_result)
            applications = [ApplicationSchema(**app) for app in apps_data]
            
            user_data = dict(row)
            user_data['roles'] = roles
            user_data['applications'] = applications
            users.append(UserSchema(**user_data))
        
        pages = (total + page_size - 1) // page_size
        
        return UserListResponse(
            items=users,
            total=total,
            page=page,
            page_size=page_size,
            pages=pages
        )

    @staticmethod
    async def get_user_by_id(user_id: int, include_inactive: bool = False) -> Optional[UserSchema]:
        """根据ID获取用户"""
        connection = connections.get("default")

        # 根据参数决定是否包含非活跃用户
        status_condition = "" if include_inactive else "AND u.status = 1"

        user_sql = f"""
        SELECT
            u.user_id as id,
            u.username,
            u.member_id,
            u.work_id,
            u.email,
            u.full_name,
            u.department,
            u.phone_number as phone,
            u.sso_user_id,
            u.last_login_at,
            u.login_count,
            u.status,
            u.created_at,
            u.updated_at,
            u.created_by,
            u.updated_by
        FROM users u
        WHERE u.user_id = %s {status_condition}
        """
        
        user_result = await connection.execute_query(user_sql, [user_id])
        user_data_list = UserManagementService.extract_query_result(user_result)
        if not user_data_list:
            return None

        user_data = dict(user_data_list[0])
        
        # 获取用户角色
        roles_sql = """
        SELECT r.id, r.role_name, r.role_code, r.description, r.is_system, r.status,
               r.created_at, r.updated_at, r.created_by, r.updated_by
        FROM roles r
        INNER JOIN user_roles ur ON r.id = ur.role_id
        WHERE ur.user_id = %s AND ur.status = 1 AND r.status = 1
        """
        roles_result = await connection.execute_query(roles_sql, [user_id])
        roles_data = UserManagementService.extract_query_result(roles_result)
        roles = [RoleSchema(**role) for role in roles_data]
        
        # 获取用户应用权限
        apps_sql = """
        SELECT a.id, a.app_name, a.app_code, a.app_url, a.description, a.icon,
               a.sort_order, a.status, a.created_at, a.updated_at, a.created_by, a.updated_by
        FROM applications a
        INNER JOIN user_app_permissions uap ON a.id = uap.app_id
        WHERE uap.user_id = %s AND uap.status = 1 AND a.status = 1
        """
        apps_result = await connection.execute_query(apps_sql, [user_id])
        apps_data = UserManagementService.extract_query_result(apps_result)
        applications = [ApplicationSchema(**app) for app in apps_data]
        
        user_data['roles'] = roles
        user_data['applications'] = applications
        
        return UserSchema(**user_data)

    @staticmethod
    async def get_roles(page: int = 1, page_size: int = 20, search: str = None) -> RoleListResponse:
        """获取角色列表"""
        connection = connections.get("default")
        
        # 构建查询条件
        where_conditions = ["r.status = 1"]
        params = []
        
        if search:
            where_conditions.append("r.role_name LIKE %s")
            params.append(f"%{search}%")
        
        where_clause = " AND ".join(where_conditions)
        
        # 查询总数
        count_sql = f"""
        SELECT COUNT(*) as total
        FROM roles r
        WHERE {where_clause}
        """
        
        count_result = await connection.execute_query(count_sql, params)
        count_data = UserManagementService.extract_query_result(count_result)
        total = count_data[0]['total'] if count_data else 0
        
        # 查询角色列表
        offset = (page - 1) * page_size
        
        roles_sql = f"""
        SELECT r.id, r.role_name, r.role_code, r.description, r.is_system, r.status,
               r.created_at, r.updated_at, r.created_by, r.updated_by
        FROM roles r
        WHERE {where_clause}
        ORDER BY r.created_at DESC
        LIMIT %s OFFSET %s
        """
        
        params.extend([page_size, offset])
        roles_result = await connection.execute_query(roles_sql, params)
        roles_data = UserManagementService.extract_query_result(roles_result)
        roles = [RoleSchema(**role) for role in roles_data]
        pages = (total + page_size - 1) // page_size
        
        return RoleListResponse(
            items=roles,
            total=total,
            page=page,
            page_size=page_size,
            pages=pages
        )

    @staticmethod
    async def get_permissions(page: int = 1, page_size: int = 20, search: str = None, app_id: int = None) -> PermissionListResponse:
        """获取权限列表"""
        connection = connections.get("default")
        
        # 构建查询条件
        where_conditions = ["p.status = 1"]
        params = []
        
        if search:
            where_conditions.append("(p.permission_name LIKE %s OR p.permission_code LIKE %s)")
            search_param = f"%{search}%"
            params.extend([search_param, search_param])
        
        if app_id:
            where_conditions.append("p.app_id = %s")
            params.append(app_id)
        
        where_clause = " AND ".join(where_conditions)
        
        # 查询总数
        count_sql = f"""
        SELECT COUNT(*) as total
        FROM permissions p
        WHERE {where_clause}
        """
        
        count_result = await connection.execute_query(count_sql, params)
        count_data = UserManagementService.extract_query_result(count_result)
        total = count_data[0]['total'] if count_data else 0
        
        # 查询权限列表
        offset = (page - 1) * page_size
        
        permissions_sql = f"""
        SELECT 
            p.id, p.permission_name, p.permission_code, p.permission_type, p.parent_id,
            p.app_id, a.app_name, p.resource_path, p.description, p.sort_order, p.status,
            p.created_at, p.updated_at, p.created_by, p.updated_by
        FROM permissions p
        LEFT JOIN applications a ON p.app_id = a.id
        WHERE {where_clause}
        ORDER BY p.sort_order, p.created_at DESC
        LIMIT %s OFFSET %s
        """
        
        params.extend([page_size, offset])
        permissions_result = await connection.execute_query(permissions_sql, params)
        permissions_data = UserManagementService.extract_query_result(permissions_result)
        permissions = [PermissionSchema(**permission) for permission in permissions_data]
        pages = (total + page_size - 1) // page_size
        
        return PermissionListResponse(
            items=permissions,
            total=total,
            page=page,
            page_size=page_size,
            pages=pages
        )

    @staticmethod
    async def get_applications(page: int = 1, page_size: int = 20, search: str = None) -> ApplicationListResponse:
        """获取应用列表"""
        connection = connections.get("default")
        
        # 构建查询条件
        where_conditions = ["a.status = 1"]
        params = []
        
        if search:
            where_conditions.append("a.app_name LIKE %s")
            params.append(f"%{search}%")
        
        where_clause = " AND ".join(where_conditions)
        
        # 查询总数
        count_sql = f"""
        SELECT COUNT(*) as total
        FROM applications a
        WHERE {where_clause}
        """
        
        count_result = await connection.execute_query(count_sql, params)
        count_data = UserManagementService.extract_query_result(count_result)
        total = count_data[0]['total'] if count_data else 0
        
        # 查询应用列表
        offset = (page - 1) * page_size
        
        apps_sql = f"""
        SELECT a.id, a.app_name, a.app_code, a.app_url, a.description, a.icon,
               a.sort_order, a.status, a.created_at, a.updated_at, a.created_by, a.updated_by
        FROM applications a
        WHERE {where_clause}
        ORDER BY a.sort_order, a.created_at DESC
        LIMIT %s OFFSET %s
        """
        
        params.extend([page_size, offset])
        apps_result = await connection.execute_query(apps_sql, params)
        apps_data = UserManagementService.extract_query_result(apps_result)
        applications = [ApplicationSchema(**app) for app in apps_data]
        pages = (total + page_size - 1) // page_size
        
        return ApplicationListResponse(
            items=applications,
            total=total,
            page=page,
            page_size=page_size,
            pages=pages
        )

    @staticmethod
    async def get_user_permission_details(user_id: int) -> Optional[UserPermissionDetailResponse]:
        """获取用户权限详情"""
        connection = connections.get("default")
        
        # 获取用户基本信息
        user = await UserManagementService.get_user_by_id(user_id)
        if not user:
            return None
        
        # 获取用户角色权限
        role_permissions_sql = """
        SELECT 
            r.id as role_id, r.role_name, r.role_code, r.is_system,
            p.id as permission_id, p.permission_name, p.permission_code, p.permission_type
        FROM roles r
        INNER JOIN user_roles ur ON r.id = ur.role_id
        INNER JOIN role_permissions rp ON r.id = rp.role_id
        INNER JOIN permissions p ON rp.permission_id = p.id
        WHERE ur.user_id = %s AND ur.status = 1 AND r.status = 1 AND rp.status = 1 AND p.status = 1
        ORDER BY r.role_name, p.sort_order
        """
        
        role_permissions_result = await connection.execute_query(role_permissions_sql, [user_id])
        role_permissions_data = UserManagementService.extract_query_result(role_permissions_result)

        # 组织角色权限数据
        role_permissions = {}
        for row in role_permissions_data:
            role_id = row['role_id']
            if role_id not in role_permissions:
                role_permissions[role_id] = {
                    'role': {
                        'id': row['role_id'],
                        'role_name': row['role_name'],
                        'role_code': row['role_code'],
                        'is_system': row['is_system']
                    },
                    'permissions': []
                }
            
            role_permissions[role_id]['permissions'].append({
                'id': row['permission_id'],
                'permission_name': row['permission_name'],
                'permission_code': row['permission_code'],
                'permission_type': row['permission_type']
            })
        
        # 获取用户特殊权限
        special_permissions_sql = """
        SELECT p.id, p.permission_name, p.permission_code, p.permission_type,
               p.parent_id, p.app_id, p.resource_path, p.description, p.sort_order, p.status,
               p.created_at, p.updated_at, p.created_by, p.updated_by
        FROM permissions p
        INNER JOIN user_permissions up ON p.id = up.permission_id
        WHERE up.user_id = %s AND up.status = 1 AND p.status = 1
        """
        
        special_permissions_result = await connection.execute_query(special_permissions_sql, [user_id])
        special_permissions_data = UserManagementService.extract_query_result(special_permissions_result)
        special_permissions = [PermissionSchema(**permission) for permission in special_permissions_data]
        
        return UserPermissionDetailResponse(
            user=user,
            role_permissions=list(role_permissions.values()),
            special_permissions=special_permissions,
            applications=user.applications
        )

    @staticmethod
    async def create_user(user_data: UserCreateRequest) -> UserSchema:
        """创建新用户"""
        connection = connections.get("default")

        try:
            # 验证密码强度
            is_valid, error_msg = password_service.validate_password_strength(user_data.password)
            if not is_valid:
                raise ValueError(f"密码不符合要求: {error_msg}")

            # 加密密码
            encrypted_password, salt = password_service.hash_password(user_data.password)

            # 检查用户名是否已存在
            existing_user_sql = "SELECT COUNT(*) as count FROM users WHERE username = %s OR member_id = %s"
            existing_result = await connection.execute_query(existing_user_sql, [user_data.username, user_data.member_id])
            existing_data = UserManagementService.extract_query_result(existing_result)
            if existing_data and existing_data[0]['count'] > 0:
                raise ValueError("用户名或member_id已存在")

            # 插入用户数据
            insert_sql = """
            INSERT INTO users (
                username, member_id, work_id, email, full_name, department,
                phone_number, password, password_salt, status, created_at, updated_at
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, NOW(), NOW())
            """

            await connection.execute_query(insert_sql, [
                user_data.username,
                user_data.member_id,
                user_data.work_id,
                user_data.email,
                user_data.full_name,
                user_data.department,
                user_data.phone,
                encrypted_password,
                salt,
                True  # 默认启用状态
            ])

            # 获取新创建的用户ID
            user_id_sql = "SELECT LAST_INSERT_ID() as user_id"
            user_id_result = await connection.execute_query(user_id_sql)
            user_id_data = UserManagementService.extract_query_result(user_id_result)
            user_id = user_id_data[0]['user_id']

            # 分配角色
            if user_data.role_ids:
                for role_id in user_data.role_ids:
                    role_sql = """
                    INSERT INTO user_roles (user_id, role_id, status, created_at, updated_at)
                    VALUES (%s, %s, 1, NOW(), NOW())
                    """
                    await connection.execute_query(role_sql, [user_id, role_id])

            # 分配应用权限
            if user_data.app_ids:
                for app_id in user_data.app_ids:
                    app_sql = """
                    INSERT INTO user_app_permissions (user_id, app_id, status, created_at, updated_at)
                    VALUES (%s, %s, 1, NOW(), NOW())
                    """
                    await connection.execute_query(app_sql, [user_id, app_id])

            # 返回创建的用户信息
            return await UserManagementService.get_user_by_id(user_id)

        except Exception as e:
            raise Exception(f"创建用户失败: {str(e)}")

    @staticmethod
    async def update_user(user_id: int, user_data: UserUpdateRequest) -> UserSchema:
        """更新用户信息"""
        connection = connections.get("default")

        try:
            # 构建更新字段
            update_fields = []
            params = []

            if user_data.username is not None:
                update_fields.append("username = %s")
                params.append(user_data.username)

            if user_data.member_id is not None:
                update_fields.append("member_id = %s")
                params.append(user_data.member_id)

            if user_data.work_id is not None:
                update_fields.append("work_id = %s")
                params.append(user_data.work_id)

            if user_data.email is not None:
                update_fields.append("email = %s")
                params.append(user_data.email)

            if user_data.full_name is not None:
                update_fields.append("full_name = %s")
                params.append(user_data.full_name)

            if user_data.department is not None:
                update_fields.append("department = %s")
                params.append(user_data.department)

            if user_data.phone is not None:
                update_fields.append("phone_number = %s")
                params.append(user_data.phone)

            if user_data.status is not None:
                update_fields.append("status = %s")
                params.append(user_data.status)

            # 处理密码更新
            if user_data.password is not None:
                # 验证密码强度
                is_valid, error_msg = password_service.validate_password_strength(user_data.password)
                if not is_valid:
                    raise ValueError(f"密码不符合要求: {error_msg}")

                # 加密新密码
                encrypted_password, salt = password_service.hash_password(user_data.password)
                update_fields.append("password = %s")
                update_fields.append("password_salt = %s")
                params.extend([encrypted_password, salt])

            if update_fields:
                update_fields.append("updated_at = NOW()")
                params.append(user_id)

                update_sql = f"""
                UPDATE users
                SET {', '.join(update_fields)}
                WHERE user_id = %s
                """
                await connection.execute_query(update_sql, params)

            # 更新角色分配
            if user_data.role_ids is not None:
                # 删除现有角色
                await connection.execute_query("DELETE FROM user_roles WHERE user_id = %s", [user_id])

                # 添加新角色
                for role_id in user_data.role_ids:
                    role_sql = """
                    INSERT INTO user_roles (user_id, role_id, status, created_at, updated_at)
                    VALUES (%s, %s, 1, NOW(), NOW())
                    """
                    await connection.execute_query(role_sql, [user_id, role_id])

            # 更新应用权限
            if user_data.app_ids is not None:
                # 删除现有应用权限
                await connection.execute_query("DELETE FROM user_app_permissions WHERE user_id = %s", [user_id])

                # 添加新应用权限
                for app_id in user_data.app_ids:
                    app_sql = """
                    INSERT INTO user_app_permissions (user_id, app_id, status, created_at, updated_at)
                    VALUES (%s, %s, 1, NOW(), NOW())
                    """
                    await connection.execute_query(app_sql, [user_id, app_id])

            # 返回更新后的用户信息
            return await UserManagementService.get_user_by_id(user_id)

        except Exception as e:
            raise Exception(f"更新用户失败: {str(e)}")

    @staticmethod
    async def change_password(user_id: int, password_data: ChangePasswordRequest) -> bool:
        """用户修改密码"""
        connection = connections.get("default")

        try:
            # 获取用户当前密码和盐值
            password_sql = """
            SELECT password, password_salt
            FROM users
            WHERE user_id = %s AND status = 1
            """
            password_result = await connection.execute_query(password_sql, [user_id])
            password_data_list = UserManagementService.extract_query_result(password_result)

            if not password_data_list:
                raise ValueError("用户不存在或已被禁用")

            current_password = password_data_list[0]['password']
            current_salt = password_data_list[0]['password_salt']

            # 验证旧密码
            if not password_service.verify_password(password_data.old_password, current_password, current_salt):
                raise ValueError("旧密码不正确")

            # 验证新密码强度
            is_valid, error_msg = password_service.validate_password_strength(password_data.new_password)
            if not is_valid:
                raise ValueError(f"新密码不符合要求: {error_msg}")

            # 加密新密码
            encrypted_password, salt = password_service.hash_password(password_data.new_password)

            # 更新密码
            update_sql = """
            UPDATE users
            SET password = %s, password_salt = %s, updated_at = NOW()
            WHERE user_id = %s
            """
            await connection.execute_query(update_sql, [encrypted_password, salt, user_id])

            return True

        except Exception as e:
            raise Exception(f"修改密码失败: {str(e)}")

    @staticmethod
    async def reset_password(user_id: int, password_data: ResetPasswordRequest) -> bool:
        """管理员重置用户密码"""
        connection = connections.get("default")

        try:
            # 验证新密码强度
            is_valid, error_msg = password_service.validate_password_strength(password_data.new_password)
            if not is_valid:
                raise ValueError(f"新密码不符合要求: {error_msg}")

            # 加密新密码
            encrypted_password, salt = password_service.hash_password(password_data.new_password)

            # 更新密码
            update_sql = """
            UPDATE users
            SET password = %s, password_salt = %s, updated_at = NOW()
            WHERE user_id = %s
            """
            await connection.execute_query(update_sql, [encrypted_password, salt, user_id])

            return True

        except Exception as e:
            raise Exception(f"重置密码失败: {str(e)}")

    @staticmethod
    async def verify_user_password(username: str, password: str) -> Optional[UserSchema]:
        """验证用户密码（用于登录）"""
        connection = connections.get("default")

        try:
            # 获取用户信息和密码
            user_sql = """
            SELECT
                u.user_id as id, u.username, u.member_id, u.work_id, u.email,
                u.full_name, u.department, u.phone_number as phone, u.sso_user_id,
                u.last_login_at, u.login_count, u.status, u.created_at, u.updated_at,
                u.created_by, u.updated_by, u.password, u.password_salt
            FROM users u
            WHERE (u.username = %s OR u.member_id = %s) AND u.status = 1
            """
            user_result = await connection.execute_query(user_sql, [username, username])
            user_data_list = UserManagementService.extract_query_result(user_result)

            if not user_data_list:
                return None

            user_data = user_data_list[0]
            stored_password = user_data['password']
            salt = user_data['password_salt']

            # 验证密码
            if not password_service.verify_password(password, stored_password, salt):
                return None

            # 更新登录统计
            update_login_sql = """
            UPDATE users
            SET last_login_at = NOW(), login_count = login_count + 1
            WHERE user_id = %s
            """
            await connection.execute_query(update_login_sql, [user_data['id']])

            # 移除密码字段，返回用户信息
            user_data.pop('password', None)
            user_data.pop('password_salt', None)

            # 获取用户角色和应用信息
            user = UserSchema(**user_data, roles=[], applications=[])
            return await UserManagementService.get_user_by_id(user.id)

        except Exception as e:
            raise Exception(f"验证用户密码失败: {str(e)}")

    @staticmethod
    async def update_user_status(user_id: int, status: bool) -> UserSchema:
        """更新用户状态（启用/停用）"""
        connection = connections.get("default")

        try:
            # 检查用户是否存在
            check_sql = "SELECT COUNT(*) as count FROM users WHERE user_id = %s"
            check_result = await connection.execute_query(check_sql, [user_id])
            check_data = UserManagementService.extract_query_result(check_result)

            if not check_data or check_data[0]['count'] == 0:
                raise ValueError("用户不存在")

            # 更新用户状态
            update_sql = """
            UPDATE users
            SET status = %s, updated_at = NOW()
            WHERE user_id = %s
            """
            await connection.execute_query(update_sql, [status, user_id])

            # 返回更新后的用户信息（包含非活跃用户）
            return await UserManagementService.get_user_by_id(user_id, include_inactive=True)

        except Exception as e:
            raise Exception(f"更新用户状态失败: {str(e)}")

    @staticmethod
    async def update_role(role_id: int, role_data: RoleUpdateRequest) -> RoleSchema:
        """更新角色信息"""
        connection = connections.get("default")

        try:
            # 检查角色是否存在
            check_sql = "SELECT COUNT(*) as count FROM roles WHERE id = %s"
            check_result = await connection.execute_query(check_sql, [role_id])
            check_data = UserManagementService.extract_query_result(check_result)

            if not check_data or check_data[0]['count'] == 0:
                raise ValueError("角色不存在")

            # 检查是否为系统角色
            system_check_sql = "SELECT is_system FROM roles WHERE id = %s"
            system_result = await connection.execute_query(system_check_sql, [role_id])
            system_data = UserManagementService.extract_query_result(system_result)

            if system_data and system_data[0]['is_system']:
                raise ValueError("系统角色不允许修改")

            # 更新角色信息
            update_fields = []
            params = []

            if role_data.role_name is not None:
                update_fields.append("role_name = %s")
                params.append(role_data.role_name)

            if role_data.role_code is not None:
                update_fields.append("role_code = %s")
                params.append(role_data.role_code)

            if role_data.description is not None:
                update_fields.append("description = %s")
                params.append(role_data.description)

            if not update_fields:
                raise ValueError("没有提供要更新的字段")

            update_fields.append("updated_at = NOW()")
            params.append(role_id)

            update_sql = f"""
            UPDATE roles
            SET {', '.join(update_fields)}
            WHERE id = %s
            """

            await connection.execute_query(update_sql, params)

            # 返回更新后的角色信息
            return await UserManagementService.get_role_by_id(role_id)

        except Exception as e:
            raise Exception(f"更新角色信息失败: {str(e)}")

    @staticmethod
    async def get_role_by_id(role_id: int) -> RoleSchema:
        """根据ID获取角色详情"""
        connection = connections.get("default")

        role_sql = """
        SELECT
            r.id,
            r.role_name,
            r.role_code,
            r.description,
            r.is_system,
            r.status,
            r.created_at,
            r.updated_at,
            r.created_by,
            r.updated_by
        FROM roles r
        WHERE r.id = %s
        """

        result = await connection.execute_query(role_sql, [role_id])
        data = UserManagementService.extract_query_result(result)

        if not data:
            raise ValueError("角色不存在")

        role_data = data[0]

        return RoleSchema(
            id=role_data['id'],
            role_name=role_data['role_name'],
            role_code=role_data['role_code'],
            description=role_data['description'],
            is_system=bool(role_data['is_system']),
            status=bool(role_data['status']),
            created_at=role_data['created_at'],
            updated_at=role_data['updated_at'],
            created_by=role_data['created_by'],
            updated_by=role_data['updated_by']
        )
