"""酒店订单API的数据模式定义"""

from pydantic import BaseModel, ConfigDict, Field, field_serializer, model_validator
from typing import Optional, List, Any
from datetime import date, time, datetime, timedelta
from decimal import Decimal


class ValidationError(BaseModel):
    """数据验证错误"""
    row: int = Field(..., description="行号")
    message: str = Field(..., description="错误消息")
    field: Optional[str] = Field(default="", description="字段名")
    error_type: Optional[str] = Field(default="validation_error", description="错误类型")
    value: Optional[str] = Field(default="", description="错误值")


class ExcelUploadResponse(BaseModel):
    """Excel上传响应"""
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="处理结果消息")
    validation_errors: List[ValidationError] = Field(default=[], description="验证错误列表")


class ExcelValidationResponse(BaseModel):
    """Excel验证响应"""
    total_rows: int = Field(..., description="总行数")
    valid_rows: int = Field(..., description="有效行数")
    error_rows: int = Field(..., description="错误行数")
    errors: List[ValidationError] = Field(default=[], description="验证错误列表")
    should_continue: bool = Field(..., description="是否可以继续导入")
    duplicate_rows: List[int] = Field(default=[], description="重复数据的行号列表")


class HotelOrderBase(BaseModel):
    """酒店订单基础数据模式"""
    project_id: int
    sequence_number: int
    
    # 入住人基础信息
    guest_full_name: str
    guest_surname: Optional[str] = None
    guest_given_name: Optional[str] = None
    guest_nationality: Optional[str] = None
    guest_gender: Optional[str] = None
    guest_birth_date: Optional[str] = None
    guest_id_type: Optional[str] = None
    guest_id_number: Optional[str] = None
    guest_id_expiry_date: Optional[str] = None
    guest_mobile_country_code: Optional[str] = Field(default="+86", description="入住人手机号国际区号")
    guest_mobile_phone: Optional[str] = None
    guest_email: Optional[str] = None
    
    # 酒店预订信息
    destination: Optional[str] = None
    hotel_id: Optional[str] = None
    room_type: Optional[str] = None
    room_count: Optional[int] = Field(default=1, description="预订房间数量")
    policy_name: Optional[str] = None
    include_breakfast: Optional[str] = Field(default="否", description="是否含早")
    is_half_day_room: Optional[str] = Field(default="否", description="是否为半日房")
    check_in_time: Optional[str] = None
    check_out_time: Optional[str] = None
    is_group_booking: Optional[str] = Field(default="否", description="是否为团房")
    group_booking_name: Optional[str] = None
    room_number: Optional[str] = None
    
    # 支付和财务信息
    payment_method: Optional[str] = None
    invoice_type: Optional[str] = None
    tax_rate: Optional[str] = Field(default="0", description="税率")
    agreement_type: Optional[str] = None
    supplier_name: Optional[str] = None
    payment_channel: Optional[str] = None
    payment_transaction_id: Optional[str] = None
    cost_per_room: Optional[str] = Field(default="", description="对供成本（每间房成本）")
    hidden_service_fee: Optional[str] = Field(default="", description="隐藏手续费")
    cancellation_policy: Optional[str] = None
    is_violation: Optional[str] = Field(default="否", description="是否违规")
    
    # 联系人和管理信息
    contact_person: Optional[str] = None
    contact_mobile_country_code: Optional[str] = Field(default="+86", description="联系人手机号国际区号")
    contact_mobile_phone: Optional[str] = None
    order_remarks: Optional[str] = None
    cost_center: Optional[str] = None
    trip_submission_item: Optional[str] = None
    approver: Optional[str] = None
    
    # 同住人信息
    roommate_name: Optional[str] = None
    roommate_surname: Optional[str] = None
    roommate_given_name: Optional[str] = None
    roommate_nationality: Optional[str] = None
    roommate_gender: Optional[str] = None
    roommate_birth_date: Optional[str] = None
    roommate_id_type: Optional[str] = None
    roommate_id_number: Optional[str] = None
    roommate_id_expiry_date: Optional[str] = None
    roommate_mobile_country_code: Optional[str] = Field(default="+86", description="同住人手机号国际区号")
    roommate_mobile_phone: Optional[str] = None
    roommate_email: Optional[str] = None
    
    # 对账单信息
    company_name: Optional[str] = None
    hotel_name: Optional[str] = None
    amount: Optional[str] = Field(default="0", description="对客金额")
    booking_agent: Optional[str] = None
    order_number: Optional[str] = None
    bill_number: Optional[str] = None
    
    # 订单状态
    order_status: Optional[str] = Field(default="initial", description="订单状态")
    fail_reason: Optional[str] = Field(None, description="失败原因")


class HotelOrderCreate(HotelOrderBase):
    """创建酒店订单的数据模式"""
    pass


class HotelOrderUpdate(BaseModel):
    """更新酒店订单的数据模式"""
    # 入住人基础信息
    guest_full_name: Optional[str] = None
    guest_surname: Optional[str] = None
    guest_given_name: Optional[str] = None
    guest_nationality: Optional[str] = None
    guest_gender: Optional[str] = None
    guest_birth_date: Optional[str] = None
    guest_id_type: Optional[str] = None
    guest_id_number: Optional[str] = None
    guest_id_expiry_date: Optional[str] = None
    guest_mobile_country_code: Optional[str] = None
    guest_mobile_phone: Optional[str] = None
    guest_email: Optional[str] = None
    
    # 酒店预订信息
    destination: Optional[str] = None
    hotel_id: Optional[str] = None
    room_type: Optional[str] = None
    room_count: Optional[int] = None
    policy_name: Optional[str] = None
    include_breakfast: Optional[str] = None
    is_half_day_room: Optional[str] = None
    check_in_time: Optional[str] = None
    check_out_time: Optional[str] = None
    is_group_booking: Optional[str] = None
    group_booking_name: Optional[str] = None
    room_number: Optional[str] = None
    
    # 支付和财务信息
    payment_method: Optional[str] = None
    invoice_type: Optional[str] = None
    tax_rate: Optional[str] = None
    agreement_type: Optional[str] = None
    supplier_name: Optional[str] = None
    payment_channel: Optional[str] = None
    payment_transaction_id: Optional[str] = None
    cost_per_room: Optional[str] = None
    hidden_service_fee: Optional[str] = None
    cancellation_policy: Optional[str] = None
    is_violation: Optional[str] = None
    
    # 联系人和管理信息
    contact_person: Optional[str] = None
    contact_mobile_country_code: Optional[str] = None
    contact_mobile_phone: Optional[str] = None
    order_remarks: Optional[str] = None
    cost_center: Optional[str] = None
    trip_submission_item: Optional[str] = None
    approver: Optional[str] = None
    
    # 同住人信息
    roommate_name: Optional[str] = None
    roommate_surname: Optional[str] = None
    roommate_given_name: Optional[str] = None
    roommate_nationality: Optional[str] = None
    roommate_gender: Optional[str] = None
    roommate_birth_date: Optional[str] = None
    roommate_id_type: Optional[str] = None
    roommate_id_number: Optional[str] = None
    roommate_id_expiry_date: Optional[str] = None
    roommate_mobile_country_code: Optional[str] = None
    roommate_mobile_phone: Optional[str] = None
    roommate_email: Optional[str] = None
    
    # 对账单信息
    company_name: Optional[str] = None
    hotel_name: Optional[str] = None
    amount: Optional[str] = None
    booking_agent: Optional[str] = None
    order_number: Optional[str] = None
    bill_number: Optional[str] = None
    
    # 订单状态
    order_status: Optional[str] = None
    fail_reason: Optional[str] = None


class HotelOrderResponse(BaseModel):
    """酒店订单响应数据模式"""
    id: int
    project_id: int
    sequence_number: int
    
    # 入住人基础信息
    guest_full_name: str
    guest_surname: Optional[str] = None
    guest_given_name: Optional[str] = None
    guest_nationality: Optional[str] = None
    guest_gender: Optional[str] = None
    guest_birth_date: Optional[str] = None
    guest_id_type: Optional[str] = None
    guest_id_number: Optional[str] = None
    guest_id_expiry_date: Optional[str] = None
    guest_mobile_country_code: Optional[str] = None
    guest_mobile_phone: Optional[str] = None
    guest_email: Optional[str] = None
    
    # 酒店预订信息
    destination: Optional[str] = None
    hotel_id: Optional[str] = None
    room_type: Optional[str] = None
    room_count: Optional[int] = None
    policy_name: Optional[str] = None
    include_breakfast: Optional[str] = None
    is_half_day_room: Optional[str] = None
    check_in_time: Optional[str] = None
    check_out_time: Optional[str] = None
    is_group_booking: Optional[str] = None
    group_booking_name: Optional[str] = None
    room_number: Optional[str] = None
    
    # 支付和财务信息
    payment_method: Optional[str] = None
    invoice_type: Optional[str] = None
    tax_rate: Optional[int] = None
    agreement_type: Optional[str] = None
    supplier_name: Optional[str] = None
    payment_channel: Optional[str] = None
    payment_transaction_id: Optional[str] = None
    cost_per_room: Optional[str] = None
    hidden_service_fee: Optional[str] = None
    cancellation_policy: Optional[str] = None
    is_violation: Optional[str] = None
    
    # 联系人和管理信息
    contact_person: Optional[str] = None
    contact_mobile_country_code: Optional[str] = None
    contact_mobile_phone: Optional[str] = None
    order_remarks: Optional[str] = None
    cost_center: Optional[str] = None
    trip_submission_item: Optional[str] = None
    approver: Optional[str] = None
    
    # 同住人信息
    roommate_name: Optional[str] = None
    roommate_surname: Optional[str] = None
    roommate_given_name: Optional[str] = None
    roommate_nationality: Optional[str] = None
    roommate_gender: Optional[str] = None
    roommate_birth_date: Optional[str] = None
    roommate_id_type: Optional[str] = None
    roommate_id_number: Optional[str] = None
    roommate_id_expiry_date: Optional[str] = None
    roommate_mobile_country_code: Optional[str] = None
    roommate_mobile_phone: Optional[str] = None
    roommate_email: Optional[str] = None
    
    # 对账单信息
    company_name: Optional[str] = None
    hotel_name: Optional[str] = None
    amount: Optional[Decimal] = None
    booking_agent: Optional[str] = None
    order_number: Optional[str] = None
    bill_number: Optional[str] = None
    
    # 订单状态和元数据
    order_status: str
    fail_reason: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    is_deleted: bool
    
    model_config = ConfigDict(from_attributes=True)

    @field_serializer('created_at', 'updated_at')
    def serialize_datetime(self, dt: datetime) -> str:
        return dt.isoformat() if dt else None


class HotelOrderListResponse(BaseModel):
    """酒店订单列表响应"""
    total: int
    page: int
    page_size: int
    items: List[HotelOrderResponse]


class BookingRequest(BaseModel):
    """预订请求"""
    booking_type: str = Field(..., description="预订类型: book_only 或 book_and_ticket")
    orders: List[int] = Field(..., description="订单ID列表")
    sms_notify: Optional[bool] = Field(default=False, description="是否短信通知")
    has_agent: Optional[bool] = Field(default=False, description="是否有代订人")
    agent_phone: Optional[str] = Field(None, description="代订人手机号码")


class BookingResponse(BaseModel):
    """预订响应"""
    processed_count: int = Field(..., description="处理的订单数量")
    success_count: int = Field(..., description="成功的订单数量")
    failed_count: int = Field(..., description="失败的订单数量")
    message: str = Field(..., description="处理结果消息")


class ClearOrdersResponse(BaseModel):
    """清空订单响应"""
    deleted_count: int = Field(..., description="删除的订单数量")
    message: str = Field(..., description="处理结果消息")


class CreateBookingTaskRequest(BaseModel):
    """创建预订任务请求"""
    booking_type: str = Field(..., description="预订类型: book_only 或 book_and_ticket")
    task_title: str = Field(..., description="任务标题")
    task_description: Optional[str] = Field(None, description="任务描述")
    sms_notify: bool = Field(default=False, description="是否短信通知")
    has_agent: bool = Field(default=False, description="是否有代订人")
    agent_phone: Optional[str] = Field(None, description="代订人手机号码")
    order_ids: Optional[List[int]] = Field(None, description="指定要预订的订单ID列表，如果为None则预订所有initial状态的订单")


class CreateBookingTaskResponse(BaseModel):
    """创建预订任务响应"""
    task_id: str = Field(..., description="任务ID")
    project_id: int = Field(..., description="项目ID")
    task_title: str = Field(..., description="任务标题")
    submitted_orders_count: int = Field(..., description="提交的订单数量")
    message: str = Field(..., description="处理结果消息")


class ProjectOrderStatsResponse(BaseModel):
    """项目订单统计响应"""
    project_id: int = Field(..., description="项目ID")
    total_orders: int = Field(..., description="总订单数")
    completed_orders: int = Field(..., description="预定完成订单数")
    completed_amount: Decimal = Field(..., description="预定完成订单金额总和")
    
    model_config = ConfigDict(from_attributes=True)

    @field_serializer('completed_amount')
    def serialize_amount(self, amount: Decimal) -> str:
        return str(amount)


class ProjectOrderDetailStatsResponse(BaseModel):
    """项目订单详细统计响应"""
    project_id: int = Field(..., description="项目ID")
    total_orders: int = Field(..., description="总订单数")
    check_failed_orders: int = Field(..., description="验证失败订单数")
    initial_orders: int = Field(..., description="待提交订单数")
    submitted_orders: int = Field(..., description="已提交订单数")
    processing_orders: int = Field(..., description="处理中订单数")
    completed_orders: int = Field(..., description="预定完成订单数")
    failed_orders: int = Field(..., description="预定失败订单数")
    completed_amount: Decimal = Field(..., description="预定完成订单金额总和")
    total_amount: Decimal = Field(..., description="所有订单金额总和")
    
    model_config = ConfigDict(from_attributes=True)

    @field_serializer('completed_amount', 'total_amount')
    def serialize_amount(self, amount: Decimal) -> str:
        return str(amount)


class TaskOrderStatsResponse(BaseModel):
    """任务订单统计响应"""
    task_id: str = Field(..., description="任务ID")
    order_count: int = Field(..., description="订单总数")
    submitted_orders: int = Field(..., description="已提交订单数")
    processing_orders: int = Field(..., description="处理中订单数")
    completed_orders: int = Field(..., description="预定完成订单数")
    failed_orders: int = Field(..., description="预定失败订单数")
    total_amount: Decimal = Field(..., description="订单总金额")
    total_people: int = Field(..., description="总人数")
    
    model_config = ConfigDict(from_attributes=True)

    @field_serializer('total_amount')
    def serialize_amount(self, amount: Decimal) -> str:
        return str(amount) 