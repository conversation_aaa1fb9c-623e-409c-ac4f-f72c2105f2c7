"""加密解密工具模块"""
import os
import base64
from typing import Optional
from cryptography.fernet import <PERSON><PERSON><PERSON>
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC


class EncryptionService:
    """加密解密服务类"""
    
    def __init__(self):
        self._fernet = None
    
    def _init_encryption_key(self):
        """初始化加密密钥"""
        if self._fernet is not None:
            return
            
        # 从环境变量获取密钥
        encryption_key = os.getenv('SYSTEM_SETTINGS_ENCRYPTION_KEY')
        if not encryption_key:
            # 提供默认值用于开发环境
            encryption_key = "default-encryption-key-for-development-only"
        
        # 使用PBKDF2生成密钥
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=b'system_settings_salt',  # 在生产环境中应该使用随机salt
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(encryption_key.encode()))
        self._fernet = Fernet(key)
    
    def encrypt(self, plaintext: str) -> str:
        """
        加密字符串
        
        Args:
            plaintext: 要加密的明文
            
        Returns:
            加密后的字符串（base64编码）
        """
        if not plaintext:
            return ""
        
        self._init_encryption_key()
        encrypted_bytes = self._fernet.encrypt(plaintext.encode('utf-8'))
        return base64.urlsafe_b64encode(encrypted_bytes).decode('utf-8')
    
    def decrypt(self, ciphertext: str) -> str:
        """
        解密字符串
        
        Args:
            ciphertext: 要解密的密文（base64编码）
            
        Returns:
            解密后的明文字符串
        """
        if not ciphertext:
            return ""
        
        self._init_encryption_key()
        try:
            encrypted_bytes = base64.urlsafe_b64decode(ciphertext.encode('utf-8'))
            decrypted_bytes = self._fernet.decrypt(encrypted_bytes)
            return decrypted_bytes.decode('utf-8')
        except Exception as e:
            raise ValueError(f"Failed to decrypt data: {str(e)}")


# 全局加密服务实例
encryption_service = EncryptionService() 