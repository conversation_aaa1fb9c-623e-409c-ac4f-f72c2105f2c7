import jwt
from datetime import datetime, timedelta, timezone
from passlib.context import <PERSON><PERSON><PERSON>ontext
from typing import Any, Dict, Optional

from src.core.config import settings

# Password hashing context using bcrypt
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# 使用正确的配置项名称
ALGORITHM = settings.jwt_algorithm
JWT_SECRET_KEY = settings.jwt_secret
ACCESS_TOKEN_EXPIRE_MINUTES = settings.access_token_expire_minutes

def create_access_token(subject: Any, expires_delta: Optional[timedelta] = None) -> str:
    """
    Generates a JWT access token.

    :param subject: The subject of the token (e.g., user ID).
    :param expires_delta: Optional timedelta for token expiration. Uses setting if None.
    :return: The encoded JWT token.
    """
    if expires_delta:
        expire = datetime.now(timezone.utc) + expires_delta
    else:
        expire = datetime.now(timezone.utc) + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)

    to_encode: Dict[str, Any] = {"exp": expire, "sub": str(subject)}
    encoded_jwt = jwt.encode(to_encode, JWT_SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def verify_token(token: str) -> Optional[str]:
    """
    Verifies the JWT token and returns the subject (user ID).

    :param token: The JWT token string.
    :return: The subject (user ID) if the token is valid, None otherwise.
    """
    try:
        payload = jwt.decode(token, JWT_SECRET_KEY, algorithms=[ALGORITHM])
        # Optionally add more checks here (e.g., token type, issuer)
        subject = payload.get("sub")
        return subject
    except jwt.ExpiredSignatureError:
        # Token has expired
        return None
    except jwt.InvalidTokenError:
        # Any other invalid token error
        return None

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """
    Verifies a plain password against a hashed password.

    :param plain_password: The plain text password.
    :param hashed_password: The hashed password from the database.
    :return: True if the passwords match, False otherwise.
    """
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    """
    Hashes a plain password using bcrypt.

    :param password: The plain text password.
    :return: The hashed password.
    """
    return pwd_context.hash(password) 