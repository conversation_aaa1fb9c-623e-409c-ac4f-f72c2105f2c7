import os
import sys
from pathlib import Path
from tempfile import gettempdir
from enum import Enum

from pydantic_settings import BaseSettings, SettingsConfigDict
from pydantic import field_validator
from yarl import URL
from typing import Union, Optional

# 确定项目根目录相对于此文件的位置
# Path(__file__).parent -> src/core
# Path(__file__).parent.parent -> src
# Path(__file__).parent.parent.parent -> project root
PROJECT_ROOT = Path(__file__).parent.parent.parent

def get_env_file() -> str:
    """
    根据命令行参数或环境变量确定要加载的环境文件
    优先级：命令行参数 > DTTRIP_ENV环境变量 > 默认.env
    """
    # 检查命令行参数
    for i, arg in enumerate(sys.argv):
        if arg == "--env" and i + 1 < len(sys.argv):
            env_name = sys.argv[i + 1]
            env_file = PROJECT_ROOT / f".{env_name}.env"
            if env_file.exists():
                print(f"使用命令行指定的环境文件: {env_file}")
                return str(env_file)
            else:
                print(f"警告：指定的环境文件不存在: {env_file}")
    
    # 检查DTTRIP_ENV环境变量
    env_name = os.getenv("DTTRIP_ENV")
    if env_name:
        env_file = PROJECT_ROOT / f".{env_name}.env"
        if env_file.exists():
            print(f"使用DTTRIP_ENV环境变量指定的环境文件: {env_file}")
            return str(env_file)
        else:
            print(f"警告：DTTRIP_ENV指定的环境文件不存在: {env_file}")
    
    # 默认使用.env文件
    default_env = PROJECT_ROOT / ".env"
    if default_env.exists():
        print(f"使用默认环境文件: {default_env}")
        return str(default_env)
    else:
        print("警告：没有找到任何环境文件")
        return ""

# 获取要使用的环境文件路径
ENV_FILE_PATH = get_env_file()

TEMP_DIR = Path(gettempdir())

class LogLevel(str, Enum):
    """日志级别枚举"""
    CRITICAL = "CRITICAL"
    ERROR = "ERROR"
    WARNING = "WARNING"
    INFO = "INFO"
    DEBUG = "DEBUG"

class Environment(str, Enum):
    """环境类型枚举"""
    DEVELOPMENT = "development"
    TESTING = "testing"
    STAGING = "staging"
    PRODUCTION = "production"
    # 支持简写形式
    PROD = "prod"

class Settings(BaseSettings):
    """
    应用程序设置。
    
    这些参数只从指定的.env文件加载，不会从系统环境变量加载。
    """

    model_config = SettingsConfigDict(
        env_file=ENV_FILE_PATH if ENV_FILE_PATH else None,
        env_file_encoding="utf-8",
        extra="ignore",
    )
    
    @classmethod
    def settings_customise_sources(
        cls,
        settings_cls,
        init_settings,
        env_settings,
        dotenv_settings,
        file_secret_settings,
    ):
        """
        自定义设置来源，只使用.env文件，忽略系统环境变量
        """
        return (
            init_settings,
            dotenv_settings,  # 只保留.env文件设置
            file_secret_settings,
        )

    # 应用程序设置
    host: str = "0.0.0.0"
    port: int = 8000
    # uvicorn 工作进程数量
    workers_count: int = 1
    # 启用 uvicorn 热重载
    reload: bool = False
    # 应用名称
    app_name: str = "Service Operation Server"
    # 应用版本
    version: str = "0.1.0"

    # 环境模式
    environment: Environment = Environment.DEVELOPMENT
    # 是否开启调试模式
    debug: bool = True

    # 日志设置
    log_level: LogLevel = LogLevel.INFO
    log_format: str = "<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>"

    # 数据库变量
    db_host: str = "database-mysql-qa.cdb.17usoft.com"
    db_port: int = 17782
    db_user: str = "TCSoapServer"
    db_password: str = "x37zhCbgKKWKlXJO3CrGVP2y4RcZVBYH"
    db_base: str = "TCSoapServer"
    db_echo: bool = False

    # JWT设置
    secret_key: str = "replace-with-secure-secret-key"
    jwt_secret: str = "replace-with-secure-jwt-secret"
    jwt_algorithm: str = "HS256"
    access_token_expire_minutes: int = 1440  # 1天
    
    # SSO设置
    sso_enabled: bool = True
    sso_domain: str = "http://tccommon.qas.17usoft.com"
    sso_client_id: str = "OpsFlow.local"
    sso_client_secret: str = "6d0863be86c8ce517bb4df62de36b30b"
    sso_return_url: str = "http://localhost:5173"
    sso_redirect_uri: str = "http://localhost:5173/auth/callback"

    # 系统设置加密密钥
    system_settings_encryption_key: str = "dttrip-system-settings-encryption-key-2024"
    
    # 文件上传设置
    upload_dir: Path = Path("uploads")
    max_file_size: int = 10 * 1024 * 1024  # 10MB
    allowed_image_types: list[str] = ["image/jpeg", "image/jpg", "image/png", "image/gif", "image/bmp", "image/webp"]
    allowed_archive_types: list[str] = ["application/zip", "application/x-rar-compressed", "application/x-zip-compressed"]
    
    # CORS由nginx代理统一处理，后端不再需要CORS配置

    # Kafka设置
    kafka_enabled: bool = True
    kafka_bootstrap_servers: str = "kafka.ops.17usoft.com:9092"
    kafka_topic: str = "dttrip_soap_topic_tasks"
    kafka_acks: str = "1"  # 1:确保leader收到消息；0:消息发到发送缓冲区即可；-1/all:确保leader收到消息并且同步给follower
    kafka_retries: int = 1
    kafka_linger_ms: int = 3  # 提高生产效率，尤其是在同步生产时
    kafka_batch_size: int = 64 * 1024  # 64KB
    kafka_buffer_memory: int = 64 * 1024 * 1024  # 64MB
    kafka_receive_buffer: int = 64 * 1024 * 1024  # 64MB
    kafka_compression_type: str = "snappy"  # 推荐使用snappy或lz4格式压缩
    
    # S3 OSS设置
    s3_endpoint_url: Optional[str] = None
    s3_region_name: str = "cn-north-1"
    s3_aws_access_key_id: Optional[str] = None
    s3_aws_secret_access_key: Optional[str] = None
    s3_bucket_name: str = "passport-recognition"
    s3_key_prefix: str = ""
    s3_use_ssl: bool = False
    s3_verify: bool = False

    # 护照识别配置
    passport_recognition_url: str = "http://127.0.0.1:5000/recognize"
    passport_recognition_timeout: int = 30  # 30秒

    @property
    def upload_path(self) -> Path:
        """
        获取上传文件的完整路径。
        
        :return: 上传目录的Path对象。
        """
        upload_path = PROJECT_ROOT / self.upload_dir
        upload_path.mkdir(exist_ok=True)  # 确保目录存在
        return upload_path

    @property
    def db_url(self) -> str:
        """
        从设置中组装数据库URL。
        
        :return: 数据库URL。
        """
        # 使用 Tortoise ORM 支持的 URL 格式
        return f"mysql://{self.db_user}:{self.db_password}@{self.db_host}:{self.db_port}/{self.db_base}"
        
    @property
    def db_url_async(self) -> URL:
        """
        从设置中组装 SQLAlchemy 格式的异步数据库URL。
        
        :return: 数据库URL。
        """
        return URL.build(
            scheme="mysql+aiomysql",
            host=self.db_host,
            port=self.db_port,
            user=self.db_user,
            password=self.db_password,
            path=f"/{self.db_base}",
        )

    # Redis设置 (如果需要)
    # @property
    # def redis_url(self) -> URL:
    #     """从设置中组装Redis URL"""
    #     path = ""
    #     if self.redis_base is not None:
    #         path = f"/{self.redis_base}"
    #     return URL.build(
    #         scheme="redis",
    #         host=self.redis_host,
    #         port=self.redis_port,
    #         user=self.redis_user,
    #         password=self.redis_password,
    #         path=path,
    #     )


settings = Settings()

# 确保上传目录存在
settings.upload_dir.mkdir(parents=True, exist_ok=True)
