"""
护照识别任务服务
处理护照识别的异步任务，包括任务调度和状态管理
"""

import asyncio
import traceback
import json
from typing import Dict, Any, List, Optional
from datetime import datetime
from loguru import logger
from pathlib import Path

from src.services.dify_service import dify_service
from src.services.file_service import file_service
from src.db.models import Passport


class PassportTaskService:
    def __init__(self):
        self.running_tasks: Dict[str, asyncio.Task] = {}
        self.task_queue: asyncio.Queue = asyncio.Queue()
        self.is_processing = False
    
    async def start_task_processor(self):
        """启动任务处理器"""
        if self.is_processing:
            return
        
        self.is_processing = True
        logger.info("护照识别任务处理器已启动")
        
        # 启动任务处理循环
        asyncio.create_task(self._process_task_queue())
    
    async def stop_task_processor(self):
        """停止任务处理器"""
        self.is_processing = False
        
        # 取消所有运行中的任务
        for task_id, task in self.running_tasks.items():
            if not task.done():
                task.cancel()
                logger.info(f"取消任务: {task_id}")
        
        self.running_tasks.clear()
        logger.info("护照识别任务处理器已停止")
    
    async def submit_recognition_task(self, task_id: str, user_id: str) -> bool:
        """
        提交护照识别任务
        
        Args:
            task_id: 任务ID
            user_id: 用户ID
            
        Returns:
            是否成功提交任务
        """
        try:
            # 检查任务是否已经在运行
            if task_id in self.running_tasks:
                logger.warning(f"任务 {task_id} 已在运行中")
                return False
            
            # 获取该任务下的所有护照记录
            passports = await Passport.filter(
                task_id=task_id,
                user_id=user_id,
                processing_status="pending"
            ).all()
            
            if not passports:
                logger.warning(f"任务 {task_id} 没有待处理的护照记录")
                return False
            
            # 提交任务到队列
            task_data = {
                "task_id": task_id,
                "user_id": user_id,
                "passport_ids": [p.id for p in passports]
            }
            
            await self.task_queue.put(task_data)
            logger.info(f"任务 {task_id} 已提交到队列，包含 {len(passports)} 个护照")
            
            return True
            
        except Exception as e:
            logger.error(f"提交识别任务失败: {e}")
            return False
    
    async def _process_task_queue(self):
        """处理任务队列"""
        while self.is_processing:
            try:
                # 从队列中获取任务（超时1秒）
                task_data = await asyncio.wait_for(
                    self.task_queue.get(), 
                    timeout=1.0
                )
                
                # 创建异步任务处理
                task = asyncio.create_task(
                    self._process_recognition_task(task_data)
                )
                
                # 记录运行中的任务
                task_id = task_data["task_id"]
                self.running_tasks[task_id] = task
                
                logger.info(f"开始处理任务: {task_id}")
                
            except asyncio.TimeoutError:
                # 队列为空，继续等待
                continue
            except Exception as e:
                logger.error(f"处理任务队列时出错: {e}")
                await asyncio.sleep(1)
    
    async def _process_recognition_task(self, task_data: Dict[str, Any]):
        """
        处理单个识别任务
        
        Args:
            task_data: 任务数据
        """
        task_id = task_data["task_id"]
        user_id = task_data["user_id"]
        passport_ids = task_data["passport_ids"]
        
        try:
            # 更新所有护照状态为处理中
            await Passport.filter(id__in=passport_ids).update(
                processing_status="processing"
            )
            
            # 逐个处理护照
            for passport_id in passport_ids:
                try:
                    await self._process_single_passport(passport_id, user_id)
                except Exception as e:
                    logger.error(f"处理护照 {passport_id} 失败: {e}")
                    # 更新失败状态
                    await Passport.filter(id=passport_id).update(
                        processing_status="failed",
                        additional_info={"error": str(e)}
                    )
            
            logger.info(f"任务 {task_id} 处理完成")
            
        except Exception as e:
            logger.error(f"处理任务 {task_id} 失败: {e}")
            # 更新所有护照状态为失败
            await Passport.filter(id__in=passport_ids).update(
                processing_status="failed",
                additional_info={"error": str(e)}
            )
        finally:
            # 从运行任务列表中移除
            if task_id in self.running_tasks:
                del self.running_tasks[task_id]
    
    async def _process_single_passport(self, passport_id: int, user_id: str):
        """
        处理单个护照识别
        
        Args:
            passport_id: 护照记录ID
            user_id: 用户ID
        """
        try:
            # 获取护照记录
            passport = await Passport.get_or_none(id=passport_id)
            if not passport:
                raise ValueError(f"护照记录 {passport_id} 不存在")
            
            # 获取本地文件路径
            if not passport.uploaded_image_url:
                raise ValueError("护照记录没有上传的图片")
            
            # 从URL构建本地文件路径
            # 处理多种可能的URL格式，包括历史遗留的错误格式
            url = passport.uploaded_image_url
            
            # 处理历史遗留的重复uploads路径
            if "/uploads/uploads/" in url:
                # 错误格式: /uploads/uploads/passport/2025/06/24/file.png
                relative_path = url.replace("/uploads/uploads/", "")
            elif url.startswith("/uploads/"):
                # URL格式: /uploads/passport/2025/06/24/file.png
                relative_path = url.replace("/uploads/", "")
            elif url.startswith("http"):
                # S3 URL格式: https://oss.qa.17usoft.com/soap-files/tmc.ai.soap.server/passport/2025/06/24/file.png
                # 从S3 URL中提取文件路径信息
                logger.info(f"🌐 检测到S3 URL: {url}")
                
                # 提取S3 URL中的文件路径部分
                # URL格式: https://oss.qa.17usoft.com/soap-files/tmc.ai.soap.server/passport/2025/06/24/uuid.ext
                try:
                    # 查找'/passport/'之后的路径部分
                    if "/passport/" in url:
                        # 提取 passport/2025/06/24/uuid.ext 部分
                        relative_path = url.split("/passport/", 1)[1]
                        relative_path = f"passport/{relative_path}"
                        logger.info(f"📁 从S3 URL提取的相对路径: {relative_path}")
                    else:
                        # 如果无法从S3 URL提取路径，尝试查找数据库中的Dify文件名
                        logger.warning(f"⚠️ 无法从S3 URL提取文件路径，尝试使用Dify文件名: {passport.dify_filename}")
                        if passport.dify_filename:
                            # 构造可能的相对路径
                            relative_path = f"passport/{passport.dify_filename}"
                        else:
                            raise ValueError(f"无法从S3 URL提取文件路径，且没有Dify文件名: {url}")
                except Exception as e:
                    logger.error(f"❌ 处理S3 URL失败: {e}")
                    raise ValueError(f"无法处理S3 URL格式: {url}")
            elif url.startswith("uploads/uploads/"):
                # 相对路径错误格式: uploads/uploads/passport/2025/06/24/file.png
                relative_path = url.replace("uploads/uploads/", "")
            elif url.startswith("uploads/"):
                # 相对路径格式但包含uploads前缀: uploads/passport/2025/06/24/file.png
                relative_path = url.replace("uploads/", "")
            else:
                # 纯相对路径格式: passport/2025/06/24/file.png
                relative_path = url
            
            local_file_path = file_service.get_absolute_path(relative_path)
            
            # 添加调试日志
            logger.info(f"🔍 路径处理调试信息:")
            logger.info(f"  - 原始URL: {passport.uploaded_image_url}")
            logger.info(f"  - 相对路径: {relative_path}")
            logger.info(f"  - 绝对路径: {local_file_path}")
            logger.info(f"  - 文件是否存在: {Path(local_file_path).exists()}")
            
            if not Path(local_file_path).exists():
                logger.error(f"❌ 本地文件不存在: {local_file_path}")
                
                # 如果本地文件不存在且是S3 URL，尝试从S3下载
                if url.startswith("http"):
                    logger.info(f"🌐 尝试从S3下载文件: {url}")
                    try:
                        from src.services.s3_service import s3_service
                        
                        # 确保本地目录存在
                        Path(local_file_path).parent.mkdir(parents=True, exist_ok=True)
                        
                        # 从S3下载文件
                        download_success = await s3_service.download_file(url, local_file_path)
                        
                        if download_success:
                            logger.info(f"✅ 从S3下载文件成功: {url} -> {local_file_path}")
                        else:
                            raise ValueError(f"从S3下载文件失败: {url}")
                    except Exception as e:
                        logger.error(f"❌ 从S3下载文件失败: {e}")
                        raise ValueError(f"本地文件不存在且S3下载失败: {local_file_path}")
                else:
                    raise ValueError(f"本地文件不存在: {local_file_path}")
            
            # 步骤1：上传文件到Dify
            logger.info(f"=== 📤 步骤1: 上传护照 {passport_id} 到Dify ===")
            logger.info(f"📁 本地文件路径: {local_file_path}")
            logger.info(f"👤 用户ID: {user_id}")
            
            upload_result = await dify_service.upload_file(local_file_path, user_id)
            
            if not upload_result:
                logger.error(f"❌ 护照 {passport_id} 上传到Dify失败")
                raise Exception("上传文件到Dify失败")
            
            logger.info(f"✅ 护照 {passport_id} 上传成功，文件ID: {upload_result.get('id')}")
            
            # 更新Dify文件信息
            logger.info(f"💾 更新护照 {passport_id} 的Dify文件信息")
            dify_info = {
                "dify_image_uuid": upload_result.get("id"),
                "dify_image_filename": upload_result.get("name"),
                "dify_filename": upload_result.get("name")
            }
            logger.info(f"📋 Dify文件信息: {dify_info}")
            
            await passport.update_from_dict(dify_info)
            await passport.save()
            logger.info(f"✅ 护照 {passport_id} Dify文件信息更新完成")
            
            # 步骤2：调用Dify进行识别
            logger.info(f"=== 🔍 步骤2: 开始识别护照 {passport_id} ===")
            logger.info(f"📁 Dify文件ID: {upload_result['id']}")
            logger.info(f"👤 用户ID: {user_id}")
            
            recognition_result = await dify_service.recognize_passport(
                upload_file_id=upload_result["id"],
                user_id=user_id
            )
            
            if not recognition_result:
                logger.error(f"❌ 护照 {passport_id} Dify识别失败")
                raise Exception("Dify识别失败")
            
            logger.info(f"✅ 护照 {passport_id} 识别成功，消息ID: {recognition_result.get('message_id')}")
            
            # 步骤3：解析识别结果
            logger.info(f"=== 🔄 步骤3: 解析护照 {passport_id} 识别结果 ===")
            parsed_data = await dify_service.parse_recognition_result(recognition_result)
            
            # 统计解析到的有效字段
            valid_fields = {k: v for k, v in parsed_data.items() if v is not None and v != "" and k != "additional_info"}
            logger.info(f"✅ 护照 {passport_id} 解析完成，提取到 {len(valid_fields)} 个有效字段")
            logger.info(f"📋 有效字段: {list(valid_fields.keys())}")
            
            # 步骤4：更新数据库记录
            logger.info(f"=== 💾 步骤4: 更新护照 {passport_id} 数据库记录 ===")
            update_data = {
                "processing_status": "completed",
                "additional_info": parsed_data.get("additional_info", {})
            }
            logger.info(f"📊 准备更新的基础数据: {update_data}")
            
            # 添加识别的字段
            field_list = [
                "certificate_type", "country_of_issue", "certificate_number", 
                "surname", "given_names", "nationality", "date_of_birth",
                "sex", "date_of_issue", "date_of_expiry",
                "passenger_type", "viz_mrz_consistency", "ssr_code",
                "mrz_line1", "mrz_line2"
            ]
            
            updated_fields = []
            for field in field_list:
                if parsed_data.get(field) is not None:
                    update_data[field] = parsed_data[field]
                    updated_fields.append(field)
            
            logger.info(f"📝 添加 {len(updated_fields)} 个识别字段: {updated_fields}")
            
            # 处理日期字段为字符串格式
            for date_field in ["date_of_birth", "date_of_issue", "date_of_expiry"]:
                if parsed_data.get(date_field):
                    try:
                        # 确保日期字段为字符串格式
                        date_value = parsed_data[date_field]
                        
                        # 如果是日期对象，转换为字符串
                        if hasattr(date_value, 'strftime'):
                            update_data[date_field] = date_value.strftime("%Y-%m-%d")
                        elif isinstance(date_value, str):
                            # 如果已经是字符串，直接使用
                            update_data[date_field] = date_value
                        else:
                            # 其他类型转换为字符串
                            update_data[date_field] = str(date_value)
                    except Exception as e:
                        logger.warning(f"处理日期字段 {date_field} 失败: {e}")
                        # 如果处理失败，设置为空字符串
                        update_data[date_field] = ""
            
            # 更新记录
            logger.info(f"💾 开始更新护照 {passport_id} 数据库记录")
            logger.info(f"📊 最终更新数据: {json.dumps(update_data, ensure_ascii=False, indent=2)}")
            
            await passport.update_from_dict(update_data)
            await passport.save()
            
            logger.info(f"✅ 护照 {passport_id} 识别和保存完成!")
            logger.info(f"🎉 护照 {passport_id} 处理流程全部完成!")
            
        except Exception as e:
            logger.error(f"💥 处理护照 {passport_id} 失败: {e}")
            logger.error(f"📁 出错的护照记录ID: {passport_id}")
            logger.error(f"👤 用户ID: {user_id}")
            logger.error(f"🔍 异常堆栈: {traceback.format_exc()}")
            raise
    
    async def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """
        获取任务状态
        
        Args:
            task_id: 任务ID
            
        Returns:
            任务状态信息
        """
        try:
            # 检查是否在运行中
            is_running = task_id in self.running_tasks
            
            # 获取任务的护照记录统计
            passports = await Passport.filter(task_id=task_id).all()
            
            status_counts = {
                "pending": 0,
                "processing": 0,
                "completed": 0,
                "failed": 0
            }
            
            for passport in passports:
                status = passport.processing_status
                if status in status_counts:
                    status_counts[status] += 1
            
            total_count = len(passports)
            progress = 0
            if total_count > 0:
                completed = status_counts["completed"] + status_counts["failed"]
                progress = (completed / total_count) * 100
            
            return {
                "task_id": task_id,
                "is_running": is_running,
                "total_files": total_count,
                "status_counts": status_counts,
                "progress": round(progress, 2)
            }
            
        except Exception as e:
            logger.error(f"获取任务状态失败: {e}")
            return {
                "task_id": task_id,
                "is_running": False,
                "error": str(e)
            }


# 创建全局实例
passport_task_service = PassportTaskService() 