"""
S3 OSS服务模块

提供图片文件上传到S3兼容的OSS服务的功能
"""

import os
import uuid
import io
from typing import Optional, <PERSON>ple
from pathlib import Path
from datetime import datetime

import boto3
from boto3.s3.transfer import TransferConfig, create_transfer_manager
from botocore.exceptions import ClientError, NoCredentialsError
from loguru import logger

from src.core.config import settings


class S3Service:
    """S3 OSS服务类"""
    
    def __init__(self):
        """初始化S3客户端"""
        self.client = None
        self.bucket_name = None
        self.key_prefix = None
        self._initialize_client()
    
    def _initialize_client(self) -> None:
        """初始化S3客户端"""
        try:
            # 从settings获取S3配置
            from src.core.config import settings
            
            endpoint_url = settings.s3_endpoint_url
            region_name = settings.s3_region_name
            aws_access_key_id = settings.s3_aws_access_key_id
            aws_secret_access_key = settings.s3_aws_secret_access_key
            self.bucket_name = settings.s3_bucket_name
            self.key_prefix = settings.s3_key_prefix
            use_ssl = settings.s3_use_ssl
            verify = settings.s3_verify
            
            if not all([endpoint_url, aws_access_key_id, aws_secret_access_key, self.bucket_name]):
                logger.warning("S3配置不完整，S3上传功能将被禁用")
                return
            
            # 创建S3客户端
            self.client = boto3.client(
                's3',
                endpoint_url=endpoint_url,
                region_name=region_name,
                use_ssl=use_ssl,
                verify=verify,
                aws_access_key_id=aws_access_key_id,
                aws_secret_access_key=aws_secret_access_key
            )
            
            logger.info(f"S3客户端初始化成功，endpoint: {endpoint_url}, bucket: {self.bucket_name}")
            
        except Exception as e:
            logger.error(f"S3客户端初始化失败: {e}")
            self.client = None
    
    def is_available(self) -> bool:
        """检查S3服务是否可用"""
        return self.client is not None and self.bucket_name is not None
    
    def _generate_s3_key(self, filename: str, folder: str = "passport") -> str:
        """生成S3对象键"""
        # 生成时间戳文件夹
        timestamp = datetime.now().strftime('%Y/%m/%d')
        
        # 生成唯一文件名
        file_ext = Path(filename).suffix
        unique_name = f"{uuid.uuid4().hex}{file_ext}"
        
        # 组合完整的S3键
        if self.key_prefix:
            s3_key = f"{self.key_prefix}/{folder}/{timestamp}/{unique_name}"
        else:
            s3_key = f"{folder}/{timestamp}/{unique_name}"
        
        return s3_key
    
    def _get_file_url(self, s3_key: str) -> str:
        """获取文件的访问URL"""
        from src.core.config import settings
        
        endpoint_url = settings.s3_endpoint_url
        # 移除协议前缀，使用HTTPS
        if endpoint_url.startswith('http://'):
            endpoint_url = endpoint_url.replace('http://', 'https://')
        elif not endpoint_url.startswith('https://'):
            endpoint_url = f"https://{endpoint_url}"
        
        return f"{endpoint_url}/{self.bucket_name}/{s3_key}"
    
    async def upload_file(self, file_path: str, folder: str = "passport") -> Optional[str]:
        """
        上传文件到S3
        
        Args:
            file_path: 本地文件路径
            folder: S3中的文件夹名称
            
        Returns:
            上传成功返回文件URL，失败返回None
        """
        if not self.is_available():
            logger.warning("S3服务不可用，跳过文件上传")
            return None
        
        try:
            file_path_obj = Path(file_path)
            if not file_path_obj.exists():
                logger.error(f"文件不存在: {file_path}")
                return None
            
            # 生成S3键
            s3_key = self._generate_s3_key(file_path_obj.name, folder)
            
            # 获取文件大小
            file_size = file_path_obj.stat().st_size
            
            # 判断是否使用分段上传（大于50MB）
            if file_size > 50 * 1024 * 1024:
                return await self._multipart_upload(file_path, s3_key)
            else:
                return await self._simple_upload(file_path, s3_key)
                
        except Exception as e:
            logger.error(f"上传文件到S3失败: {e}")
            return None
    
    async def _simple_upload(self, file_path: str, s3_key: str) -> Optional[str]:
        """简单上传（小文件）"""
        try:
            # 检测文件类型
            file_ext = Path(file_path).suffix.lower()
            content_type = self._get_content_type(file_ext)
            
            # 上传文件
            with open(file_path, 'rb') as file_data:
                self.client.put_object(
                    Bucket=self.bucket_name,
                    Key=s3_key,
                    Body=file_data,
                    ContentType=content_type
                )
            
            file_url = self._get_file_url(s3_key)
            logger.info(f"文件上传成功: {file_path} -> {file_url}")
            return file_url
            
        except ClientError as e:
            logger.error(f"S3上传失败: {e}")
            return None
    
    async def _multipart_upload(self, file_path: str, s3_key: str) -> Optional[str]:
        """分段上传（大文件）"""
        try:
            # 创建传输管理器
            config = TransferConfig(
                multipart_threshold=1024 * 25,  # 25MB
                max_concurrency=10,
                multipart_chunksize=1024 * 25,
                use_threads=True
            )
            
            transfer_manager = create_transfer_manager(client=self.client, config=config)
            
            # 检测文件类型
            file_ext = Path(file_path).suffix.lower()
            content_type = self._get_content_type(file_ext)
            
            # 执行上传
            with open(file_path, 'rb') as file_data:
                transfer_future = transfer_manager.upload(
                    fileobj=file_data,
                    bucket=self.bucket_name,
                    key=s3_key,
                    extra_args={'ContentType': content_type}
                )
                transfer_future.result()  # 等待上传完成
            
            file_url = self._get_file_url(s3_key)
            logger.info(f"大文件分段上传成功: {file_path} -> {file_url}")
            return file_url
            
        except ClientError as e:
            logger.error(f"S3分段上传失败: {e}")
            return None
    
    async def upload_file_data(self, file_data: bytes, filename: str, folder: str = "passport") -> Optional[str]:
        """
        上传文件数据到S3
        
        Args:
            file_data: 文件二进制数据
            filename: 文件名
            folder: S3中的文件夹名称
            
        Returns:
            上传成功返回文件URL，失败返回None
        """
        if not self.is_available():
            logger.warning("S3服务不可用，跳过文件上传")
            return None
        
        try:
            # 生成S3键
            s3_key = self._generate_s3_key(filename, folder)
            
            # 检测文件类型
            file_ext = Path(filename).suffix.lower()
            content_type = self._get_content_type(file_ext)
            
            # 判断是否使用分段上传（大于50MB）
            if len(file_data) > 50 * 1024 * 1024:
                # 分段上传
                config = TransferConfig(
                    multipart_threshold=1024 * 25,  # 25MB
                    max_concurrency=10,
                    multipart_chunksize=1024 * 25,
                    use_threads=True
                )
                
                transfer_manager = create_transfer_manager(client=self.client, config=config)
                
                upload_stream = io.BytesIO(file_data)
                transfer_future = transfer_manager.upload(
                    fileobj=upload_stream,
                    bucket=self.bucket_name,
                    key=s3_key,
                    extra_args={'ContentType': content_type}
                )
                transfer_future.result()  # 等待上传完成
            else:
                # 简单上传
                self.client.put_object(
                    Bucket=self.bucket_name,
                    Key=s3_key,
                    Body=file_data,
                    ContentLength=len(file_data),
                    ContentType=content_type
                )
            
            file_url = self._get_file_url(s3_key)
            logger.info(f"文件数据上传成功: {filename} -> {file_url}")
            return file_url
            
        except ClientError as e:
            logger.error(f"S3上传文件数据失败: {e}")
            return None
        except Exception as e:
            logger.error(f"上传文件数据到S3失败: {e}")
            return None
    
    def _get_content_type(self, file_ext: str) -> str:
        """根据文件扩展名获取Content-Type"""
        content_types = {
            '.jpg': 'image/jpeg',
            '.jpeg': 'image/jpeg',
            '.png': 'image/png',
            '.gif': 'image/gif',
            '.webp': 'image/webp',
            '.bmp': 'image/bmp',
            '.tiff': 'image/tiff',
            '.tif': 'image/tiff',
            '.pdf': 'application/pdf',
            '.zip': 'application/zip',
            '.rar': 'application/x-rar-compressed',
        }
        return content_types.get(file_ext, 'application/octet-stream')
    
    async def delete_file(self, file_url: str) -> bool:
        """
        删除S3中的文件
        
        Args:
            file_url: 文件URL
            
        Returns:
            删除成功返回True，失败返回False
        """
        if not self.is_available():
            logger.warning("S3服务不可用，跳过文件删除")
            return False
        
        try:
            # 从URL提取S3键
            s3_key = self._extract_s3_key_from_url(file_url)
            if not s3_key:
                logger.error(f"无法从URL提取S3键: {file_url}")
                return False
            
            # 删除文件
            self.client.delete_object(Bucket=self.bucket_name, Key=s3_key)
            logger.info(f"文件删除成功: {file_url}")
            return True
            
        except ClientError as e:
            logger.error(f"S3删除文件失败: {e}")
            return False
        except Exception as e:
            logger.error(f"删除S3文件失败: {e}")
            return False
    
    def _extract_s3_key_from_url(self, file_url: str) -> Optional[str]:
        """从文件URL提取S3键"""
        try:
            # URL格式: https://endpoint/bucket/key
            if f"/{self.bucket_name}/" in file_url:
                parts = file_url.split(f"/{self.bucket_name}/", 1)
                if len(parts) == 2:
                    return parts[1]
            return None
        except Exception:
            return None

    async def download_file(self, file_url: str, local_path: str) -> bool:
        """
        从S3下载文件到本地路径
        
        Args:
            file_url: S3文件URL
            local_path: 本地保存路径
            
        Returns:
            下载成功返回True，失败返回False
        """
        if not self.is_available():
            logger.warning("S3服务不可用，跳过文件下载")
            return False
        
        try:
            # 从URL提取S3键
            s3_key = self._extract_s3_key_from_url(file_url)
            if not s3_key:
                logger.error(f"无法从URL提取S3键: {file_url}")
                return False
            
            # 确保本地目录存在
            local_path_obj = Path(local_path)
            local_path_obj.parent.mkdir(parents=True, exist_ok=True)
            
            # 下载文件
            self.client.download_file(
                Bucket=self.bucket_name,
                Key=s3_key,
                Filename=str(local_path)
            )
            
            logger.info(f"文件下载成功: {file_url} -> {local_path}")
            return True
            
        except ClientError as e:
            error_code = e.response.get('Error', {}).get('Code', 'Unknown')
            if error_code == 'NoSuchKey':
                logger.error(f"S3文件不存在: {file_url}")
            else:
                logger.error(f"S3下载文件失败: {e}")
            return False
        except Exception as e:
            logger.error(f"下载S3文件失败: {e}")
            return False

    async def download_file_data(self, file_url: str) -> Optional[bytes]:
        """
        从S3下载文件数据到内存
        
        Args:
            file_url: S3文件URL
            
        Returns:
            文件数据，失败返回None
        """
        if not self.is_available():
            logger.warning("S3服务不可用，跳过文件下载")
            return None
        
        try:
            # 从URL提取S3键
            s3_key = self._extract_s3_key_from_url(file_url)
            if not s3_key:
                logger.error(f"无法从URL提取S3键: {file_url}")
                return None
            
            # 下载文件数据
            response = self.client.get_object(Bucket=self.bucket_name, Key=s3_key)
            file_data = response['Body'].read()
            
            logger.info(f"文件数据下载成功: {file_url}, 大小: {len(file_data)} 字节")
            return file_data
            
        except ClientError as e:
            error_code = e.response.get('Error', {}).get('Code', 'Unknown')
            if error_code == 'NoSuchKey':
                logger.error(f"S3文件不存在: {file_url}")
            else:
                logger.error(f"S3下载文件数据失败: {e}")
            return None
        except Exception as e:
            logger.error(f"下载S3文件数据失败: {e}")
            return None

    async def test_connection(self) -> Tuple[bool, str]:
        """
        测试S3连接
        
        Returns:
            (是否成功, 消息)
        """
        if not self.is_available():
            return False, "S3服务配置不完整"
        
        try:
            # 使用一个轻量级的测试方法：尝试上传并删除一个小测试文件
            test_key = f"test/connection-test-{uuid.uuid4().hex[:8]}.txt"
            test_content = "S3连接测试"
            
            # 尝试上传测试文件
            self.client.put_object(
                Bucket=self.bucket_name,
                Key=test_key,
                Body=test_content.encode('utf-8'),
                ContentType='text/plain'
            )
            
            # 验证文件存在
            self.client.head_object(Bucket=self.bucket_name, Key=test_key)
            
            # 删除测试文件
            self.client.delete_object(Bucket=self.bucket_name, Key=test_key)
            
            return True, f"S3连接测试成功，bucket: {self.bucket_name}"
            
        except NoCredentialsError:
            return False, "S3认证失败：缺少访问凭证"
        except ClientError as e:
            error_code = e.response['Error']['Code']
            if error_code == 'NoSuchBucket':
                return False, f"S3存储桶不存在: {self.bucket_name}"
            elif error_code == 'AccessDenied':
                return False, "S3访问被拒绝：权限不足"
            else:
                return False, f"S3连接失败: {e}"
        except Exception as e:
            return False, f"S3连接测试失败: {e}"


# 创建全局S3服务实例
s3_service = S3Service() 