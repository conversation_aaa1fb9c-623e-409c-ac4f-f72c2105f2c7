from tortoise import fields
import json

from .base import AbstractBaseModel

class Passport(AbstractBaseModel):
    """护照信息模型"""
    
    # 基础信息
    task_id = fields.CharField(max_length=100, description="任务ID")
    
    # 图片相关信息
    uploaded_image_url = fields.TextField(description="上传后护照图片地址")
    dify_image_url = fields.TextField(null=True, description="Dify图片地址")
    dify_image_uuid = fields.CharField(max_length=100, null=True, description="Dify图片UUID")
    dify_image_filename = fields.CharField(max_length=255, null=True, description="Dify图片文件名")
    dify_filename = fields.Char<PERSON>ield(max_length=255, null=True, description="Dify文件名")
    
    # 护照识别信息
    certificate_type = fields.CharField(max_length=50, null=True, description="证件类型")
    country_of_issue = fields.CharField(max_length=100, null=True, description="签发国")
    certificate_number = fields.Char<PERSON><PERSON>(max_length=50, null=True, description="证件号码")
    surname = fields.Char<PERSON><PERSON>(max_length=100, null=True, description="姓氏")
    given_names = fields.CharField(max_length=100, null=True, description="名字")
    nationality = fields.CharField(max_length=100, null=True, description="国籍")
    date_of_birth = fields.CharField(max_length=50, default="", description="出生日期")
    sex = fields.CharField(max_length=10, null=True, description="性别")
    date_of_issue = fields.CharField(max_length=50, default="", description="签发日期")
    date_of_expiry = fields.CharField(max_length=50, default="", description="有效期至")
    
    # 新增字段
    passenger_type = fields.CharField(max_length=20, null=True, description="旅客类型（成人/儿童）")
    viz_mrz_consistency = fields.CharField(max_length=255, null=True, description="VIZ与MRZ数据一致性检查结果")
    ssr_code = fields.CharField(max_length=500, null=True, description="SSR码")
    
    # MRZ信息
    mrz_line1 = fields.CharField(max_length=255, null=True, description="MRZ第一行")
    mrz_line2 = fields.CharField(max_length=255, null=True, description="MRZ第二行")
    
    # 额外信息（JSON格式）
    additional_info = fields.JSONField(null=True, description="额外信息")
    
    # 处理状态
    processing_status = fields.CharField(
        max_length=20, 
        default="pending", 
        description="处理状态: pending, processing, completed, failed"
    )
    
    # 关联用户表
    user = fields.ForeignKeyField(
        "models.User", 
        related_name="passports", 
        on_delete=fields.CASCADE,
        to_field="user_id",
        description="关联用户"
    )

    class Meta:
        table = "passports"
        ordering = ["-created_at"]

    def __str__(self) -> str:
        user_id = self.user.user_id if self.user else 'Unknown'
        return f"Passport {self.certificate_number or 'Unknown'} - {user_id}"
    
    def to_recognition_json(self) -> dict:
        """转换为护照识别信息的JSON格式"""
        return {
            "certificate_type": self.certificate_type,
            "country_of_issue": self.country_of_issue,
            "certificate_number": self.certificate_number,
            "surname": self.surname,
            "given_names": self.given_names,
            "nationality": self.nationality,
            "date_of_birth": self.date_of_birth,
            "sex": self.sex,
            "date_of_issue": self.date_of_issue,
            "date_of_expiry": self.date_of_expiry,
            "passenger_type": self.passenger_type,
            "viz_mrz_consistency": self.viz_mrz_consistency,
            "ssr_code": self.ssr_code,
            "mrz_line1": self.mrz_line1,
            "mrz_line2": self.mrz_line2,
            "additional_info": self.additional_info or {}
        }
    
    @classmethod
    async def create_from_recognition_data(cls, user_id: str, task_id: str, 
                                         uploaded_image_url: str, recognition_data: dict):
        """从识别数据创建护照记录"""
        from .user import User
        
        # 获取用户对象
        user = await User.filter(user_id=user_id).first()
        if not user:
            raise ValueError(f"User with user_id {user_id} not found")
        
        # 处理日期字段为字符串格式
        def format_date_string(date_value):
            if not date_value:
                return ""
            try:
                # 如果是字符串，直接返回
                if isinstance(date_value, str):
                    return date_value
                # 如果是日期对象，格式化为字符串
                elif hasattr(date_value, 'strftime'):
                    return date_value.strftime("%Y-%m-%d")
                else:
                    return str(date_value)
            except (ValueError, TypeError):
                return ""
        
        passport = await cls.create(
            user=user,
            task_id=task_id,
            uploaded_image_url=uploaded_image_url,
            # Dify相关字段提供默认值
            dify_image_uuid=recognition_data.get("dify_image_uuid", ""),
            dify_image_filename=recognition_data.get("dify_image_filename", ""),
            dify_filename=recognition_data.get("dify_filename", ""),
            # 护照识别信息 - 为所有必填字段提供默认值
            certificate_type=recognition_data.get("certificate_type", ""),
            country_of_issue=recognition_data.get("country_of_issue", ""),
            certificate_number=recognition_data.get("certificate_number", ""),
            surname=recognition_data.get("surname", ""),
            given_names=recognition_data.get("given_names", ""),
            nationality=recognition_data.get("nationality", ""),
            date_of_birth=format_date_string(recognition_data.get("date_of_birth")),
            sex=recognition_data.get("sex", ""),
            date_of_issue=format_date_string(recognition_data.get("date_of_issue")),
            date_of_expiry=format_date_string(recognition_data.get("date_of_expiry")),
            passenger_type=recognition_data.get("passenger_type", ""),
            viz_mrz_consistency=recognition_data.get("viz_mrz_consistency", ""),
            ssr_code=recognition_data.get("ssr_code", ""),
            mrz_line1=recognition_data.get("mrz_line1", ""),
            mrz_line2=recognition_data.get("mrz_line2", ""),
            additional_info=recognition_data.get("additional_info", {}),
            processing_status="completed"
        )
        return passport 