from tortoise import fields
from tortoise.contrib.pydantic import pydantic_model_creator

from .base import AbstractBaseModel

class HotelOrder(AbstractBaseModel):
    """酒店订单模型"""
    
    # 系统字段
    project_id = fields.BigIntField(description="项目ID")
    is_deleted = fields.BooleanField(default=False, description="是否删除")
    fail_reason = fields.TextField(null=True, description="失败原因")
    
    # 业务字段
    sequence_number = fields.IntField(description="序号")
    guest_full_name = fields.Char<PERSON><PERSON>(max_length=100, default="", description="入住人姓名")
    guest_surname = fields.Char<PERSON><PERSON>(max_length=50, default="", description="入住人姓")
    guest_given_name = fields.Char<PERSON><PERSON>(max_length=50, default="", description="入住人名")
    guest_nationality = fields.Char<PERSON>ield(max_length=50, default="", description="入住人国籍")
    guest_gender = fields.Char<PERSON><PERSON>(max_length=10, default="", description="入住人性别")
    guest_birth_date = fields.CharField(max_length=50, default="", description="入住人出生日期")
    guest_id_type = fields.Char<PERSON><PERSON>(max_length=30, default="", description="入住人证件类型")
    guest_id_number = fields.CharField(max_length=50, default="", description="入住人证件号码")
    guest_id_expiry_date = fields.CharField(max_length=50, default="", description="入住人证件有效期至")
    guest_mobile_country_code = fields.CharField(max_length=10, default="+86", description="入住人手机号国际区号")
    guest_mobile_phone = fields.CharField(max_length=20, default="", description="入住人手机号")
    guest_email = fields.CharField(max_length=100, default="", description="入住人邮箱")
    
    # 酒店预订信息
    destination = fields.CharField(max_length=100, default="", description="目的地")
    hotel_id = fields.CharField(max_length=50, default="", description="酒店ID")
    room_type = fields.CharField(max_length=50, default="", description="预订房型")
    room_count = fields.IntField(default=1, description="预订房间数量")
    policy_name = fields.CharField(max_length=100, default="", description="政策名称")
    include_breakfast = fields.CharField(max_length=10, default="否", description="是否含早")
    is_half_day_room = fields.CharField(max_length=10, default="否", description="是否为半日房")
    check_in_time = fields.CharField(max_length=50, default="", description="入住时间")
    check_out_time = fields.CharField(max_length=50, default="", description="离店时间")
    is_group_booking = fields.CharField(max_length=10, default="否", description="是否为团房")
    group_booking_name = fields.CharField(max_length=100, default="", description="团房名称")
    room_number = fields.CharField(max_length=20, default="", description="房间号")
    
    # 支付和财务信息
    payment_method = fields.CharField(max_length=50, default="", description="支付方式")
    invoice_type = fields.CharField(max_length=30, default="", description="发票类型")
    tax_rate = fields.DecimalField(max_digits=5, decimal_places=4, default=0.0000, description="税率")
    agreement_type = fields.CharField(max_length=50, default="", description="协议类型")
    supplier_name = fields.CharField(max_length=100, default="", description="供应商名称")
    payment_channel = fields.CharField(max_length=50, default="", description="支付渠道")
    payment_transaction_id = fields.CharField(max_length=100, default="", description="支付流水号")
    cost_per_room = fields.DecimalField(max_digits=10, decimal_places=2, default=0.00, description="对供成本（每间房成本）")
    hidden_service_fee = fields.DecimalField(max_digits=10, decimal_places=2, default=0.00, description="隐藏手续费")
    cancellation_policy = fields.TextField(null=True, description="取消规则")
    is_violation = fields.CharField(max_length=10, default="否", description="是否违规")
    
    # 联系人和管理信息
    contact_person = fields.CharField(max_length=50, default="", description="联系人姓名")
    contact_mobile_country_code = fields.CharField(max_length=10, default="+86", description="联系人手机号国际区号")
    contact_mobile_phone = fields.CharField(max_length=20, default="", description="联系人手机号")
    order_remarks = fields.TextField(null=True, description="订单备注")
    cost_center = fields.CharField(max_length=100, default="", description="成本中心")
    trip_submission_item = fields.TextField(null=True, description="行程提交项")
    approver = fields.CharField(max_length=50, default="", description="审批人")
    
    # 同住人信息
    roommate_name = fields.CharField(max_length=100, default="", description="同住人姓名")
    roommate_surname = fields.CharField(max_length=50, default="", description="同住人姓")
    roommate_given_name = fields.CharField(max_length=50, default="", description="同住人名")
    roommate_nationality = fields.CharField(max_length=50, default="", description="同住人国籍")
    roommate_gender = fields.CharField(max_length=10, default="", description="同住人性别")
    roommate_birth_date = fields.CharField(max_length=50, default="", description="同住人出生日期")
    roommate_id_type = fields.CharField(max_length=30, default="", description="同住人证件类型")
    roommate_id_number = fields.CharField(max_length=50, default="", description="同住人证件号码")
    roommate_id_expiry_date = fields.CharField(max_length=50, default="", description="同住人证件有效期至")
    roommate_mobile_country_code = fields.CharField(max_length=10, default="+86", description="同住人手机号国际区号")
    roommate_mobile_phone = fields.CharField(max_length=20, default="", description="同住人手机号")
    roommate_email = fields.CharField(max_length=100, default="", description="同住人邮箱")
    
    # 对账单信息
    company_name = fields.CharField(max_length=100, default="", description="公司名称")
    hotel_name = fields.CharField(max_length=100, default="", description="酒店名称")
    amount = fields.DecimalField(max_digits=10, decimal_places=2, default=0.00, description="对客金额")
    booking_agent = fields.CharField(max_length=50, default="", description="代订人")
    order_number = fields.CharField(max_length=100, default="", description="订单号")
    bill_number = fields.CharField(max_length=100, default="", description="账单号")
    
    # 订单状态管理
    order_status = fields.CharField(
        max_length=20, 
        default="initial", 
        description="订单状态：initial=待提交, submitted=已提交, processing=处理中, completed=预定完成, failed=预定失败, check_failed=验证失败"
    )
    
    # 时间戳字段
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")

    class Meta:
        table = "hotel_orders"
        table_description = "酒店预订订单表"
        indexes = [
            "project_id",
            "order_status",
            "guest_id_number",
            "hotel_id",
            "check_in_time",
            "created_at",
            "is_deleted"
        ]

    def __str__(self):
        return f"HotelOrder(id={self.id}, guest_name={self.guest_full_name}, hotel_name={self.hotel_name})"

    async def soft_delete(self):
        """软删除订单"""
        self.is_deleted = True
        await self.save() 