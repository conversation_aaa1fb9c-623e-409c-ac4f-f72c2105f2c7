from tortoise import fields
from tortoise.contrib.pydantic import pydantic_model_creator

from .base import AbstractBaseModel

class User(AbstractBaseModel):
    """用户模型 - 表示系统中的用户信息"""
    username = fields.CharField(max_length=100, description="用户名")
    department = fields.CharField(max_length=100, default="", description="部门名称")
    user_id = fields.CharField(max_length=50, unique=True, description="用户唯一标识符")
    work_id = fields.CharField(max_length=50, default="", description="工号")
    new_work_id = fields.CharField(max_length=50, default="", description="新工号")
    department_id = fields.CharField(max_length=50, default="", description="部门ID")
    gender = fields.CharField(max_length=10, default="", description="性别")
    email = fields.CharField(max_length=255, default="", description="邮箱地址")
    dept_level_id = fields.Char<PERSON>ield(max_length=255, default="", description="部门级别ID")
    dept_level_name = fields.CharField(max_length=255, default="", description="部门级别名称")
    phone_number = fields.CharField(max_length=50, default="", description="电话号码")
    mtid = fields.CharField(max_length=50, default="", description="MT ID标识")
    ctids = fields.CharField(max_length=50, default="", description="CT IDs标识")
    gid = fields.CharField(max_length=50, default="", description="G ID标识")
    mobile = fields.CharField(max_length=50, default="", description="手机号码")
    member_id = fields.CharField(max_length=50, default="", description="会员ID")
    is_virtual = fields.IntField(null=True, description="是否虚拟用户（0否，1是）")
    tid = fields.CharField(max_length=50, default="", description="T ID标识")
    device_id = fields.CharField(max_length=100, default="", description="设备ID")

    # Relationship back to ApiKey (One-to-Many)
    # This is defined by the related_name="api_keys" in ApiKey model
    # 不需要显式定义反向关系，Tortoise ORM 会自动处理

    class Meta:
        table = "users"
        ordering = ["username"]

    def __str__(self) -> str:
        return self.username
