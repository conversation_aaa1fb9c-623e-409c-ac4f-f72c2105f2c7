[tool.poetry]
name = "fastapi-base-framework"
version = "0.1.0"
description = "基于 FastAPI 构建的后端 API 基础框架，遵循最佳实践"
authors = [
    "Framework Team <<EMAIL>>"
]
maintainers = [
    "Framework Maintainer <<EMAIL>>"
]
readme = "README.md"
packages = [{include = "src"}]

[tool.poetry.dependencies]
python = ">3.9.1,<4"
uvicorn = { version = "^0.34.0", extras = ["standard"] }
gunicorn = "^23.0.0"
pydantic = "^2.7.0"
pydantic-settings = "^2.7.0"
yarl = "^1.18.3"
ujson = "^5.10.0"
tortoise-orm = "^0.23.0"
aerich = "^0.8.0"
aiomysql = "^0.2.0"
pymysql = "^1.1.0"
cryptography = "^44.0.0"
redis = {version = "^5.2.1", extras = ["hiredis"]}
httptools = "^0.6.4"
pymongo = "^4.10.1"
loguru = "^0.7.3"
python-jose = "^3.3.0"     # 用于JWT处理
passlib = {version = "^1.7.4", extras = ["bcrypt"]}  # 密码哈希
typer = "^0.9.0"         # 用于CLI工具
rich = "^13.7.1"        # 用于CLI输出美化
psutil = "^7.0.0"
bcrypt = "4.0.1"
rarfile = "^4.2"
fastapi = "^0.115.6"
python-multipart = "^0.0.20"
httpx = "^0.27.0"  # 用于HTTP客户端请求
pandas = "^2.2.3"
openpyxl = "^3.1.5"
kafka-python = "^2.0.2"  # Kafka客户端库
python-snappy = "^0.7.3"
boto3 = "^1.35.0"  # AWS S3 SDK
requests = "^2.32.4"


[tool.poetry.group.dev.dependencies]
pytest = "^8"
ruff = "^0.5.0"
mypy = "^1.10.1"
pre-commit = "^3.7.1"
black = "^24.4.2"
pytest-cov = "^5"
anyio = "^4"
pytest-env = "^1.1.3"
fakeredis = "^2.23.3"
asynctest = "^0.13.0"
nest-asyncio = "^1.6.0"

[tool.isort]
profile = "black"
multi_line_output = 3
src_paths = ["src",]

[tool.mypy]
strict = true
ignore_missing_imports = true
allow_subclassing_any = true
allow_untyped_calls = true
pretty = true
show_error_codes = true
implicit_reexport = true
allow_untyped_decorators = true
warn_unused_ignores = false
warn_return_any = false
namespace_packages = true

# Remove this and add `types-redis`
# when the issue https://github.com/python/typeshed/issues/8242 is resolved.
[[tool.mypy.overrides]]
module = [
    'redis.asyncio'
]
ignore_missing_imports = true

[tool.pytest.ini_options]
filterwarnings = [
    "error",
    "ignore::DeprecationWarning",
    "ignore:.*unclosed.*:ResourceWarning",
]
env = [
    "APP_ENVIRONMENT=pytest",
    "APP_DB_BASE=app_test_db",
    "APP_DB_USER=root"
]
[tool.aerich]
tortoise_orm = "src.db.config.TORTOISE_CONFIG"
location = "./src/db/migrations"
src_folder = "./src"

[tool.ruff]
# List of enabled rulsets.
# See https://docs.astral.sh/ruff/rules/ for more information.
lint.select = [
    "E",   # Error
    "F",   # Pyflakes
    "W",   # Pycodestyle
    "C90", # McCabe complexity
    "I",   # Isort
    "N",   # pep8-naming
    "D",   # Pydocstyle
    "ANN", # Pytype annotations
    "S",   # Bandit
    "B",   # Bugbear
    "COM", # Commas
    "C4",  # Comprehensions
    "ISC", # Implicit string concat
    "PIE", # Unnecessary code
    "T20", # Catch prints
    "PYI", # validate pyi files
    "Q",   # Checks for quotes
    "RSE", # Checks raise statements
    "RET", # Checks return statements
    "SLF", # Self checks
    "SIM", # Simplificator
    "PTH", # Pathlib checks
    "ERA", # Checks for commented out code
    "PL",  # PyLint checks
    "RUF", # Specific to Ruff checks
]
lint.ignore = [
    "D105",    # Missing docstring in magic method
    "D107",    # Missing docstring in __init__
    "B008",    # Do not perform function calls in argument defaults
    "D211",    # No blank lines allowed before class docstring
    "D212",    # Multi-line docstring summary should start at the first line
    "D401",    # First line should be in imperative mood
    "D104",    # Missing docstring in public package
    "D100",    # Missing docstring in public module
    "D202",    # No blank lines allowed after function docstring
    "ANN102",  # Missing type annotation for self in method
    "ANN101",  # Missing type annotation for argument
    "ANN401",  # typing.Any are disallowed in `**kwargs
    "PLR0913", # Too many arguments for function call
    "D106",    # Missing docstring in public nested class
]
exclude = [
    "src/db/migrations",
    ".venv/"
]
lint.mccabe = { max-complexity = 10 }
line-length = 88

[tool.ruff.lint.per-file-ignores]
"tests/*" = [
    "S101", # Use of assert detected
]

[tool.ruff.lint.pydocstyle]
convention = "pep257"
ignore-decorators = ["typing.overload"]

[tool.ruff.lint.pylint]
allow-magic-value-types = ["int", "str", "float", "bytes"]


[fastapi-template.options]
project_name = "mice_open_tcai"
api_type = "rest"
enable_redis = "True"
enable_rmq = "None"
ci_type = "none"
enable_migrations = "True"
enable_taskiq = "None"
enable_kube = "None"
kube_name = "mice-open-tcai"
enable_routers = "True"
enable_kafka = "None"
enable_loguru = "True"
traefik_labels = "None"
add_dummy = "None"
orm = "tortoise"
self_hosted_swagger = "None"
prometheus_enabled = "None"
sentry_enabled = "None"
otlp_enabled = "None"
gunicorn = "True"
add_users = "None"
cookie_auth = "None"
jwt_auth = "None"

[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"
