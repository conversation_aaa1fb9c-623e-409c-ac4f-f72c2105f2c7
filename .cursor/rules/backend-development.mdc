---
description: 
globs: 
alwaysApply: false
---
# 后端API开发指南

## API端点开发模式

### 1. 端点定义
API端点在 [endpoints.py](mdc:service-operation-server/src/api/project/endpoints.py) 中定义，遵循以下模式：
- 使用FastAPI路由装饰器
- 包含详细的文档字符串
- 实现适当的错误处理
- 使用依赖注入获取当前用户

### 2. 数据模型
数据验证和序列化模型在 [schemas.py](mdc:service-operation-server/src/api/project/schemas.py) 中定义：
- `ProjectCreate` - 创建项目请求模型
- `ProjectUpdate` - 更新项目请求模型  
- `ProjectResponse` - 项目响应模型
- 使用Pydantic进行数据验证
- 实现字段序列化器处理特殊类型（如日期）

### 3. 数据库模型
数据库模型在 `src/db/models/` 目录中定义，使用Tortoise ORM：
- 继承自 `Model` 基类
- 定义字段类型和约束
- 实现模型关系和索引

## 开发最佳实践

### 错误处理
```python
try:
    # 业务逻辑
    result = await SomeModel.create(**data)
    return ResponseModel.model_validate(result)
except Exception as e:
    logger.error(f"操作失败: {str(e)}")
    raise HTTPException(status_code=400, detail=f"操作失败: {str(e)}")
```

### 用户认证
使用 `get_current_user` 依赖注入获取当前登录用户：
```python
async def some_endpoint(
    current_user: User = Depends(get_current_user)
):
    # 使用 current_user.user_id 和 current_user.username
```

### 日期处理
日期字段使用统一的序列化方式：
```python
@field_serializer('date_field')
def serialize_date(self, date_value: date) -> str:
    return date_value.isoformat()
```

## 常见问题解决

### 日期转换错误
如果出现日期转换错误，检查：
1. 数据库模型中的字段类型
2. Pydantic模型中的类型定义
3. 序列化器的实现

### 认证问题
如果出现认证相关错误，检查：
1. JWT token配置
2. 用户认证中间件
3. 依赖注入的实现
